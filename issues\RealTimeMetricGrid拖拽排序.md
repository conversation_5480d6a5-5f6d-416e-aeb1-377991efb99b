# RealTimeMetricGrid 拖拽排序功能实现

## 任务描述

为 RealTimeMetricGrid 组件添加卡片拖拽排序功能，支持用户自定义指标卡片的显示顺序，并将排序状态保存在浏览器内存中。

## 技术分析

- 项目已安装 `sortablejs` 依赖
- 已有 `dragableTable` 组件可作为参考实现
- `useRealTimeMetrics` composable 已有配置管理基础
- 当前使用 CSS Grid 布局显示 MetricCard 组件

## 实施方案

采用方案1：直接扩展当前组件

- 扩展 `useRealTimeMetrics` 添加排序状态管理
- 在 `RealTimeMetricGrid.vue` 中集成 Sortable.js
- 利用现有的 localStorage 配置机制

## 实施计划

### 1. 扩展 useRealTimeMetrics composable

- 添加 `metricOrder` ref 管理排序状态
- 添加 `setMetricOrder` 方法更新排序
- 扩展 localStorage 保存/读取排序配置
- 修改 `currentRealTimeMetrics` 计算属性按排序返回数据

### 2. 修改 RealTimeMetricGrid.vue 组件

- 导入 Sortable 库
- 在 `onMounted` 中初始化拖拽
- 添加拖拽事件处理逻辑
- 配置弹窗添加重置排序功能
- 添加拖拽相关的 CSS 样式

### 3. 样式和交互优化

- 添加拖拽状态的 CSS 类
- 设置拖拽时的视觉反馈
- 确保拖拽不影响现有交互

### 4. 测试验证

- 测试拖拽排序功能
- 验证配置保存和恢复
- 检查与现有功能的兼容性

## 预期效果

- 用户可通过拖拽调整指标卡片顺序
- 排序状态自动保存到浏览器 localStorage
- 提供流畅的拖拽视觉反馈
- 与现有配置功能无缝集成
