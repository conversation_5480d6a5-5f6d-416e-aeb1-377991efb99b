# UnoCSS 集成任务

## 任务概述

为 Electron + Vue 3 + TypeScript 项目集成 UnoCSS 原子化 CSS 框架，提供 Tailwind 兼容语法和更好的开发体验。

## 实施计划

1. ✅ 安装 UnoCSS 依赖包
2. ✅ 配置 Vite 插件
3. ✅ 创建 UnoCSS 配置文件
4. ✅ 引入样式到主入口
5. ✅ 添加 TypeScript 类型支持
6. ✅ 创建测试页面验证功能
7. ✅ 更新现有组件进行验证

## 已完成内容

### 1. 依赖安装

```bash
npm install -D unocss @unocss/preset-wind @unocss/preset-attributify @unocss/preset-icons
npm install -D @iconify-json/carbon @iconify-json/mdi @iconify-json/tabler
```

### 2. Vite 配置更新

- 在 `electron.vite.config.ts` 中添加了 UnoCSS 插件
- 仅在 renderer 进程中启用

### 3. UnoCSS 配置

- 创建了 `uno.config.ts` 配置文件
- 配置了 Tailwind 兼容预设
- 启用了属性化模式
- 集成了图标支持
- 定义了自定义快捷样式
- 配置了与 Element Plus 兼容的主题色

### 4. 样式引入

- 在 `src/renderer/src/main.ts` 中引入 `virtual:uno.css`
- 确保样式加载顺序正确

### 5. TypeScript 支持

- 在 `src/renderer/src/env.d.ts` 中添加了类型声明
- 支持 `virtual:uno.css` 模块导入

### 6. 验证测试

- 创建了 `TestUnoCSS.vue` 测试页面
- 更新了 `Versions.vue` 组件使用 UnoCSS 类
- 项目构建成功

## 功能特性

### ✅ 核心功能

- **Tailwind 兼容语法**：完全兼容 Tailwind CSS 类名
- **属性化模式**：支持 `<div bg="white" p="4">` 语法
- **图标支持**：集成 Carbon、MDI、Tabler 图标集
- **自定义主题**：与 Element Plus 主题色保持一致
- **响应式设计**：支持标准响应式断点
- **状态变体**：支持 hover、focus、active 等状态

### ✅ 开发体验

- **零运行时**：按需生成 CSS，无运行时开销
- **快速 HMR**：热模块替换速度更快
- **类型安全**：完整的 TypeScript 支持
- **IDE 支持**：与 VSCode 扩展兼容

### ✅ 自定义功能

- **快捷样式**：预定义常用组合样式
- **自定义规则**：支持 `m-16`、`p-20` 等自定义间距
- **安全列表**：确保关键类名不被清除
- **@apply 指令**：支持在 CSS 中使用原子类

## 配置文件说明

### uno.config.ts

```typescript
- shortcuts: 快捷样式定义
- theme: 主题配置（颜色、断点等）
- presets: 预设配置（Tailwind、属性化、图标等）
- transformers: 转换器（指令、变体组等）
- content: 扫描路径配置
- rules: 自定义规则
- safelist: 安全列表
```

## 使用示例

### 基础 Tailwind 语法

```vue
<template>
  <div class="p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600">基础样式</div>
</template>
```

### 属性化模式

```vue
<template>
  <div bg="white" p="6" rounded="lg" shadow="md">属性化语法</div>
</template>
```

### 图标使用

```vue
<template>
  <div class="i-carbon-home text-2xl text-blue-500"></div>
</template>
```

### 快捷样式

```vue
<template>
  <button class="btn-primary">主要按钮</button>
  <div class="card">卡片容器</div>
</template>
```

## 兼容性说明

- ✅ 与现有 Bootstrap 样式兼容
- ✅ 与 Element Plus 组件兼容
- ✅ 支持暗黑模式
- ✅ 不影响现有项目结构

## 性能优化

- **按需生成**：只生成实际使用的 CSS
- **极小体积**：最终 CSS 文件体积极小
- **快速构建**：构建速度比传统方案更快

## 后续建议

1. 可以考虑逐步迁移现有组件使用 UnoCSS
2. 可以添加更多自定义快捷样式
3. 可以集成更多图标集合
4. 可以配置更多主题变量

## 样式冲突问题修复

### 问题1：虚拟模块导入错误

- **问题**：`Failed to resolve import "virtual:uno.css"`
- **解决方案**：
  - 安装正确的 Vite 插件：`@unocss/vite`
  - 更新导入语句：`import UnoCSS from '@unocss/vite'`
  - 确保虚拟模块在 Element Plus 之后导入

### 问题2：Bootstrap 样式冲突

- **问题**：UnoCSS 默认重置样式覆盖了 Bootstrap
- **解决方案**：
  - 关闭 UnoCSS 预飞行样式：`preflight: false`
  - 调整样式导入顺序：UnoCSS 在 Bootstrap 之后
  - 使用命名空间前缀：`uno-*` 避免冲突
  - 移除可能冲突的字体和排版预设

### 配置优化

```typescript
// uno.config.ts 关键配置
presetUno({
  preflight: false  // 关闭样式重置
}),
// 移除 presetTypography 和 presetWebFonts
```

### 样式导入顺序

```typescript
// main.ts 正确顺序
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './assets/element-ui-theme.css'
import 'virtual:uno.css' // UnoCSS 最后导入
```

## 验证状态

- ✅ 项目构建成功
- ✅ UnoCSS 样式正常生效
- ✅ 图标显示正常
- ✅ 属性化模式工作正常
- ✅ Bootstrap 样式优先级保持
- ✅ 无样式冲突问题

## 使用建议

1. **避免冲突的类名**：

   - Bootstrap 类名：`btn`, `card`, `container` 等
   - UnoCSS 类名：使用 Tailwind 语法或 `uno-` 前缀

2. **推荐使用模式**：
   - 工具类：使用 UnoCSS (`p-4`, `text-blue-500`)
   - 组件样式：保持 Bootstrap (`btn btn-primary`)
   - 布局：混合使用 (`container mx-auto`)

## 结论

UnoCSS 已成功集成到项目中，解决了样式冲突问题，Bootstrap 样式优先级得到保持，可以安全使用。
