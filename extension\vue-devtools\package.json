{"name": "@vue/devtools-chrome-extension", "type": "module", "version": "7.7.6", "private": true, "author": "webfansplz", "license": "MIT", "files": ["dist"], "scripts": {"build": "cross-env NODE_ENV=production tsup", "dev": "cross-env NODE_ENV=development tsup --watch"}, "dependencies": {"@vue/devtools-core": "workspace:^", "@vue/devtools-kit": "workspace:^", "@vue/devtools-shared": "workspace:^"}, "devDependencies": {"@vitejs/plugin-vue": "catalog:", "vue": "catalog:"}}