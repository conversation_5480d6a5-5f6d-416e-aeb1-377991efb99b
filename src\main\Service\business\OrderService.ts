/**
 * 订单业务服务
 * 提供订单相关的所有业务处理逻辑
 */
import SqliteDB from '../../module/SqliteDB'
import { Order, Product } from '../../module/interfaces'
import { Op } from 'sequelize'
import { createProducts, getProductById } from './ProductService'

// ================================
// 订单创建服务
// ================================

/**
 * 从订单数组中提取唯一的商品信息
 * @param orders 订单数组
 * @returns 去重后的商品数组
 */
async function extractUniqueProducts(orders: Order[]): Promise<Product[]> {
  const productMap = new Map<string, Product>()

  for (const order of orders) {
    if (order.product_id && order.product_name) {
      // 检查商品是否已存在于数据库中
      const existingProduct = await getProductById(order.product_id)
      if (!existingProduct && !productMap.has(order.product_id)) {
        productMap.set(order.product_id, {
          product_id: order.product_id,
          product_name: order.product_name
        })
      }
    }
  }

  return Array.from(productMap.values())
}

/**
 * 批量创建订单
 * @param orders 订单数据数组
 * @returns 处理结果统计信息
 */
export async function createOrder(orders: Order[]): Promise<{
  totalOrders: number
  successCount: number
  failedOrders: Array<{ index: number; error: string; data: Order }>
  productResult?: {
    totalProducts: number
    successCount: number
    failedProducts: Array<{ index: number; error: string; data: Product }>
  }
}> {
  const result = {
    totalOrders: orders.length,
    successCount: 0,
    failedOrders: [] as Array<{ index: number; error: string; data: Order }>
  }

  console.log(`开始批量创建${orders.length}个订单...`)

  // 首先创建相关的商品
  console.log('开始提取并创建商品数据...')
  const uniqueProducts = await extractUniqueProducts(orders)
  let productResult

  if (uniqueProducts.length > 0) {
    console.log(`发现 ${uniqueProducts.length} 个新商品需要创建`)
    productResult = await createProducts(uniqueProducts)
    console.log(`商品创建完成：成功 ${productResult.successCount}/${productResult.totalProducts}`)
  } else {
    console.log('没有新商品需要创建')
  }

  // 分批处理，避免单个事务过大
  const BATCH_SIZE = 50
  const batches: Order[][] = []
  for (let i = 0; i < orders.length; i += BATCH_SIZE) {
    batches.push(orders.slice(i, i + BATCH_SIZE))
  }
  for (const batch of batches) {
    const transaction = await SqliteDB.getTransaction()

    try {
      for (let i = 0; i < batch.length; i++) {
        const orderData = batch[i]
        try {
          if (!orderData.order_id) throw new Error('订单ID不能为空')

          console.log(`处理订单 ${orderData.order_id}`)
          // 直接使用模型进行操作，而不是通过SqliteDB的业务方法
          const model = SqliteDB.getOrderModel()
          await model.upsert(orderData as any, { transaction })

          result.successCount++
        } catch (error: any) {
          result.failedOrders.push({
            index: i,
            error: error?.message || '创建订单失败',
            data: orderData
          })
          console.error(`订单失败 ${orderData}:`, error.message)
        }
      }

      await transaction.commit()
      console.log('该批次成功提交')
    } catch (batchError: any) {
      await transaction.rollback()
      console.error('该批次提交失败，已回滚:', batchError.message)
    }
  }

  console.log(
    `批量创建完成：成功 ${result.successCount}/${result.totalOrders}，失败 ${result.failedOrders.length} 个`
  )
  if (result.failedOrders.length > 0) {
    console.log('失败的订单:', result.failedOrders.map((item) => item.data.order_id).join(', '))
  }

  return {
    ...result,
    productResult
  }
}

// ================================
// 订单查询服务
// ================================

/**
 * 获取订单列表（支持分页）
 * @param args 查询参数
 * @returns 订单数据列表和分页信息
 */
export async function getOrderList(args?: {
  live_id?: string
  user_id?: string
  order_status?: string
  payment_status?: string
  start_time?: string
  end_time?: string
  productName?: string
  channelFilter?: string
  page?: number
  pageSize?: number
  limit?: number
  offset?: number
}): Promise<{
  data: Order[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}> {
  try {
    // 直接使用模型进行查询
    const model = SqliteDB.getOrderModel()

    const where: any = {}
    if (args) {
      if (args.live_id) where.live_id = args.live_id
      if (args.user_id) where.user_id = args.user_id
      if (args.order_status) where.order_status = args.order_status
      if (args.payment_status) where.payment_status = args.payment_status

      // 商品名称筛选
      if (args.productName && args.productName.trim()) {
        where.product_name = {
          [Op.like]: `%${args.productName.trim()}%`
        }
      }

      // 流量渠道筛选
      if (args.channelFilter) {
        where.media_type_group_name = args.channelFilter
      }

      // 时间范围筛选
      if (args.start_time && args.end_time) {
        where.pay_time = {
          [Op.between]: [args.start_time, args.end_time]
        }
      }
    }

    // 分页参数处理
    const page = Math.max(1, args?.page || 1)
    const pageSize = Math.min(100, Math.max(1, args?.pageSize || 10))
    let limit = pageSize
    let offset = (page - 1) * pageSize

    // 兼容旧的 limit/offset 参数
    if (args?.limit !== undefined) {
      limit = args.limit
    }
    if (args?.offset !== undefined) {
      offset = args.offset
    }

    // 获取总数
    const total = await model.count({
      where
    })

    // 获取分页数据
    const results = await model.findAll({
      where,
      order: [['pay_time', 'DESC']],
      limit,
      offset,
      benchmark: true,
      logging: false
    })

    const totalPages = Math.ceil(total / pageSize)

    return {
      data: results.map((item) => item.toJSON() as Order),
      total,
      page,
      pageSize,
      totalPages
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    return {
      data: [],
      total: 0,
      page: args?.page || 1,
      pageSize: args?.pageSize || 10,
      totalPages: 0
    }
  }
}

/**
 * 从订单表中提取商品数据并迁移到商品表
 * @returns 迁移处理结果
 */
export async function extractProductsFromOrdersToDatabase(): Promise<{
  totalOrdersScanned: number
  totalProductsExtracted: number
  productResult: {
    totalProducts: number
    successCount: number
    failedProducts: Array<{ index: number; error: string; data: Product }>
  }
}> {
  console.log('开始从订单表提取商品数据并迁移到商品表...')

  try {
    // 获取所有订单数据（不分页，获取全部数据用于商品提取）
    const orderResponse = await getOrderList({ pageSize: 999999 })
    const allOrders = orderResponse.data
    console.log(`扫描到 ${allOrders.length} 个订单`)

    // 提取唯一的商品信息
    const uniqueProducts = await extractUniqueProducts(allOrders)
    console.log('uniqueProducts', uniqueProducts)
    console.log(`提取到 ${uniqueProducts.length} 个唯一商品`)

    // 批量创建商品
    const productResult = await createProducts(uniqueProducts)

    console.log(`数据迁移完成：
      - 订单扫描数量: ${allOrders.length}
      - 提取商品数量: ${uniqueProducts.length}
      - 成功创建商品: ${productResult.successCount}
      - 失败商品数量: ${productResult.failedProducts.length}`)

    return {
      totalOrdersScanned: allOrders.length,
      totalProductsExtracted: uniqueProducts.length,
      productResult
    }
  } catch (error) {
    console.error('商品数据迁移失败:', error)
    throw error
  }
}

/**
 * 根据订单ID获取订单详情
 * @param orderId 订单ID
 * @returns 订单详情
 */
export async function getOrderById(orderId: string): Promise<Order | null> {
  try {
    if (!orderId) {
      console.error('订单ID不能为空')
      return null
    }

    const model = SqliteDB.getOrderModel()
    const result = await model.findByPk(orderId, {
      benchmark: true,
      logging: false
    })

    return result ? (result.toJSON() as Order) : null
  } catch (error) {
    console.error(`获取订单 ${orderId} 详情失败:`, error)
    return null
  }
}

// ================================
// 订单更新服务
// ================================

/**
 * 更新订单状态
 * @param orderId 订单ID
 * @param status 新状态
 * @returns 更新结果
 */
export async function updateOrderStatus(orderId: string, status: string): Promise<boolean> {
  try {
    if (!orderId) {
      console.error('订单ID不能为空')
      return false
    }

    const model = SqliteDB.getOrderModel()
    const [affectedCount] = await model.update(
      { order_status: status },
      { where: { order_id: orderId } }
    )

    if (affectedCount > 0) {
      console.log(`订单 ${orderId} 状态已更新为: ${status}`)
      return true
    } else {
      console.log(`订单 ${orderId} 不存在`)
      return false
    }
  } catch (error) {
    console.error(`更新订单 ${orderId} 状态失败:`, error)
    return false
  }
}

/**
 * 删除订单
 * @param orderId 订单ID
 * @returns 删除结果
 */
export async function deleteOrder(orderId: string): Promise<boolean> {
  try {
    if (!orderId) {
      console.error('订单ID不能为空')
      return false
    }

    const model = SqliteDB.getOrderModel()
    const deletedRows = await model.destroy({
      where: { order_id: orderId },
      benchmark: true,
      logging: false
    })

    if (deletedRows > 0) {
      console.log(`订单 ${orderId} 已删除`)
      return true
    } else {
      console.log(`订单 ${orderId} 不存在`)
      return false
    }
  } catch (error) {
    console.error(`删除订单 ${orderId} 失败:`, error)
    return false
  }
}

// ================================
// 订单统计服务
// ================================

/**
 * 订单统计指标接口
 */
export interface OrderMetricsData {
  totalPayAmount: number // 成交金额（分）
  totalRefundAmount: number // 退款金额（分）
  totalOrderCount: number // 订单数
  refundOrderCount: number // 退款订单数
}

/**
 * 获取订单统计指标（支持筛选条件）
 * @param filters 筛选条件
 * @returns 订单统计数据
 */
export async function getOrderMetrics(filters?: {
  productName?: string
  orderStatus?: string
  channelFilter?: string
  startTime?: number
  endTime?: number
}): Promise<OrderMetricsData> {
  try {
    const model = SqliteDB.getOrderModel()

    // 构建查询条件
    const whereCondition: any = {}

    // 商品名称筛选
    if (filters?.productName && filters.productName.trim()) {
      whereCondition.product_name = {
        [Op.like]: `%${filters.productName.trim()}%`
      }
    }

    // 订单状态筛选
    if (filters?.orderStatus) {
      whereCondition.order_status = filters.orderStatus
    }

    // 流量渠道筛选
    if (filters?.channelFilter) {
      whereCondition.media_type_group_name = filters.channelFilter
    }

    // 时间范围筛选
    if (filters?.startTime && filters?.endTime) {
      whereCondition.pay_time = {
        [Op.between]: [filters.startTime, filters.endTime]
      }
    }

    // 获取所有符合条件的订单
    const orders = await model.findAll({
      where: Object.keys(whereCondition).length > 0 ? whereCondition : undefined,
      attributes: ['order_status', 'total_pay_amount'], // 只查询需要的字段，提高性能
      benchmark: true,
      logging: false
    })

    const orderData = orders.map((item) => item.toJSON())

    // 计算统计指标
    const totalOrderCount = orderData.length

    // 退款订单
    const refundOrders = orderData.filter((order: any) => order.order_status === 'REFUND')
    const refundOrderCount = refundOrders.length

    // 成交金额：所有订单的金额总和
    const totalPayAmount = orderData.reduce(
      (sum, order: any) => sum + (order.total_pay_amount || 0),
      0
    )

    // 退款金额：退款订单的金额总和
    const totalRefundAmount = refundOrders.reduce(
      (sum, order: any) => sum + (order.total_pay_amount || 0),
      0
    )

    return {
      totalPayAmount,
      totalRefundAmount,
      totalOrderCount,
      refundOrderCount
    }
  } catch (error) {
    console.error('获取订单统计指标失败:', error)
    return {
      totalPayAmount: 0,
      totalRefundAmount: 0,
      totalOrderCount: 0,
      refundOrderCount: 0
    }
  }
}
