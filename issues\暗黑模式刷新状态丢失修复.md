# 暗黑模式刷新状态丢失修复

## 问题描述

- 在首页设置暗黑模式后刷新页面，暗黑模式正常保持
- 在data页、详情页、report页面设置暗黑模式后刷新，会变回白色模式
- 状态保持不一致

## 问题根因

1. **路由结构差异**：

   - 首页使用 `MainLayout` 布局，包含 `Header` 组件
   - data、detail、report 页面是独立页面，不使用布局组件

2. **暗黑模式逻辑缺失**：
   - `Header.vue` 中有暗黑模式状态管理逻辑 (`useDark`)
   - 独立页面缺少状态恢复机制
   - 刷新时 localStorage 状态无法被读取和应用

## 解决方案

在 `App.vue` 中添加全局暗黑模式状态恢复逻辑

### 修改内容

1. **导入依赖**：

   ```typescript
   import { watch, onMounted } from 'vue'
   import { useDark } from '@vueuse/core'
   ```

2. **添加状态管理**：

   ```typescript
   const isDark = useDark({
     selector: 'html',
     valueDark: 'dark',
     valueLight: 'light'
   })
   ```

3. **同步 Bootstrap 主题**：

   ```typescript
   watch(
     isDark,
     (val) => {
       document.documentElement.setAttribute('data-bs-theme', val ? 'dark' : 'light')
     },
     { immediate: true }
   )
   ```

4. **初始化设置**：
   ```typescript
   onMounted(() => {
     document.documentElement.setAttribute('data-bs-theme', isDark.value ? 'dark' : 'light')
   })
   ```

## 修改文件

- `src/renderer/src/App.vue`

## 预期效果

- 所有页面刷新后都能正确恢复暗黑模式状态
- 与 Header 组件的切换逻辑保持一致
- 不影响现有功能

## 测试验证

需要测试以下场景：

1. 首页暗黑模式切换和刷新
2. data 页面暗黑模式刷新保持
3. detail 页面暗黑模式刷新保持
4. report 页面暗黑模式刷新保持
