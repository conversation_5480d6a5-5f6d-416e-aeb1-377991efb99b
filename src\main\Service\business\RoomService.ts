/**
 * 房间业务服务
 * 提供房间相关的所有业务处理逻辑
 */
import moment from 'moment'
import SqliteDB from '../../module/SqliteDB'
import { RoomCoreData } from '../../module/interfaces'
import { getRoomCoreDataByIdFromFile, calculateAccountMetrics } from '../utils'
import { Op } from 'sequelize'
import * as fs from 'fs'
import * as path from 'path'

// ================================
// 房间数据插入服务
// ================================

export async function createRoomDataByUrl(data: {
  live_room_id: string
  start_time: string
  end_time: string
  live_duration: string
}): Promise<boolean> {
  try {
    const model = SqliteDB.getRoomCoreDataModel()
    await model.upsert({
      live_id: data.live_room_id,
      start_time: data.start_time,
      start_time_ts: moment(data.start_time).unix(),
      end_time: data.end_time,
      end_time_ts: moment(data.end_time).unix(),
      live_duration: data.live_duration
    })

    return true
  } catch (error) {
    console.error(`创建房间数据失败:`, error)
    return false
  }
}

/**
 * 通过ID插入数据库(读取room文件)
 * @param id 房间ID
 * @returns 操作结果：true-成功，false-失败
 */
export async function insertRoomDataById(id: string): Promise<boolean> {
  try {
    // 使用工具函数获取转换后的房间核心数据
    const roomCoreData = await getRoomCoreDataByIdFromFile(id)
    if (!roomCoreData) {
      console.warn(`无法获取房间 ${id} 的数据`)
      return false
    }

    // 日志记录
    console.log('处理房间数据:', {
      live_id: roomCoreData.live_id,
      zbtj: roomCoreData.zbtj,
      ffll: roomCoreData.ffll,
      live_duration: roomCoreData.live_duration,
      live_room: roomCoreData.live_room,
      start_time: roomCoreData.start_time
    })

    // 插入数据库 - 直接使用模型进行upsert操作
    const model = SqliteDB.getRoomCoreDataModel()
    await model.upsert(roomCoreData as any)

    return true
  } catch (error) {
    console.error(`处理房间 ${id} 失败:`, error)
    return false
  }
}

/**
 * 批量通过ID数组插入数据库(读取room文件)
 * @param ids 房间ID数组
 * @returns 处理结果统计信息
 */
export async function batchInsertRoomDataByIds(ids: string[]): Promise<{
  totalRooms: number
  successCount: number
  failedRooms: string[]
}> {
  const result = {
    totalRooms: ids.length,
    successCount: 0,
    failedRooms: [] as string[]
  }

  console.log(`开始批量处理${ids.length}个直播间数据...`)

  // 分批处理，避免单个事务过大
  const BATCH_SIZE = 50
  const batches: string[][] = []
  for (let i = 0; i < ids.length; i += BATCH_SIZE) {
    batches.push(ids.slice(i, i + BATCH_SIZE))
  }

  for (const batch of batches) {
    const batchPromises = batch.map(async (liveId: string) => {
      try {
        // 使用工具函数获取转换后的房间核心数据
        const roomCoreData = await getRoomCoreDataByIdFromFile(liveId)

        if (!roomCoreData) {
          console.warn(`无法获取房间 ${liveId} 的数据`)
          result.failedRooms.push(liveId)
          return
        }

        console.log(`批量处理房间 ${liveId} 数据:`, roomCoreData)

        // 插入数据库 - 直接使用模型进行upsert操作
        const model = SqliteDB.getRoomCoreDataModel()
        await model.upsert(roomCoreData as any)

        result.successCount++
        console.log(`成功处理直播间 ${liveId}`)
      } catch (error: any) {
        result.failedRooms.push(liveId)
        console.error(`处理直播间 ${liveId} 失败:`, error)
      }
    })

    // 等待当前批次完成
    await Promise.all(batchPromises)
  }

  console.log(
    `批量处理完成：成功 ${result.successCount}/${result.totalRooms}，失败 ${result.failedRooms.length} 个`
  )
  if (result.failedRooms.length > 0) {
    console.log('失败的直播间ID:', result.failedRooms.join(', '))
  }

  return result
}

// ================================
// 房间列表查询服务
// ================================

/**
 * 获取房间列表
 * @param args 查询参数
 * @returns 房间数据列表
 */
export async function getRoomList(args?: {
  startTime?: string
  endTime?: string
}): Promise<RoomCoreData[]> {
  try {
    const model = SqliteDB.getRoomCoreDataModel()
    let results

    if (args && args.startTime && args.endTime) {
      // 使用时间范围查询
      results = await model.findAll({
        where: {
          start_time: {
            [Op.between]: [args.startTime, args.endTime]
          }
        },
        order: [['start_time_ts', 'DESC']],
        benchmark: true,
        logging: false
      })
    } else {
      // 获取所有数据
      results = await model.findAll({
        order: [['start_time_ts', 'DESC']],
        benchmark: true,
        logging: false
      })
    }
    return results.map((item) => item.toJSON() as RoomCoreData)
  } catch (error) {
    console.error(error)
    return []
  }
}

/**
 * 获取房间列表数据
 * @param args 查询参数
 * @returns 房间列表数据和结果状态
 */
export async function getRoomListData(args?: { startTime?: string; endTime?: string }): Promise<{
  success: boolean
  data: RoomCoreData[]
  total: number
  error?: string
}> {
  try {
    // 使用前面重构的getRoomList方法
    const roomList = await getRoomList(args)

    return {
      success: true,
      data: roomList,
      total: roomList.length
    }
  } catch (error: any) {
    console.error('获取房间列表失败:', error)
    return {
      success: false,
      data: [],
      total: 0,
      error: error?.message || '获取房间列表失败'
    }
  }
}

/**
 * 获取所有房间核心数据（系统级方法）
 * @returns 所有房间数据列表
 */
export async function getAllRoomCoreData(): Promise<RoomCoreData[]> {
  try {
    const model = SqliteDB.getRoomCoreDataModel()
    const results = await model.findAll({
      order: [['start_time_ts', 'DESC']],
      benchmark: true,
      logging: false
    })

    return results.map((item) => item.toJSON() as RoomCoreData)
  } catch (error: any) {
    console.error('获取所有房间核心数据失败:', error)
    throw new Error(`获取所有核心数据失败: ${error?.message || error}`)
  }
}

/**
 * 单个房间数据upsert操作（系统级方法）
 * @param roomCoreData 房间核心数据
 * @returns 操作结果
 */
export async function upsertRoomCoreData(roomCoreData: RoomCoreData): Promise<boolean> {
  try {
    if (!roomCoreData.live_id) {
      throw new Error('无效的live_id：live_id不能为空')
    }

    const model = SqliteDB.getRoomCoreDataModel()
    await model.upsert(roomCoreData as any)

    console.log(`房间数据upsert成功: ${roomCoreData.live_id}`)
    return true
  } catch (error: any) {
    console.error('房间数据upsert失败:', error)
    throw new Error(`数据upsert操作失败: ${error?.message || error}`)
  }
}

// ================================
// 账户指标服务
// ================================

/**
 * 获取账户指标数据
 * @param days 天数，默认30天
 * @returns 当月和上月的指标数据
 */
export async function getAccountMetrics(days: number = 30): Promise<{
  currentMonth: any
  previousMonth: any
}> {
  // 生成当月时间范围（近days天）
  const currentMonthEndDate = moment().format('YYYY/MM/DD 23:59')
  const currentMonthStartDate = moment().subtract(days, 'days').format('YYYY/MM/DD 00:00')

  // 生成上月时间范围（前一个相同天数的时间段）
  const previousMonthEndDate = moment()
    .subtract(days + 1, 'days')
    .format('YYYY/MM/DD 23:59')
  const previousMonthStartDate = moment()
    .subtract(days * 2, 'days')
    .format('YYYY/MM/DD 00:00')

  // 获取当月数据
  const model = SqliteDB.getRoomCoreDataModel()
  const currentMonthResults = await model.findAll({
    where: {
      start_time: {
        [Op.between]: [currentMonthStartDate, currentMonthEndDate]
      }
    },
    benchmark: true,
    logging: false
  })
  const currentMonthData = currentMonthResults.map((item) => item.toJSON() as RoomCoreData)

  // 获取上月数据
  const previousMonthResults = await model.findAll({
    where: {
      start_time: {
        [Op.between]: [previousMonthStartDate, previousMonthEndDate]
      }
    },
    benchmark: true,
    logging: false
  })
  const previousMonthData = previousMonthResults.map((item) => item.toJSON() as RoomCoreData)

  // 计算当月指标
  const currentMetrics = calculateAccountMetrics(currentMonthData)

  // 计算上月指标
  const previousMetrics = calculateAccountMetrics(previousMonthData)

  return {
    currentMonth: currentMetrics,
    previousMonth: previousMetrics
  }
}

// ================================
// 字幕编辑数据服务
// ================================

/**
 * 字幕编辑数据接口类型定义
 */
export interface SubtitleEditData {
  timeLabel: string // 时间标签
  relativeTime: string // 相对时间
  editedText: string // 编辑后的文本
  note: string // 备注
  timestamp: number // 时间戳
}

/**
 * 保存字幕编辑数据
 * @param roomId 房间ID
 * @param editData 编辑数据数组
 * @returns 保存结果
 */
export async function saveSubtitleEdits(
  roomId: string,
  editData: SubtitleEditData[]
): Promise<{
  success: boolean
  error?: string
}> {
  try {
    if (!roomId || !editData) {
      return {
        success: false,
        error: '房间ID和编辑数据不能为空'
      }
    }

    // 确保cache/segs文件夹存在
    const cacheDir = path.join(process.cwd(), 'cache')
    const segsDir = path.join(cacheDir, 'segs')

    if (!fs.existsSync(segsDir)) {
      fs.mkdirSync(segsDir, { recursive: true })
    }

    // 保存数据到JSON文件
    const filePath = path.join(segsDir, `seg_${roomId}.json`)
    const saveData = {
      roomId,
      updateTime: new Date().toISOString(),
      editData
    }

    fs.writeFileSync(filePath, JSON.stringify(saveData, null, 2), 'utf8')

    console.log(`字幕编辑数据保存成功: ${filePath}`)
    return { success: true }
  } catch (error: any) {
    console.error('保存字幕编辑数据失败:', error)
    return {
      success: false,
      error: error?.message || '保存失败'
    }
  }
}

/**
 * 读取字幕编辑数据
 * @param roomId 房间ID
 * @returns 读取结果
 */
export async function loadSubtitleEdits(roomId: string): Promise<{
  success: boolean
  data?: SubtitleEditData[]
  error?: string
}> {
  try {
    if (!roomId) {
      return {
        success: false,
        error: '房间ID不能为空'
      }
    }

    const segsDir = path.join(process.cwd(), 'cache', 'segs')
    const filePath = path.join(segsDir, `seg_${roomId}.json`)

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.log(`字幕编辑数据文件不存在: ${filePath}`)
      return {
        success: true,
        data: [] // 返回空数组表示没有保存的数据
      }
    }

    // 读取文件内容
    const fileContent = fs.readFileSync(filePath, 'utf8')
    const jsonData = JSON.parse(fileContent)

    console.log(`字幕编辑数据读取成功: ${filePath}`)
    return {
      success: true,
      data: jsonData.editData || []
    }
  } catch (error: any) {
    console.error('读取字幕编辑数据失败:', error)
    return {
      success: false,
      error: error?.message || '读取失败'
    }
  }
}
