# ECharts图表重复图例和状态恢复问题修复

## 问题背景

在 `ProductCustomContent.vue` 组件中，用户反馈了ECharts图表的多个问题：

1. **重复图例按钮问题** - 刷新测试中出现了两次图例按钮
2. **本地存储状态恢复问题** - 全部取消选择后刷新页面，没有实现全部取消选择状态，还是选择了上一次最后一个选中的
3. **chartUtils.ts第789行错误** - 组件加载时报错 `TypeError: Cannot read properties of undefined (reading 'type')` at chartUtils.ts:789:23

## 问题分析

### 1. 重复图例按钮问题

**原因分析**：

- 有3个地方都会调用initChart()：timeFrame变化、roomData变化、onMounted
- 多个监听器同时触发时，会导致createChartLegend被多次调用
- 图例容器被多次创建，产生重复的图例按钮

**解决方案**：

- 添加 `isInitializing` 标志防止重复初始化
- 在initChart开始时设置标志，完成或出错时重置
- 每次initChart时清理图例容器
- 优化watch函数，只有当值真正改变时才重新初始化

### 2. 本地存储状态恢复问题

**原因分析**：

- createChartLegend函数在第282行有默认逻辑
- 当没有任何项被选中时，函数会自动选择默认项
- 即使用户明确取消了所有选择并存储到localStorage，刷新后系统仍会强制选中第一个项目

**解决方案**：

- 修改chartUtils.ts中的默认逻辑判断条件
- 添加 `hasStoredState` 检查
- 只有在没有存储状态的情况下才应用默认逻辑
- 如果有存储状态（即使全部为false），也应该尊重用户的选择

### 3. chartUtils.ts第789行错误

**原因分析**：

- handleChartResize函数中的chartInstance.resize()调用时
- chart实例可能已经被销毁或为undefined
- 缺少足够的类型和状态检查

**解决方案**：

- 增强chart实例检查，添加类型检查
- 检查 `isDisposed` 和 `resize` 函数是否存在
- 添加try-catch包装resize调用
- 提供友好的错误日志

## 修复详情

### 文件：`src/renderer/src/views/detail/trends/ProductCustomContent.vue`

#### 1. 添加初始化标志防止重复调用

```typescript
// 添加初始化标志防止重复调用
const isInitializing = ref<boolean>(false)

const initChart = (): void => {
  if (!chartContainer.value || !legendContainer.value) return

  // 防止重复初始化
  if (isInitializing.value) return
  isInitializing.value = true

  // ... 其他代码

  loading.value = false
  // 重置初始化标志
  isInitializing.value = false
}
```

#### 2. 清理图例容器防止重复创建

```typescript
// 清理图例容器，防止重复创建
if (legendContainer.value) {
  legendContainer.value.innerHTML = ''
}
```

#### 3. 优化watch函数避免重复调用

```typescript
// 监听时间范围变化，自动更新图表
watch(
  () => props.timeFrame,
  (newVal, oldVal) => {
    // 只有当值真正改变时才重新初始化
    if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
      initChart()
    }
  },
  { deep: true }
)

// 监听roomData变化，自动更新图表
watch(
  () => props.roomData,
  (newVal, oldVal) => {
    // 只有当值真正改变时才重新初始化
    if (newVal !== oldVal) {
      initChart()
    }
  },
  { deep: true }
)
```

#### 4. 修复类型错误

```typescript
// 使用类型断言修复chartInstance类型问题
createChartLegend(
  legendContainer.value,
  seriesNames,
  colorMap,
  chartInstance.value as echarts.ECharts
  // ... 其他参数
)

const resizeCleanup = handleChartResize(chartInstance.value as echarts.ECharts, {
  debounce: 200,
  immediate: true
})
```

### 文件：`src/renderer/src/utils/chartUtils.ts`

#### 1. 修复本地存储状态恢复逻辑

```typescript
// 修复全部取消选择状态恢复问题
// 如果没有任何项被选中且未指定特殊项，使用默认逻辑
const hasSelectedItems = Object.values(selectedMap).some((v) => v)
const hasStoredState = Object.keys(storedState).length > 0

// 只有在没有存储状态的情况下才应用默认逻辑
// 如果有存储状态（即使全部为false），也应该尊重用户的选择
if (
  !hasSelectedItems &&
  !hasStoredState &&
  (!defaultSelectSpecial || !specialItems || specialItems.length === 0) &&
  !hasExternalConfig
) {
  // 应用默认选择逻辑
}
```

#### 2. 增强chart实例检查防止resize错误

```typescript
// 增强chart实例检查，防止resize错误
if (Array.isArray(chartInstance)) {
  chartInstance.forEach((chart) => {
    if (
      chart &&
      typeof chart.isDisposed === 'function' &&
      !chart.isDisposed() &&
      typeof chart.resize === 'function'
    ) {
      try {
        chart.resize()
      } catch (err) {
        console.warn('Chart resize failed:', err)
      }
    }
  })
} else if (
  chartInstance &&
  typeof chartInstance.isDisposed === 'function' &&
  !chartInstance.isDisposed() &&
  typeof chartInstance.resize === 'function'
) {
  try {
    chartInstance.resize()
  } catch (err) {
    console.warn('Chart resize failed:', err)
  }
}
```

#### 3. 修复未使用变量的linter错误

```typescript
// 修复catch块中未使用的变量
} catch {
    return {}
}
```

## 修复效果

### 解决的问题

1. ✅ **重复图例按钮问题** - 通过添加初始化标志和清理图例容器解决
2. ✅ **本地存储状态恢复问题** - 通过修改默认逻辑判断条件，尊重用户的全部取消选择状态
3. ✅ **chartUtils.ts第789行错误** - 通过增强类型检查和异常处理解决
4. ✅ **类型错误** - 通过添加类型断言修复TypeScript类型问题
5. ✅ **性能优化** - 通过防重复调用和优化watch函数提升性能

### 技术改进

1. **更强的错误处理** - 添加了完善的try-catch和类型检查
2. **更好的状态管理** - 通过初始化标志防止竞态条件
3. **更准确的用户体验** - 尊重用户的选择状态，包括全部取消选择
4. **更好的性能** - 避免不必要的重复初始化和DOM操作

## 测试建议

1. **图例功能测试**：

   - 测试图例按钮点击隐藏/显示功能
   - 测试全部取消选择后的状态保持
   - 测试页面刷新后的状态恢复

2. **性能测试**：

   - 测试快速切换timeFrame时的响应性
   - 测试roomData变化时的图表更新
   - 测试窗口resize时的图表适应性

3. **边界情况测试**：
   - 测试无数据情况下的处理
   - 测试组件快速挂载/卸载的稳定性
   - 测试异常数据的错误处理

## 总结

此次修复主要解决了ECharts图表组件中的三个核心问题：重复图例创建、本地存储状态恢复和chart实例错误处理。通过添加防重复逻辑、修复默认状态判断条件和增强错误处理，显著提升了组件的稳定性和用户体验。

所有修复都遵循了项目的编码规范，使用了项目标准的工具函数，并保持了与其他组件的一致性。修复后的组件能够正确处理用户的图例选择状态，避免了重复创建和状态丢失的问题。
