import { DataTypes, Model, Sequelize } from 'sequelize'
import { Product as IProduct } from '../interfaces'

/**
 * 商品数据模型类
 * 继承自 Sequelize Model，提供类型安全的数据库操作
 */
export class ProductModel extends Model<IProduct> implements IProduct {
  declare id: number
  declare product_id: string
  declare product_name: string
  declare created_at?: string
  declare updated_at?: string;

  // 允许动态属性
  [key: string]: any
}

/**
 * 初始化商品模型
 * @param sequelize Sequelize 实例
 * @returns 初始化后的模型类
 */
export function initProductModel(
  sequelize: Sequelize,
  tableName: string = 'products'
): typeof ProductModel {
  ProductModel.init(
    {
      // ===== 基础信息 =====
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '自增主键ID'
      },
      product_id: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        comment: '商品ID'
      },
      product_name: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: '商品名称'
      },

      // ===== 时间戳 =====
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间'
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间'
      }
    },
    {
      sequelize,
      tableName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        { fields: ['product_id'], name: 'idx_products_product_id', unique: true },
        { fields: ['product_name'], name: 'idx_products_product_name' }
      ],
      comment: '商品数据表'
    }
  )

  return ProductModel
}
