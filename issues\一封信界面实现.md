# 一封信界面实现

## 功能描述

实现了一个完整的一封信弹窗系统，支持无法手动关闭的特殊弹窗，包含消息阅读和答题测验功能。

## 数据格式

```typescript
{
  message: string,      // 第一页显示的消息内容
  qa: [{               // 问答题目数组
    q: string,         // 问题文本
    c: string[],       // 选项数组
    a: number[]        // 正确答案索引数组 (从1开始)
  }]
}
```

## 实现文件

### 1. 类型定义

- `src/renderer/src/types/letterTypes.ts` - 一封信系统的TypeScript类型定义

### 2. 状态管理

- `src/renderer/src/composables/useLetter.ts` - 一封信状态管理

### 3. 前端组件

- `src/renderer/src/components/modals/LetterModal.vue` - 一封信弹窗组件

### 4. 后端服务

- `src/main/Service/LZYService.ts` - 添加了一封信发送功能

### 5. 应用集成

- `src/renderer/src/App.vue` - 集成一封信监听和显示

## 界面特性

### 第一页：消息阅读

- 显示一封信图标和标题
- 显示格式化的消息内容（支持换行）
- 阅读提醒区域
- "我已阅读完毕"按钮（必须点击才能继续）
- "开始答题"按钮（显示题目数量）

### 第二页：答题测验

- 显示当前题目进度
- 问题文本区域（带背景高亮）
- 复选框选项列表（支持多选）
- 答题提示信息
- 答题状态显示
- "下一题"/"提交答案"按钮

### 第三页：结果展示

- 圆形分数显示（带颜色等级）
- 总体成绩统计
- 详细答题结果列表
- 每题的正确答案对比
- 评价等级和建议
- "确认完成"按钮（完成后才能关闭）

## 技术特性

1. **无法手动关闭** - 使用 `backdrop: 'static', keyboard: false` 配置
2. **三页式流程** - 消息阅读 → 答题测验 → 结果展示
3. **多选题支持** - 支持单选和多选题目
4. **答案验证** - 严格验证用户选择与正确答案的匹配
5. **HTML文档支持** - message支持完整的HTML内容渲染（使用v-html）
6. **智能内容检测** - 自动检测HTML标签，纯文本自动转换换行符
7. **响应式设计** - 适配各种屏幕尺寸
8. **暗黑模式** - 完整的暗黑主题支持
9. **Bootstrap集成** - 使用Bootstrap Modal确保UI一致性

## 使用方法

### 后端发送一封信

```typescript
// 在任何地方调用LZYService的静态方法
LZYService.sendLetter({
  message:
    '亲爱的用户：\n\n欢迎使用我们的系统！\n\n为了确保您能正确使用系统功能，请完成以下简单测验。',
  qa: [
    {
      q: '系统支持哪些主要功能？',
      c: ['直播数据分析', '订单管理', '字幕编辑', '文件下载'],
      a: [1, 2, 3] // 选项A、B、C是正确答案
    },
    {
      q: '如何切换暗黑模式？',
      c: ['点击设置按钮', '使用快捷键', '右上角主题切换', '系统自动'],
      a: [3] // 选项C是正确答案
    }
  ]
})
```

### 测试一封信功能

在浏览器开发者工具的控制台中执行：

```javascript
// 方法1：发送简单文本一封信
window.electron.ipcRenderer.send('letter', {
  message: '这是一条测试信件\n\n请仔细阅读并完成答题测验。',
  qa: [
    {
      q: '这是一道测试题目，请选择正确答案：',
      c: ['选项A', '选项B', '选项C', '选项D'],
      a: [2, 3]
    }
  ]
})

// 方法2：调用后端测试函数（包含HTML内容）
window.electron.ipcRenderer.invoke('test:sendLetter').then((result) => {
  console.log('测试结果:', result)
})

// 方法3：发送HTML文档一封信
window.electron.ipcRenderer.send('letter', {
  message: `
    <div style="text-align: center; padding: 20px;">
      <h1 style="color: #007bff;">📢 重要通知</h1>
      <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
        <p><strong>您好！</strong></p>
        <p>这是一个支持 <em>HTML</em> 内容的测试信件。</p>
        <ul style="text-align: left; display: inline-block;">
          <li>支持<span style="color: red;">彩色文字</span></li>
          <li>支持<strong>粗体</strong>和<em>斜体</em></li>
          <li>支持列表和其他HTML元素</li>
        </ul>
      </div>
    </div>
  `,
  qa: [
    {
      q: '一封信系统支持哪些内容格式？',
      c: ['纯文本', 'HTML文档', 'Markdown', '以上都支持'],
      a: [1, 2]
    }
  ]
})
```

## 架构说明

### IPC通信流程

```
后端LZYService.sendLetter(data)
  → BrowserWindow.webContents.send('letter', data)
    → 前端ipcRenderer.on('letter', callback)
      → useLetter.showLetter()
        → Bootstrap Modal显示
```

### 状态管理

- 使用 `useLetter` composable 统一管理状态
- 支持页面切换、答题状态跟踪、结果计算
- 事件监听的初始化和清理

### 组件通信

- 父组件通过 `useLetter` 获取状态
- 子组件通过 props 接收数据
- 事件通过 composable 方法触发

## 扩展可能

1. **答题时间限制** - 为每题或整体测验添加时间限制
2. **答题历史** - 记录用户的答题历史和成绩
3. **题目随机化** - 支持题目和选项的随机排序
4. **答题提示** - 提供答题提示或解析
5. **成绩统计** - 提供详细的答题数据分析
6. **音频/视频支持** - 在消息或题目中嵌入多媒体内容
7. **答题奖励** - 根据答题成绩给予用户奖励
8. **批量发送** - 支持向特定用户群体发送不同的一封信

## 注意事项

1. 一封信弹窗在答题完成前无法关闭
2. 必须按顺序完成：阅读 → 答题 → 查看结果
3. 答案索引从1开始计算（与数组索引不同）
4. 支持多选题，用户必须选择所有正确答案才算正确
5. 弹窗会自动适配暗黑/明亮主题
