@font-face {
  font-family: 'iconfont'; /* Project id 4935158 */
  src:
    url('iconfont.woff2?t=1748485070207') format('woff2'),
    url('iconfont.woff?t=1748485070207') format('woff'),
    url('iconfont.ttf?t=1748485070207') format('truetype');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-liaotian:before {
  content: '\e60f';
}

.icon-dianying:before {
  content: '\e623';
}
