# 房间列表本地文件存在性检查

## 任务描述

为 roomList 中的每个房间检查是否存在对应的本地缓存文件，如果不存在则添加字符标识。

## 实现方案

在 `src/main/Service/GFService.ts` 的 `/detail/getData` 处理器中，获取 roomList 后添加文件存在性检查逻辑。

## 技术实现

### 修改位置

- 文件：`src/main/Service/GFService.ts`
- 位置：第24行 `const roomList = await getAllRoomCoreData()` 之后

### 实现逻辑

```typescript
// 检查每个房间的本地文件是否存在，不存在的添加标识
roomList.forEach((room) => {
  const roomFilePath = getBaseCachePath() + 'room_' + room.live_id
  if (!fs.existsSync(roomFilePath)) {
    ;(room as any).missingFile = '✗'
  }
})
```

### 功能说明

1. 遍历 roomList 中的每个房间数据
2. 构造对应的本地缓存文件路径：`cache/room_${room.live_id}`
3. 检查文件是否存在
4. 如果文件不存在，为该房间对象添加 `missingFile: '✗'` 字段

## 预期结果

- 存在本地文件的房间：无额外标识
- 缺失本地文件的房间：带有 `missingFile: '✗'` 标识

## 完成状态

✅ 已完成实现并验证代码逻辑正确
