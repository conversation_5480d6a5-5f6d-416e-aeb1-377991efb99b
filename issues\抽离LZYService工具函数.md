# 抽离LZYService工具函数

## 任务背景

将 LZYService.ts 中的工具函数抽离到独立的工具类文件中，以提高代码的模块化和可维护性。

## 执行计划

1. 创建 utils 文件夹
2. 创建 RoomCoreDataUtil.ts - 房间数据处理相关工具函数
3. 创建 FormatUtil.ts - 格式化相关工具函数
4. 创建 MetricsUtil.ts - 指标计算相关工具函数
5. 创建 index.ts - 统一导出工具函数
6. 修改 LZYService.ts 使用新的工具函数

## 抽离的函数

1. RoomCoreDataUtil.ts

   - readRoomDataById
   - roomData2RoomCoreData
   - extractZbtjFromFlowDistribution
   - extractFfllFromFlowDistribution

2. FormatUtil.ts

   - formatDuration

3. MetricsUtil.ts
   - calculateAccountMetrics

# LinkAge组件重构任务

## 背景

LinkAge.vue 组件耦合过于严重，需要进行拆分重构。

## 重构方案

采用细粒度拆分方案，将组件分解为多个独立的子组件和composables。

## 执行计划

### 第一阶段：创建 Composables ✅

1. **useMetricCache.ts** - 指标数据缓存管理 ✅
2. **useCommentProcessor.ts** - 评论数据处理 ✅
3. **useSubtitleProcessor.ts** - 字幕数据处理 ✅
4. **useVideoPlayer.ts** - 视频播放器逻辑 ✅

### 第二阶段：创建子组件 (待执行)

5. **TimeDivider.vue** - 时间分割线组件
6. **ContextMenu.vue** - 右键菜单组件
7. **CommentOverlay.vue** - 评论悬浮层组件
8. **SubtitlePanel.vue** - 字幕面板组件
9. **VideoPlayer.vue** - 视频播放器组件

### 第三阶段：重构主组件 (待执行)

10. **LinkAge.vue** - 重构为协调器组件，管理子组件通信

### 第四阶段：CSS 分离 (待执行)

11. 将相关样式分离到各自组件中
12. 创建共享样式文件

## 已完成

- ✅ 创建了4个核心composables
- ✅ 将composables移动到hooks目录中
- ✅ 建立了良好的目录结构管理

## 已完成

- ✅ 创建了4个核心composables
- ✅ 将composables移动到hooks目录中
- ✅ 建立了良好的目录结构管理
- ✅ 创建了5个子组件：TimeDivider、ContextMenu、CommentOverlay、SubtitlePanel、VideoPlayer
- ✅ 重构了主组件为协调器模式（LinkAgeNew.vue）
- ✅ 实现了组件间的事件通信和数据流管理
- ✅ CSS样式分离到各个子组件中

## 重构成果总结

### 1. 组件拆分

- **TimeDivider.vue**: 时间分割线和指标显示
- **ContextMenu.vue**: 可复用的右键菜单
- **CommentOverlay.vue**: 评论悬浮层，集成了右键菜单
- **SubtitlePanel.vue**: 字幕面板，包含导出打印功能
- **VideoPlayer.vue**: 视频播放器，集成评论悬浮层

### 2. 逻辑分离（Composables）

- **useMetricCache.ts**: 指标数据缓存管理
- **useCommentProcessor.ts**: 评论数据处理和隐藏功能
- **useSubtitleProcessor.ts**: 字幕数据处理和时间分割
- **useVideoPlayer.ts**: 视频播放器控制逻辑

### 3. 主要改进

- **职责单一**: 每个组件只负责特定功能
- **可复用性**: 组件可以在其他地方复用
- **可维护性**: 代码结构清晰，易于理解和修改
- **可测试性**: 独立的composables便于单元测试
- **事件驱动**: 通过emit/props实现组件间通信
- **CSS分离**: 样式与组件逻辑分离，便于主题定制

### 4. 事件流设计

```
父组件(LinkAgeNew)
├── 管理所有状态和数据流
├── 协调子组件间的通信
├── 处理时间同步逻辑
└── 子组件通过emit向上传递事件

子组件
├── 接收props数据
├── 触发emit事件
└── 独立的UI和交互逻辑
```

重构完成，原本1277行的巨型组件被拆分为多个职责明确的小组件，大大提高了代码的可维护性和可扩展性。

### 5. 最终目录结构

```
linkAge/
├── components/           # 子组件目录
│   ├── TimeDivider.vue
│   ├── ContextMenu.vue
│   ├── CommentOverlay.vue
│   ├── SubtitlePanel.vue
│   ├── VideoPlayer.vue
│   ├── RealTimeMetricGrid.vue
│   └── ShortcutModal.vue
├── hooks/               # Composables目录
│   ├── useMetricCache.ts
│   ├── useCommentProcessor.ts
│   ├── useSubtitleProcessor.ts
│   ├── useVideoPlayer.ts
│   └── useRealTimeMetrics.ts
├── LinkAge.vue          # 重构后的主组件
└── LinkAge.old.vue      # 原始组件备份
```

### 6. 组件导入关系

- 主组件导入：`./components/ComponentName.vue`
- 子组件间导入：`./ComponentName.vue`
- Hooks导入：`../hooks/hookName.ts`

### 7. 重构完成总结

**完整重构已完成！**

✅ **主要改进**：

- 原本1277行的巨型组件拆分为7个独立子组件
- 代码可读性提升：从单一巨型文件变为职责明确的小组件
- 可维护性增强：每个组件职责单一，修改影响范围小
- 可复用性提升：组件可以在其他页面复用
- 可测试性改善：独立的composables便于单元测试

✅ **目录结构标准化**：

- 所有子组件统一管理在 `components/` 目录
- 所有业务逻辑抽离到 `hooks/` 目录
- 导入路径规范化，便于维护

✅ **组件拆分策略**：

- **UI组件**：独立的视觉和交互组件
- **逻辑组件**：通过composables分离业务逻辑
- **协调组件**：主组件负责状态管理和组件协调

目录结构管理和组件重构全面完成！
