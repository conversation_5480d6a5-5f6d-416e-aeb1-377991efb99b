# DragableTable 可拖拽表格组件实现任务

## 任务描述

实现一个基于 Element UI Table 的可拖拽表头组件，支持：

- 表头拖拽调整列顺序
- 表头显示/隐藏配置
- localStorage 持久化存储
- 格式化函数支持
- 自定义插槽支持

## 实施计划

1. ✅ 安装 sortablejs 依赖
2. ✅ 创建 dragableTable.vue 核心组件
3. ✅ 实现列配置管理功能
4. ✅ 集成 Sortable.js 拖拽功能
5. ✅ 实现 localStorage 存储
6. ✅ 支持格式化函数和插槽
7. ✅ 创建使用示例文件
8. ✅ 编写详细使用文档

## 已完成文件

- `src/renderer/src/components/common/dragableTable.vue` - 核心组件
- `src/renderer/src/components/common/dragableTableExample.vue` - 使用示例
- `src/renderer/src/components/common/README.md` - 使用文档

## 核心特性实现

### 1. 列配置接口

```typescript
interface Column {
  key: string // 列唯一标识
  label: string // 列标题
  width?: number // 列宽度
  fixed?: 'left' | 'right' // 固定列
  sortable?: boolean // 是否可排序
  format?: (value: any, row: any) => string // 格式化函数
  slotName?: string // 自定义插槽名称
}
```

### 2. 拖拽功能

- 使用 Sortable.js 实现表头拖拽
- 支持拖拽动画和视觉反馈
- 自动更新列顺序配置

### 3. 配置管理

- 列顺序管理 (columnOrder)
- 列可见性管理 (columnVisibility)
- localStorage 自动保存/读取
- 新列自动处理

### 4. 格式化和插槽

- 支持 format 函数格式化显示
- 支持自定义插槽渲染
- 插槽参数：row, column, value, index

### 5. 样式兼容

- 支持项目暗色主题
- 响应式布局设计
- 拖拽状态样式反馈

## 使用方法

### 基础用法

```vue
<DragableTable :columns="columns" :data="tableData" storage-key="my-table-config" />
```

### 格式化函数

```javascript
{
  key: 'salary',
  label: '薪资',
  format: (value) => `¥${value.toLocaleString()}`
}
```

### 自定义插槽

```vue
<DragableTable :columns="columns" :data="data">
  <template #actions="{ row, index }">
    <el-button @click="handleEdit(row)">编辑</el-button>
  </template>
</DragableTable>
```

## 技术要点

1. **Sortable.js 集成**: 在 el-table 头部创建拖拽实例
2. **配置持久化**: localStorage 存储列顺序和可见性
3. **TypeScript 支持**: 完整的类型定义和类型安全
4. **事件机制**: columnOrderChange 和 columnVisibilityChange 事件
5. **兼容性处理**: 新增列的默认配置合并

## 注意事项

- 至少保留一列可见
- storageKey 避免冲突
- 列 key 必须唯一
- 支持 Element UI 原生 props 透传

## 完成状态

✅ 组件开发完成
✅ 示例代码完成
✅ 文档编写完成
✅ TypeScript 类型检查通过
✅ 工具箱插槽功能完成
✅ el-table 属性透传完成
✅ Bootstrap 风格配置按钮完成

## 最新更新

- 添加了 `toolbox` 插槽，支持自定义工具按钮（如导出、刷新等）
- 移除了默认的 `border` 属性，改为通过 props 透传
- 使用 `inheritAttrs: false` 和 `v-bind="$attrs"` 实现完整的 el-table 属性透传
- 优化了工具箱和配置按钮的布局，使用 gap 实现间距
- **新增内置分页功能**，支持完整的分页配置和状态管理
- 成功将 HomeTable 组件迁移到新的 DragableTable 组件
- 移除了对外部 usePagination 的依赖，实现了完全内聚的表格解决方案

## 分页功能特性

- ✅ 可配置的分页参数（pageSize、pageSizes、layout等）
- ✅ 自动数据分页处理
- ✅ 分页状态管理（currentPage、pageSize）
- ✅ 分页事件通知（page-change、page-size-change）
- ✅ 可选的总条数显示
- ✅ 响应式分页布局

## HomeTable 快速筛选器集成

- ✅ 将 HomeTable 的快速日期筛选器移动到 DragableTable 工具箱
- ✅ 将自定义日期选择器也移动到工具箱
- ✅ 优化工具箱布局，支持筛选器、搜索框和操作按钮的合理排列
- ✅ 添加响应式样式，在小屏幕上自动调整布局
- ✅ 移除了 HomeTable 原有的独立筛选器容器

## 工具箱功能特性

- ✅ 灵活的布局系统，支持任意组件组合
- ✅ 内置间距和对齐样式
- ✅ 响应式设计，自动适配不同屏幕尺寸
- ✅ 支持筛选器、搜索框、按钮等各种工具组件

组件现在提供了完整的表格解决方案，包括列拖拽、列配置、工具箱扩展（支持筛选器）和分页功能。外部只需传入 columns 配置和 data 数据即可获得功能完善的表格组件。
