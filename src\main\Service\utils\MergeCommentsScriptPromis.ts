/*
  合并comments和wcommentsPromise版本
  1. 读取comments文件
  2. 读取wcomments文件
  3. 合并comments和wcomments
  4. 保存到缓存文件
*/

import { promises as fs } from 'fs'
import path from 'path'
import { app } from 'electron'

const rootDir = app.getAppPath() // 返回 electron-app 根目录（即 package.json 所在目录）

// 简单异步并发控制函数，poolLimit表示最大并发数
// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
async function asyncPool(poolLimit, array, iteratorFn) {
  const ret: Promise<any>[] = []
  const executing: Promise<any>[] = []

  for (const item of array) {
    const p = Promise.resolve().then(() => iteratorFn(item))
    ret.push(p)

    if (poolLimit <= array.length) {
      const e = p.then(() => {
        executing.splice(executing.indexOf(e), 1)
      })
      executing.push(e)
      if (executing.length >= poolLimit) {
        await Promise.race(executing)
      }
    }
  }

  return Promise.all(ret)
}

export async function MergeCommentsScriptPromis(roomId: string): Promise<any[]> {
  // 路径定义
  const cacheFilePath = path.join(rootDir, `cache/comments/comments_${roomId}.json`)
  const commentsDir = path.join(rootDir, `cache/comments_${roomId}`)
  const wcommentsDir = path.join(rootDir, `cache/wcomments_${roomId}`)

  // 尝试读取缓存文件
  try {
    const cacheContent = await fs.readFile(cacheFilePath, 'utf-8')
    const cachedData = JSON.parse(cacheContent)
    return cachedData // 如果缓存存在，则直接返回
  } catch (err: any) {
    // 确保缓存目录存在
    await fs.mkdir(path.dirname(cacheFilePath), { recursive: true })
    // 确保文件存在
    await fs.writeFile(cacheFilePath, '[]', 'utf-8')
    // 缓存不存在，继续处理
    // 若错误不是文件不存在的错误，抛出
    if (err.code !== 'ENOENT') throw err
  }

  console.time('总耗时')

  try {
    // 读取 comments 并构建映射表
    const commentTagMap = new Map()
    console.time('读取comments耗时')
    const commentFiles = await fs.readdir(commentsDir)
    console.log(`找到 ${commentFiles.length} 个comment文件`)

    // 限制最多100个文件并发读取
    await asyncPool(100, commentFiles, async (file) => {
      const filePath = path.join(commentsDir, file)
      const data = await fs.readFile(filePath, 'utf-8')
      const json = JSON.parse(data)
      if (json.list?.comments?.length) {
        json.list.comments.forEach((comment) => {
          const key = `${comment.nick_name}|||${comment.content}`
          commentTagMap.set(key, comment.comment_tag)
        })
      }
    })
    console.timeEnd('读取comments耗时')

    // 处理 wcomments
    console.time('处理wcomments耗时')
    // 若没有wcommentsDir 直接返回comments
    if (!(await fs.stat(wcommentsDir)).isDirectory()) {
      console.log('没有找到wcommentsDir，直接返回comments')
      const commentsList = await asyncPool(100, commentFiles, async (file) => {
        const filePath = path.join(commentsDir, file)
        const json = JSON.parse(await fs.readFile(filePath, 'utf-8'))

        if (json.list?.comments?.length) {
          for (const comment of json.list.comments) {
            return {
              ...comment,
              event_ts: file
            }
          }
        }
        return json
      })
      await fs.writeFile(cacheFilePath, JSON.stringify(commentsList, null, 2), 'utf-8')

      return commentsList
    }
    const wcommentFiles = await fs.readdir(wcommentsDir)
    console.log(`找到 ${wcommentFiles.length} 个wcomment文件`)
    const wcomments: any[] = []

    // 同样限制并发数
    await asyncPool(100, wcommentFiles, async (file) => {
      const filePath = path.join(wcommentsDir, file)
      const data = await fs.readFile(filePath, 'utf-8')
      const json = JSON.parse(data)

      const nickName = json.user?.nickName
      const content = json.content

      if (nickName && content) {
        const key = `${nickName}|||${content}`
        const tag = commentTagMap.get(key)
        if (tag !== undefined) {
          json.comment_tag = tag
        }
      }

      // 删除common字段
      delete json.common

      // 添加字段并推入结果数组
      wcomments.push({
        ...json,
        event_ts: json.eventTime,
        nick_name: json.user?.nickName
      })
    })
    console.timeEnd('处理wcomments耗时')

    // 保存到缓存文件
    await fs.writeFile(cacheFilePath, JSON.stringify(wcomments, null, 2), 'utf-8')

    console.timeEnd('总耗时')
    console.log('✅ 处理完成')
    return wcomments
  } catch (error) {
    console.error('发生错误:', error)
    throw error
  }
}

if (require.main === module) {
  MergeCommentsScriptPromis('7524671815537691407')
    .then((res) => {
      console.log(res.length)
    })
    .catch((error) => {
      console.error('发生错误:', error)
    })
}
