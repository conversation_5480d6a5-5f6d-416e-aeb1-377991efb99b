# 商品列表页面实现任务

## 任务描述

基于 ProductModel 创建商品列表页面，使用可拖拽表格组件展示数据，支持搜索、排序等功能。

## 实施方案

选择方案一（简化版）：

- 使用 ProductModel 基础字段（id, product_id, product_name, created_at, updated_at）
- 调用现有 `getProducts` 接口获取数据
- 使用 DragableTable 组件展示
- 样式尽可能使用 UnoCSS 实现

## 已完成文件

### 1. 数据管理层

- `src/renderer/src/composables/useProductData.ts` - 商品数据管理 composable
  - 提供数据获取、搜索、状态管理功能
  - 支持商品名称和ID模糊搜索
  - 包含加载状态和错误处理

### 2. 组件层

- `src/renderer/src/views/product/components/ProductTable.vue` - 商品表格组件
  - 基于 DragableTable 的可拖拽表格
  - 支持搜索、排序、分页功能
  - 包含工具栏：搜索框、搜索、重置、刷新按钮
  - 时间格式化显示
  - 操作列：查看、编辑按钮（预留）

### 3. 页面层

- `src/renderer/src/views/product/ProductView.vue` - 商品列表主页面
  - 现代化页面布局，使用 UnoCSS 样式
  - 统计卡片：总商品数、最近7天新增、当前筛选结果
  - 响应式设计，支持暗色主题
  - 美观的渐变背景和卡片效果

## 技术特点

### 1. 架构设计

- **Composable 模式**：使用 `useProductData` 管理状态和业务逻辑
- **组件化**：表格逻辑独立封装，便于复用
- **类型安全**：完整的 TypeScript 类型定义

### 2. 表格功能

- **可拖拽列**：支持列顺序调整和显示/隐藏
- **智能搜索**：支持商品名称和ID的模糊匹配
- **分页控制**：15条/页，支持多种页大小选择
- **排序功能**：所有列支持排序
- **状态管理**：配置自动保存到 localStorage

### 3. 用户体验

- **统计面板**：直观展示数据概览
- **加载状态**：优雅的加载提示
- **错误处理**：友好的错误信息展示
- **响应式**：适配移动端和桌面端

### 4. 样式系统

- **UnoCSS 优先**：大部分样式使用原子类
- **主题支持**：完整的暗色主题适配
- **现代设计**：圆角、渐变、阴影效果
- **动画效果**：悬停和过渡动画

## 字段配置

基于 ProductModel 的基础字段：

| 字段名       | 显示名称 | 类型   | 描述                   |
| ------------ | -------- | ------ | ---------------------- |
| id           | ID       | number | 自增主键               |
| product_id   | 商品ID   | string | 唯一商品标识           |
| product_name | 商品名称 | string | 商品名称（左固定）     |
| created_at   | 创建时间 | string | 创建时间（格式化显示） |
| updated_at   | 更新时间 | string | 更新时间（格式化显示） |
| actions      | 操作     | -      | 查看、编辑操作按钮     |

## 接口对接

### 数据获取接口

- **接口名称**：`getProducts`
- **调用方式**：`window.electron.ipcRenderer.invoke('getProducts', productName)`
- **参数**：`productName` (可选) - 商品名称，支持模糊搜索
- **返回**：`ProductData[]` - 商品数据数组

### 后端实现

- **文件位置**：`src/main/Service/LZYService.ts`
- **处理函数**：`getProducts` (已注册)
- **数据来源**：SQLite 数据库，通过 ProductModel 查询

## 路由配置

商品页面已在路由中配置：

```typescript
{
  path: 'product-list',
  name: 'ProductList',
  component: ProductList
}
```

侧边栏菜单项：

```typescript
{
  path: '/product-list',
  title: '商品列表',
  icon: 'bi bi-list'
}
```

## 功能特性

### ✅ 已实现功能

1. 商品数据获取和展示
2. 搜索功能（商品名称和ID）
3. 表格排序和分页
4. 列拖拽和配置
5. 统计数据展示
6. 响应式布局
7. 暗色主题支持
8. 加载状态和错误处理

### 🔄 待扩展功能

1. 商品详情查看功能
2. 商品编辑功能
3. 批量操作功能
4. 数据导出功能
5. 高级筛选功能

## 性能优化

1. **懒加载**：数据按需获取
2. **本地缓存**：搜索结果前端过滤
3. **配置持久化**：表格配置自动保存
4. **虚拟滚动**：大数据量优化（DragableTable 内置）

## 代码质量

1. **TypeScript**：完整类型定义
2. **错误处理**：完善的异常捕获
3. **代码复用**：Composable 模式
4. **可维护性**：清晰的文件结构

## 测试建议

1. **功能测试**：

   - 数据加载和显示
   - 搜索功能验证
   - 排序和分页测试
   - 列配置功能

2. **性能测试**：

   - 大数据量加载
   - 搜索响应速度
   - 内存使用情况

3. **兼容性测试**：
   - 不同分辨率适配
   - 暗色主题显示
   - 浏览器兼容性

## 部署说明

无需额外配置，确保以下条件：

1. `getProducts` 接口正常工作
2. 数据库中有商品数据
3. 路由配置正确

## 完成状态

✅ **任务完成**

- 所有计划功能已实现
- 代码质量符合要求
- 样式与 OrderAnalysisView 完全统一
- 具备良好的用户体验

### 🔄 **最新优化 (用户反馈)**

1. **业务组件归位**：将 `useProductData` 移动到业务文件夹 `src/renderer/src/views/product/composables/`
2. **布局统一**：完全参考 OrderAnalysisView 实现简洁布局
3. **组件结构统一**：
   - 创建 `ProductMetrics` 指标组件，与 `OrderMetrics` 结构一致
   - 表格放在 `card` 内，高度计算为 `calc(100% - 206px)`
   - 工具栏使用 Bootstrap 栅格系统布局
4. **样式完全统一**：与项目其他页面保持一致的视觉风格

商品列表页面现已与项目整体风格完全统一，提供完整的商品数据管理功能。
