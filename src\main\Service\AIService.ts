import { IpcMain, dialog } from 'electron'
import * as fs from 'fs'
import * as path from 'path'
import { getAPPBasePath } from '../module/API'

// AI分析服务类
export default class AIService {
  // 注册IPC事件处理器
  public static hook(ipcMain: IpcMain): void {
    // 读取模板文件
    ipcMain.handle('read-templates', async () => {
      try {
        const temFilePath = path.join(getAPPBasePath(), 'tem.json')
        const temContent = await fs.promises.readFile(temFilePath, 'utf-8')
        return JSON.parse(temContent)
      } catch (error: any) {
        console.error('读取模板文件失败:', error)
        throw new Error(`读取模板文件失败: ${error.message}`)
      }
    })

    // 保存分析结果
    ipcMain.handle(
      'save-analysis-results',
      async (_event, params: { roomId: string; results: any[] }) => {
        try {
          const { roomId, results } = params
          if (!roomId) {
            console.error('保存分析结果失败: roomId为空')
            return { success: false, error: 'roomId为空' }
          }

          const cacheDirPath = path.join(getAPPBasePath(), 'cache')
          const resultFilePath = path.join(cacheDirPath, `room_${roomId}`)
          console.log('保存分析结果路径:', resultFilePath)

          // 准备要保存的数据
          let existingData: any = {}

          // 检查文件是否已存在
          try {
            const fileContent = await fs.promises.readFile(resultFilePath, 'utf-8')
            try {
              existingData = JSON.parse(fileContent)
              console.log('读取到现有数据')
            } catch (parseError) {
              console.warn(parseError, '解析现有文件失败，将创建新文件')
              existingData = {}
            }
          } catch (err) {
            console.log(err, '文件不存在，将创建新文件')
          }

          // 如果temResults不存在，则创建
          if (!existingData.temResults) {
            existingData.temResults = []
          }

          // 添加新的分析结果到temResults数组
          if (Array.isArray(existingData.temResults)) {
            // 将新结果追加到现有数组
            existingData.temResults.push(...results)
          } else {
            // 如果temResults不是数组，则重新创建
            existingData.temResults = [...results]
          }
          // existingData.timestamp = new Date().toISOString();

          // 保存更新后的数据
          await fs.promises.writeFile(
            resultFilePath,
            JSON.stringify(existingData, null, 2),
            'utf-8'
          )

          console.log('分析结果保存成功')
          return { success: true }
        } catch (error: any) {
          console.error('保存分析结果失败:', error)
          throw new Error(`保存分析结果失败: ${error.message}`)
        }
      }
    )

    // 显示错误消息对话框
    ipcMain.on('show-error-message', (_event, title: string, message: string) => {
      dialog.showErrorBox(title, message)
    })
  }
}
