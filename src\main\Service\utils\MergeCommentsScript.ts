/*
  合并comments和wcomments同步版本
  1. 读取comments文件
  2. 读取wcomments文件
  3. 合并comments和wcomments
  4. 保存到缓存文件
*/

import fs from 'fs'
import path from 'path'
import Logger from './Logger'
import { getAPPBasePath } from '../../module/API'

const rootDir = getAPPBasePath() // 返回 electron-app 根目录（即 package.json 所在目录）

export async function MergeCommentsScript(roomId: string): Promise<any[]> {
  Logger.info(`开始合并评论数据，房间ID: ${roomId}`)

  // 路径定义
  const cacheFilePath = path.join(rootDir, `cache/comments/comments_${roomId}.json`)
  const commentsDir = path.join(rootDir, `cache/comments_${roomId}`)
  const wcommentsDir = path.join(rootDir, `cache/wcomments_${roomId}`)

  // 尝试读取缓存文件
  try {
    const cacheContent = fs.readFileSync(cacheFilePath, 'utf-8')
    const cachedData = JSON.parse(cacheContent)
    Logger.info(`找到缓存文件，直接返回 ${cachedData.length} 条数据，房间ID: ${roomId}`)
    return cachedData // 如果缓存存在，则直接返回
  } catch (err: any) {
    // 确保缓存目录存在
    fs.mkdirSync(path.dirname(cacheFilePath), { recursive: true })
    // 确保文件存在
    fs.writeFileSync(cacheFilePath, '[]', 'utf-8')
    // 缓存不存在，继续处理
    // 若错误不是文件不存在的错误，抛出
    if (err.code !== 'ENOENT') {
      Logger.error(`读取缓存文件失败，房间ID: ${roomId}，错误: ${err.message}`)
      throw err
    }
    Logger.info(`缓存文件不存在，开始处理原始数据，房间ID: ${roomId}`)
  }

  // 开始计时
  console.time('总耗时')

  // 构建comment映射表
  const commentTagMap = new Map()
  console.time('读取comments耗时')

  let commentFiles: string[]
  try {
    commentFiles = fs.readdirSync(commentsDir)
    console.log(`找到 ${commentFiles.length} 个comment文件`)
    Logger.info(
      `开始构建评论标签映射，找到 ${commentFiles.length} 个comment文件，房间ID: ${roomId}`
    )

    commentFiles.forEach((file) => {
      const filePath = path.join(commentsDir, file)
      const json = JSON.parse(fs.readFileSync(filePath, 'utf-8'))

      if (json.list?.comments?.length) {
        for (const comment of json.list.comments) {
          const key = `${comment.nick_name}|||${comment.content}`
          commentTagMap.set(key, comment.comment_tag)
        }
      }
    })
    console.timeEnd('读取comments耗时')
    Logger.info(`评论标签映射构建完成，共 ${commentTagMap.size} 个标签，房间ID: ${roomId}`)
  } catch (err: any) {
    Logger.error(`读取comments目录失败，房间ID: ${roomId}，错误: ${err.message}`)
    throw err
  }

  // 处理wcomments
  console.time('处理wcomments耗时')
  const wcomments: any[] = []
  // 若没有wcommentsDir 直接返回comments
  if (!fs.existsSync(wcommentsDir)) {
    console.log('没有找到wcommentsDir，直接返回comments')
    Logger.warn(`wcomments目录不存在，使用comments数据，房间ID: ${roomId}`)

    const commentsList = commentFiles.map((file) => {
      const filePath = path.join(commentsDir, file)
      const json = JSON.parse(fs.readFileSync(filePath, 'utf-8'))
      if (json.list?.comments?.length) {
        for (const comment of json.list.comments) {
          return {
            ...comment,
            event_ts: Math.floor(Number(file) / 1000)
          }
        }
      }
      return json
    })
    fs.writeFileSync(cacheFilePath, JSON.stringify(commentsList, null, 2), 'utf-8')
    Logger.info(`处理完成，保存 ${commentsList.length} 条comments数据，房间ID: ${roomId}`)

    return commentsList
  }

  try {
    const wcommentFiles = fs.readdirSync(wcommentsDir)
    console.log(`找到 ${wcommentFiles.length} 个wcomment文件`)
    Logger.info(`开始处理wcomments，找到 ${wcommentFiles.length} 个文件，房间ID: ${roomId}`)

    let taggedCount = 0
    wcommentFiles.forEach((file) => {
      const filePath = path.join(wcommentsDir, file)
      const json = JSON.parse(fs.readFileSync(filePath, 'utf-8'))

      const nickName = json.user?.nickName
      const content = json.content

      if (nickName && content) {
        const key = `${nickName}|||${content}`
        const tag = commentTagMap.get(key)

        if (tag !== undefined) {
          json.comment_tag = tag
          taggedCount++
        }
      }

      // 删除common 字段
      delete json.common
      wcomments.push({
        ...json,
        event_ts: json.eventTime,
        nick_name: json.user?.nickName
      })
    })

    fs.writeFileSync(cacheFilePath, JSON.stringify(wcomments, null, 2), 'utf-8')
    console.timeEnd('处理wcomments耗时')

    console.timeEnd('总耗时')
    console.log('✅ 处理完成')
    Logger.info(
      `评论合并处理完成，房间ID: ${roomId}，总计: ${wcomments.length} 条，已标记: ${taggedCount} 条`
    )

    return wcomments
  } catch (err: any) {
    Logger.error(`处理wcomments失败，房间ID: ${roomId}，错误: ${err.message}`)
    throw err
  }
}

if (require.main === module) {
  MergeCommentsScript('7524671815537691407').then((res) => {
    console.log(res.length)
  })
}
