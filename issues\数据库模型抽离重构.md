# 数据库模型抽离重构

## 任务背景

将 SqliteDB.ts 中的 RoomCoreDataModel 抽离到独立文件，并创建新的订单模型。

## 执行计划

1. 创建 model/RoomCoreData.ts - 抽离房间核心数据模型
2. 创建 model/Order.ts - 新建订单模型
3. 创建 model/index.ts - 统一模型导出
4. 更新 SqliteDB.ts - 移除内置模型，改为导入
5. 更新 interfaces.ts - 添加订单接口

## 改动文件

- src/main/module/model/RoomCoreData.ts (新建)
- src/main/module/model/Order.ts (新建)
- src/main/module/model/index.ts (新建)
- src/main/module/SqliteDB.ts (修改)
- src/main/module/interfaces.ts (修改)
