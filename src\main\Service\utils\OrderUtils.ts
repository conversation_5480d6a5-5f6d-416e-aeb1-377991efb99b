/**
 * 订单工具函数集合
 * 提供订单相关的所有工具函数和数据处理逻辑
 */

// ================================
// 订单数据验证工具函数
// ================================

/**
 * 验证订单数据完整性
 * @param orderData 订单数据
 * @returns 验证结果和错误信息
 */
export function validateOrderData(orderData: any): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  // 检查必填字段
  if (!orderData.order_id) {
    errors.push('订单ID不能为空')
  }

  if (!orderData.customer_name) {
    errors.push('客户姓名不能为空')
  }

  if (!orderData.total_amount || orderData.total_amount <= 0) {
    errors.push('订单金额必须大于0')
  }

  if (!orderData.created_at) {
    errors.push('创建时间不能为空')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// ================================
// 订单号生成工具函数
// ================================

/**
 * 生成订单号
 * @param prefix 前缀，默认为 'ORD'
 * @returns 生成的订单号
 */
export function generateOrderId(prefix: string = 'ORD'): string {
  const timestamp = Date.now()
  const random = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, '0')
  return `${prefix}${timestamp}${random}`
}

// ================================
// 订单状态工具函数
// ================================

/**
 * 订单状态枚举
 */
export enum OrderStatus {
  PENDING = 'pending', // 待处理
  CONFIRMED = 'confirmed', // 已确认
  PROCESSING = 'processing', // 处理中
  SHIPPED = 'shipped', // 已发货
  DELIVERED = 'delivered', // 已送达
  CANCELLED = 'cancelled', // 已取消
  REFUNDED = 'refunded' // 已退款
}

/**
 * 获取订单状态中文描述
 * @param status 订单状态
 * @returns 中文描述
 */
export function getOrderStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    [OrderStatus.PENDING]: '待处理',
    [OrderStatus.CONFIRMED]: '已确认',
    [OrderStatus.PROCESSING]: '处理中',
    [OrderStatus.SHIPPED]: '已发货',
    [OrderStatus.DELIVERED]: '已送达',
    [OrderStatus.CANCELLED]: '已取消',
    [OrderStatus.REFUNDED]: '已退款'
  }

  return statusMap[status] || '未知状态'
}

/**
 * 验证订单状态是否有效
 * @param status 订单状态
 * @returns 是否有效
 */
export function isValidOrderStatus(status: string): boolean {
  return Object.values(OrderStatus).includes(status as OrderStatus)
}

// ================================
// 订单金额格式化工具函数
// ================================

/**
 * 格式化订单金额为货币格式
 * @param amount 金额
 * @param currency 货币符号，默认为 '¥'
 * @returns 格式化后的金额字符串
 */
export function formatOrderAmount(amount: number, currency: string = '¥'): string {
  if (isNaN(amount) || amount < 0) {
    return `${currency}0.00`
  }

  return `${currency}${amount.toFixed(2)}`
}

/**
 * 计算订单总金额（含税）
 * @param subtotal 小计金额
 * @param taxRate 税率，默认0.1（10%）
 * @returns 含税总金额
 */
export function calculateTotalWithTax(subtotal: number, taxRate: number = 0.1): number {
  if (isNaN(subtotal) || subtotal < 0) {
    return 0
  }

  return parseFloat((subtotal * (1 + taxRate)).toFixed(2))
}

// ================================
// 订单日期工具函数
// ================================

/**
 * 格式化订单日期
 * @param date 日期字符串或时间戳
 * @returns 格式化后的日期字符串 YYYY-MM-DD HH:mm:ss
 */
export function formatOrderDate(date: string | number | Date): string {
  try {
    const dateObj = new Date(date)
    if (isNaN(dateObj.getTime())) {
      return ''
    }

    return dateObj.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
  } catch (error) {
    console.error('格式化订单日期失败:', error)
    return ''
  }
}

/**
 * 计算订单创建后的天数
 * @param createdAt 创建时间
 * @returns 天数
 */
export function getDaysFromCreation(createdAt: string | number | Date): number {
  try {
    const createDate = new Date(createdAt)
    const now = new Date()
    const diffTime = now.getTime() - createDate.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    return diffDays >= 0 ? diffDays : 0
  } catch (error) {
    console.error('计算订单天数失败:', error)
    return 0
  }
}
