# GM频道功能实现

## 任务概述

在常见问题tab上面（即菜单中FAQ之前）新增一个GM频道页面，类似于论坛功能，包含帖子列表页和详情页，支持回复展示。使用假数据实现。

## 实施方案

采用方案1：简单静态论坛页面

- 使用JSON配置文件存储假数据
- GM频道主页展示帖子列表
- 支持分类筛选和搜索功能
- 帖子详情页展示内容和回复列表
- 样式优先级：Bootstrap → UnoCSS → 手动CSS

## 执行步骤

### 1. 创建数据配置文件 ✅

- **文件**: `src/renderer/src/config/gmChannelConfig.ts`
- **内容**:
  - 定义PostItem、ReplyItem、CategoryItem接口
  - 5个分类：官方公告、自由讨论、问题求助、经验分享、意见反馈
  - 6个帖子假数据，包含置顶、分类、标签等
  - 5个回复假数据
  - 实现查询和搜索函数

### 2. 创建GM频道主页面 ✅

- **文件**: `src/renderer/src/views/gmchannel/GMChannelView.vue`
- **功能**:
  - 页面头部（标题、副标题）
  - 搜索功能（标题、内容、标签）
  - 分类筛选标签（全部+5个分类）
  - 帖子列表展示（置顶、分类、标题、作者、时间、标签、统计）
  - 响应式设计

### 3. 创建帖子详情页 ✅

- **文件**: `src/renderer/src/views/gmchannel/GMPostDetailView.vue`
- **功能**:
  - 使用独立布局（无侧边栏）
  - 返回按钮
  - 帖子完整内容展示
  - 回复列表（楼层、头像、作者、内容）
  - 支持Markdown格式内容显示
  - 帖子不存在处理

### 4. 更新路由配置 ✅

- **文件**: `src/renderer/src/router.ts`
- **添加路由**:
  - `/gm-channel` - GM频道主页（使用MainLayout）
  - `/gm-post/:id` - 帖子详情页（独立布局）

### 5. 更新菜单配置 ✅

- **文件**: `src/renderer/src/layouts/components/Sidebar.vue`
- **修改**: 在FAQ之前添加"GM频道"菜单项
- **图标**: `bi bi-chat-square-text`

## 技术实现

### 数据结构

```typescript
interface PostItem {
  id: string
  title: string
  content: string
  author: { name: string; avatar: string; level: string }
  category: string
  publishTime: string
  replyCount: number
  viewCount: number
  isTop: boolean
  tags: string[]
}

interface ReplyItem {
  id: string
  postId: string
  content: string
  author: { name: string; avatar: string; level: string }
  publishTime: string
  floor: number
}
```

### 功能特性

1. **分类系统**: 5个分类，每个分类有不同颜色标识
2. **搜索功能**: 支持标题、内容、标签搜索
3. **置顶机制**: 支持帖子置顶显示
4. **用户等级**: GM、VIP、普通用户三种等级
5. **时间显示**: 智能时间格式（分钟前、小时前、日期）
6. **内容格式**: 支持简单Markdown格式（粗体、斜体、@用户）
7. **响应式设计**: 适配移动端和桌面端

### 样式规范

- 优先使用Bootstrap组件和工具类
- 保持与FAQ页面风格一致
- 支持暗黑模式（使用CSS变量）
- hover效果和过渡动画

## 预期结果

- ✅ 用户可通过左侧菜单访问"GM频道"（位于FAQ之前）
- ✅ 主页面展示分类化的帖子列表，支持搜索和筛选
- ✅ 点击帖子可查看详情页，包含完整内容和回复列表
- ✅ 响应式设计，适配不同屏幕尺寸
- ✅ 与现有页面风格保持一致

## 执行状态

- [x] 创建数据配置文件
- [x] 创建GM频道主页面组件
- [x] 创建帖子详情页组件
- [x] 更新路由配置
- [x] 更新侧边栏菜单
- [ ] 功能测试验证

## 技术栈

- Vue 3 Composition API
- TypeScript
- Bootstrap CSS框架
- Vue Router
- 响应式数据绑定
