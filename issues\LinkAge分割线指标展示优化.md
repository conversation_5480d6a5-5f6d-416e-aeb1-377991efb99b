# LinkAge 分割线指标展示优化

## 需求

在 LinkAge.vue 组件的分割线上展示更多指标数据，包括：

1. 进入人数
2. 离开人数
3. 存续用户（进入-离开）
4. 成交金额
5. 成交单数

## 实现方案

1. 添加 `getMultipleMetricData` 函数，一次获取多个指标数据
2. 修改 `processedSegs` 计算属性，获取所有需要的指标数据
3. 重新设计分割线样式，以更美观的方式展示多个指标
4. 使用不同的图标和颜色区分不同类型的指标

## 具体改进

1. 添加了获取多个指标数据的函数 `getMultipleMetricData`

   - 获取进入人数 (watch_ucnt)
   - 获取离开人数 (leave_ucnt)
   - 计算存续用户 (进入-离开)
   - 获取成交金额 (order_amount)
   - 获取成交单数 (order_count)

2. 改进了分割线样式

   - 添加了图标和更清晰的时间标题
   - 将指标分为用户指标和订单指标两组
   - 使用不同的颜色和图标区分不同类型的指标
   - 添加了响应式样式，确保在小屏幕上也能良好显示

3. 优化了数据展示
   - 使用 `-` 表示缺失数据
   - 为成交金额添加货币符号
   - 使用缓存减少重复计算

## 效果

1. 分割线现在能够展示更多有用的指标数据
2. 指标数据按类型分组，更加清晰
3. 使用图标和颜色增强了可读性
4. 保持了良好的响应式设计
