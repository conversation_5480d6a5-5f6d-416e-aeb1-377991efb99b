import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 配置参数
const FOLDER_PATH = path.join(__dirname, '../cache/entry_7525785145926322959')
const OUTPUT_FILE = path.join(__dirname, './report/user_mapping_report.txt')

// 确保报告目录存在
const REPORT_DIR = path.join(__dirname, 'report')
if (!fs.existsSync(REPORT_DIR)) {
  fs.mkdirSync(REPORT_DIR, { recursive: true })
}

// 存储检查结果
const idStats = new Map() // {id: {count: 次数, names: Set(昵称)}}
const nameStats = new Map() // {nickName: {count: 次数, ids: Set(id)}}
const duplicateIds = [] // 重复ID的详细情况
const duplicateNames = [] // 重复昵称的详细情况

// 遍历文件夹中的所有文件
const files = fs.readdirSync(FOLDER_PATH)
files.forEach((filename) => {
  const filePath = path.join(FOLDER_PATH, filename)
  const data = JSON.parse(fs.readFileSync(filePath, 'utf-8'))

  const userId = data.user?.id
  const userNick = data.user?.nickName

  if (!userId || !userNick) {
    console.warn(`[警告] 文件 ${filename} 缺少ID或昵称字段`)
    return
  }

  // 统计ID出现情况
  if (!idStats.has(userId)) {
    idStats.set(userId, { count: 0, names: new Set() })
  }
  const idRecord = idStats.get(userId)
  idRecord.count++
  idRecord.names.add(userNick)

  // 统计昵称出现情况
  if (!nameStats.has(userNick)) {
    nameStats.set(userNick, { count: 0, ids: new Set() })
  }
  const nameRecord = nameStats.get(userNick)
  nameRecord.count++
  nameRecord.ids.add(userId)
})

// 分析重复情况
idStats.forEach((stats, id) => {
  if (stats.count > 1) {
    duplicateIds.push({
      id,
      totalOccurrences: stats.count,
      uniqueNames: stats.names.size,
      names: Array.from(stats.names)
    })
  }
})

nameStats.forEach((stats, nickName) => {
  if (stats.ids.size > 1) {
    duplicateNames.push({
      nickName,
      totalOccurrences: stats.count,
      uniqueIds: stats.ids.size,
      ids: Array.from(stats.ids)
    })
  }
})

// 生成详细报告
let report = `=== 用户ID与昵称统计分析报告 ===\n`
report += `检查时间: ${new Date().toISOString()}\n`
report += `扫描文件总数: ${files.length}\n`
report += `唯一用户ID数量: ${idStats.size}\n`
report += `唯一昵称数量: ${nameStats.size}\n\n`

// ID重复统计
report += `=== ID重复情况 ===\n`
report += `出现重复的ID数量: ${duplicateIds.length}\n`
if (duplicateIds.length > 0) {
  // 按出现次数排序
  duplicateIds.sort((a, b) => b.totalOccurrences - a.totalOccurrences)

  report += `\n[前20个最常出现的ID]\n`
  duplicateIds.slice(0, 20).forEach((item) => {
    report += `ID: ${item.id} (出现${item.totalOccurrences}次)\n`
    report += `对应昵称数: ${item.uniqueNames}\n`
    if (item.uniqueNames > 1) {
      report += `⚠️ 该ID对应多个昵称: ${item.names.join(', ')}\n`
    }
    report += `\n`
  })

  // 统计昵称变化情况
  const multiNameIds = duplicateIds.filter((item) => item.uniqueNames > 1)
  report += `\n[有昵称变化的ID]\n`
  report += `数量: ${multiNameIds.length} (占重复ID的${((multiNameIds.length / duplicateIds.length) * 100).toFixed(1)}%)\n`
}

// 昵称重复统计
report += `\n=== 昵称重复情况 ===\n`
report += `存在ID冲突的昵称数量: ${duplicateNames.length}\n`
if (duplicateNames.length > 0) {
  // 按关联ID数量排序
  duplicateNames.sort((a, b) => b.uniqueIds - a.uniqueIds)

  report += `\n[前20个最多ID冲突的昵称]\n`
  duplicateNames.slice(0, 20).forEach((item) => {
    report += `昵称: "${item.nickName}" (出现${item.totalOccurrences}次)\n`
    report += `对应不同ID数: ${item.uniqueIds}\n`
    report += `关联ID: ${item.ids.join(', ')}\n\n`
  })
}

// 写入报告文件
fs.writeFileSync(OUTPUT_FILE, report)
console.log(`检查完成，报告已生成: ${OUTPUT_FILE}`)

// 控制台输出关键指标
console.log('\n=== 关键统计结果 ===')
console.log(`文件总数: ${files.length}`)
console.log(`唯一用户ID: ${idStats.size}`)
console.log(`唯一昵称: ${nameStats.size}`)
console.log(`\n重复ID情况:`)
console.log(`- 出现重复的ID数量: ${duplicateIds.length}`)
console.log(`- 其中昵称有变化的ID: ${duplicateIds.filter((item) => item.uniqueNames > 1).length}`)
console.log(`\n昵称冲突情况:`)
console.log(`- 对应多个ID的昵称数量: ${duplicateNames.length}`)
if (duplicateNames.length > 0) {
  const maxConflict = Math.max(...duplicateNames.map((item) => item.uniqueIds))
  console.log(`- 最大ID冲突数: 1个昵称对应${maxConflict}个不同ID`)
}
