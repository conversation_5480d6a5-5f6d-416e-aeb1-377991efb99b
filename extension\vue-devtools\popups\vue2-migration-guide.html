<meta charset="utf-8" />
<link rel="stylesheet" type="text/css" href="./popup.css" />

<div class="migration-guide">
  <h2>Migration Tips</h2>
  <p style="text-indent: 8px">
    Vue DevTools v7 detected in your Vue2 project. v7 only supports Vue3 and will not work.
  </p>
  <p style="text-indent: 8px">
    The legacy version that supports both Vue 2 and Vue 3 has been moved to
    <a
      target="_blank"
      href="https://chromewebstore.google.com/detail/vuejs-devtools/iaajmlceplecbljialhhkmedjlpdblhp"
      >here</a
    >, please install and enable only the legacy version for your Vue2 app. If you're still using v5
    version, you can install it
    <a
      target="_blank"
      href="https://chromewebstore.google.com/detail/vuejs-devtools-v5/hkddcnbhifppgmfgflgaelippbigjpjo"
      >here</a
    >.
  </p>
</div>
