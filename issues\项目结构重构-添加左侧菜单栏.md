# 项目结构重构 - 添加左侧菜单栏

## 任务描述

重构 electron-app 的渲染器部分，添加左侧菜单栏，实现左右布局结构。

## 当前问题

1. 文件组织混乱：页面组件直接放在src根目录
2. 布局架构问题：Home.vue包含太多功能，缺乏统一布局管理
3. 缺乏左侧导航菜单概念

## 目标布局

- 左侧：固定宽度菜单栏（上下整个）
- 右侧：上中下三层结构
  - 顶部：从Home组件抽离的导航栏
  - 中间：主内容区域（router-view）
  - 底部：HomeFooter组件

## 实施计划

### 第一阶段：创建布局架构

1. 创建 layouts/ 目录结构
2. 创建 MainLayout.vue 主布局容器
3. 创建 Sidebar.vue 左侧菜单组件
4. 创建 Header.vue 顶部导航组件
5. 创建 Footer.vue 底部组件

### 第二阶段：重构路由和页面

6. 更新路由配置支持嵌套布局
7. 创建 views/ 目录并迁移现有页面
8. 保持现有功能逻辑不变

### 第三阶段：组件重组和优化

9. 重新组织 components/ 目录
10. 更新 App.vue 简化结构
11. 样式适配和测试

## 开始时间

2024年执行

## 预计用时

4-6小时
