# 信件组件答题逻辑集成

## 任务概述

为 LetterComponent.vue 补充完整的答题逻辑，参考 LetterModal.vue 的实现。

## 完成状态

✅ 已完成

## 主要改动

### 1. 引入答题逻辑

- 导入 `useLetter` composable
- 导入 `LetterPage` 枚举类型
- 获取答题相关的状态和方法

### 2. 页面结构改造

- 将原信件展示内容改为 MESSAGE 页面（保持完全不变）
- 添加 QUESTION 页面用于答题
- 使用条件渲染 `v-if` 在两个页面间切换

### 3. 操作逻辑实现

- **阅读确认**：添加"我已阅读完毕"按钮
- **答题功能**：复制 LetterModal 的答题页面结构
- **错误处理**：答题错误时弹窗提示并返回信件页
- **状态重置**：重置用户答案和阅读状态

### 4. 样式适配

- 复用 LetterModal 的答题相关样式
- 添加暗黑模式支持
- 保持与原信件展示风格一致

### 5. 技术修复

- 修复原有的 linter 错误（转义字符问题）
- 优化代码格式

## 技术细节

### 页面切换逻辑

```vue
<!-- 消息阅读页 -->
<div v-if="currentPage === LetterPage.MESSAGE" class="letter-container">
  <!-- 原信件内容保持不变 -->
</div>

<!-- 答题页 -->
<div v-else-if="currentPage === LetterPage.QUESTION" class="letter-container">
  <!-- 答题内容 -->
</div>
```

### 错误处理逻辑

- 答题错误时弹窗提示
- 重置所有答题状态
- 回到信件页面重新阅读

## 文件变更

- `src/renderer/src/components/LetterComponent.vue` - 主要修改文件

## 测试建议

1. 测试信件展示是否正常
2. 测试阅读确认功能
3. 测试答题流程
4. 测试错误处理和状态重置
5. 测试暗黑模式兼容性
