/*
  合并comments和wcomments分片版本
  1. 读取comments文件
  2. 读取wcomments文件
  3. 合并comments和wcomments
  4. 保存到缓存文件
*/

import { promises as fs } from 'fs'
import path from 'path'
import { app } from 'electron'

const rootDir = app.getAppPath() // 返回 electron-app 根目录（即 package.json 所在目录）

const BATCH_SIZE = 5000
const CONCURRENCY = 100

// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
async function asyncPool(limit, array, iteratorFn) {
  const ret: Promise<void>[] = []
  const executing: Promise<void>[] = []
  for (const item of array) {
    const p = Promise.resolve().then(() => iteratorFn(item))
    ret.push(p)

    if (executing.length >= limit) {
      await Promise.race(executing)
    }

    const e = p.then(() => {
      executing.splice(executing.indexOf(e), 1)
    })
    executing.push(e)
  }
  return Promise.all(ret)
}

export async function MergeCommentsScriptChuck(roomId: string): Promise<any[]> {
  // 路径定义
  const cacheFilePath = path.join(rootDir, `cache/comments/comments_${roomId}.json`)
  const commentsDir = path.join(rootDir, `cache/comments_${roomId}`)
  const wcommentsDir = path.join(rootDir, `cache/wcomments_${roomId}`)

  // 尝试读取缓存文件
  try {
    const cacheContent = await fs.readFile(cacheFilePath, 'utf-8')
    const cachedData = JSON.parse(cacheContent)
    return cachedData // 如果缓存存在，则直接返回
  } catch (err: any) {
    // 确保缓存目录存在
    await fs.mkdir(path.dirname(cacheFilePath), { recursive: true })
    // 确保文件存在
    await fs.writeFile(cacheFilePath, '[]', 'utf-8')
    // 缓存不存在，继续处理
    // 若错误不是文件不存在的错误，抛出
    if (err.code !== 'ENOENT') throw err
  }

  console.time('总耗时')

  const commentFiles = await fs.readdir(commentsDir)

  // 检查 wcomments 目录是否存在
  let wcommentFiles: string[] = []
  try {
    await fs.stat(wcommentsDir)
    wcommentFiles = await fs.readdir(wcommentsDir)
  } catch {
    // wcomments 目录不存在，直接处理 comments
    console.log('没有找到wcommentsDir，直接返回comments')
    const commentsList: any[] = []

    for (let i = 0; i < commentFiles.length; i += BATCH_SIZE) {
      const cBatch = commentFiles.slice(i, i + BATCH_SIZE)
      console.log(`🔹 正在处理第 ${i / BATCH_SIZE + 1} 批 comments`)

      await asyncPool(CONCURRENCY, cBatch, async (file) => {
        const filePath = path.join(commentsDir, file)
        const data = await fs.readFile(filePath, 'utf-8')
        const json = JSON.parse(data)

        if (json.list?.comments?.length) {
          json.list.comments.forEach((comment) => {
            commentsList.push({
              ...comment,
              event_ts: file
            })
          })
        }
      })
    }

    await fs.writeFile(cacheFilePath, JSON.stringify(commentsList, null, 2), 'utf-8')
    console.timeEnd('总耗时')
    console.log('✅ 处理完成')
    return commentsList
  }

  console.log(`🔹 comments 文件数: ${commentFiles.length}`)
  console.log(`🔹 wcomments 文件数: ${wcommentFiles.length}`)

  const commentTagMap = new Map()
  const wcomments: any[] = []

  for (let i = 0; i < commentFiles.length; i += BATCH_SIZE) {
    const cBatch = commentFiles.slice(i, i + BATCH_SIZE)
    const wBatch = wcommentFiles.slice(i, i + BATCH_SIZE)

    console.log(`📦 正在处理第 ${i / BATCH_SIZE + 1} 批 comments`)

    // 1. 读取 comments 批次，构建 Map
    await asyncPool(CONCURRENCY, cBatch, async (file) => {
      const filePath = path.join(commentsDir, file)
      const data = await fs.readFile(filePath, 'utf-8')
      const json = JSON.parse(data)
      if (json.list?.comments?.length) {
        json.list.comments.forEach((comment) => {
          const key = `${comment.nick_name}|||${comment.content}`
          commentTagMap.set(key, comment.comment_tag)
        })
      }
    })

    // 2. 处理当前 batch 的 wcomments
    console.log(`📝 正在处理第 ${i / BATCH_SIZE + 1} 批 wcomments`)
    await asyncPool(CONCURRENCY, wBatch, async (file) => {
      const filePath = path.join(wcommentsDir, file)
      const data = await fs.readFile(filePath, 'utf-8')
      const json = JSON.parse(data)

      const nickName = json.user?.nickName
      const content = json.content

      if (nickName && content) {
        const key = `${nickName}|||${content}`
        const tag = commentTagMap.get(key)

        if (tag !== undefined) {
          json.comment_tag = tag
        }
      }

      // 删除common字段
      delete json.common

      // 添加字段并推入结果数组
      wcomments.push({
        ...json,
        event_ts: json.eventTime,
        nick_name: json.user?.nickName
      })
    })
  }

  // 保存到缓存文件
  await fs.writeFile(cacheFilePath, JSON.stringify(wcomments, null, 2), 'utf-8')

  console.timeEnd('总耗时')
  console.log('✅ 处理完成')
  return wcomments
}

if (require.main === module) {
  MergeCommentsScriptChuck('7524671815537691407')
    .then((res) => {
      console.log(res.length)
    })
    .catch((err) => {
      console.error('❌ 出错:', err)
    })
}
