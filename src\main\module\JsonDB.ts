import * as fs from 'fs'

class JsonDB {
  // 读取数据库文件
  private static readDBFile(dbfile: string): any {
    try {
      // 判断文件是否存在
      if (fs.existsSync(dbfile)) {
        // 读取文件内容
        const data = fs.readFileSync(dbfile, 'utf-8')
        // 将文件内容解析为JSON格式
        return JSON.parse(data)
      }
      // 如果文件不存在，返回空对象
      return {}
    } catch (error) {
      // 如果读取文件时出错，打印错误信息，并返回空对象
      console.error(error)
      return {}
    }
  }

  private static writeDBFile(dbfile: string, data: any): void {
    try {
      const jsonData = JSON.stringify(data, null, 2)
      fs.writeFileSync(dbfile, jsonData, 'utf-8')
    } catch (error) {
      console.error(error)
    }
  }

  public static getItem(dbfile: string, name: string, defaultValue = {}): any {
    const db = this.readDBFile(dbfile)
    if (name in db) {
      return db[name]
    } else {
      return defaultValue
    }
  }

  public static getRecord(dbfile: string): any {
    const db = this.readDBFile(dbfile)
    return db
  }

  public static setItem(dbfile: string, name: string, value: any): void {
    const db = this.readDBFile(dbfile)
    db[name] = value
    this.writeDBFile(dbfile, db)
  }

  public static setRecord(dbfile: string, record: any): void {
    this.writeDBFile(dbfile, record)
  }
}

export default JsonDB
