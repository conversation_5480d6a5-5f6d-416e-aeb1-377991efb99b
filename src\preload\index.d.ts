import { ElectronAPI } from '@electron-toolkit/preload'

declare global {
  interface Window {
    electron: ElectronAPI & {
      ipcRenderer: {
        invoke: (channel: string, ...args: any[]) => Promise<any>
        on: (channel: string, listener: (event: any, ...args: any[]) => void) => void
        once: (channel: string, listener: (event: any, ...args: any[]) => void) => void
        removeListener: (channel: string, listener: (...args: any[]) => void) => void
        removeAllListeners: (channel: string) => void
        send: (channel: string, ...args: any[]) => void
      }
    }
    api: {
      clipboard: {
        writeText: (text: string) => void
        readText: () => string
      }
    }
    /**
     * 开发环境调试工具
     * @仅在开发模式可用
     */
    __debug?: {
      /**
       * 打开版本信息页面 (#/versions)
       */
      openVersions: () => void

      /**
       * 显示当前路由信息
       */
      showRouteInfo: () => void

      /**
       * 显示/隐藏内存监控面板
       */
      showMemoryMonitor: () => void

      /**
       * 隐藏内存监控面板
       */
      hideMemoryMonitor: () => void

      /**
       * 记录内存快照到控制台
       */
      logMemorySnapshot: (label?: string) => void
    }
  }
}
