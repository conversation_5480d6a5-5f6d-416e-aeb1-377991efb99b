/**
 * 进入记录业务服务
 * 提供用户进入记录相关的所有业务处理逻辑
 */
import SqliteDB from '../../module/SqliteDB'
import { Enter } from '../../module/interfaces'
import { Op, Transaction, literal } from 'sequelize'
import { ParseEntryUsers } from '../utils/parseEntryUsers'

// ================================
// 核心业务方法
// ================================

/**
 * 事务批量插入/更新进入记录
 * @param enterDataList 完全符合数据库格式的对象数组
 * @returns 处理结果统计
 */
export async function upsertEnterRecords(enterDataList: Enter[]): Promise<{
  success: boolean
  totalCount: number
  insertedCount: number
  updatedCount: number
  failedRecords: Array<{ index: number; error: string; data: Enter }>
}> {
  const result = {
    success: false,
    totalCount: enterDataList.length,
    insertedCount: 0,
    updatedCount: 0,
    failedRecords: [] as Array<{ index: number; error: string; data: Enter }>
  }

  let transaction: Transaction | null = null

  try {
    const EnterModel = SqliteDB.getEnterModel()
    transaction = await SqliteDB.getTransaction()

    for (let i = 0; i < enterDataList.length; i++) {
      const enterData = enterDataList[i]

      try {
        // 检查是否已存在该用户记录
        const existingRecord = await EnterModel.findOne({
          where: { user_id: enterData.user_id },
          transaction
        })

        if (existingRecord) {
          // 更新现有记录
          await existingRecord.update(
            {
              room_id: enterData.room_id,
              nick_name: enterData.nick_name,
              douyin_level: enterData.douyin_level,
              badge_level: enterData.badge_level,
              enter_count: enterData.enter_count,
              has_profile_video: enterData.has_profile_video,
              has_showcase: enterData.has_showcase,
              follower_count: enterData.follower_count,
              updated_at: new Date().toISOString()
            },
            { transaction }
          )

          result.updatedCount++
        } else {
          // 插入新记录
          await EnterModel.create(
            {
              user_id: enterData.user_id,
              room_id: enterData.room_id,
              nick_name: enterData.nick_name,
              douyin_level: enterData.douyin_level,
              badge_level: enterData.badge_level,
              enter_count: enterData.enter_count || 1,
              has_profile_video: enterData.has_profile_video,
              has_showcase: enterData.has_showcase,
              follower_count: enterData.follower_count
            },
            { transaction }
          )

          result.insertedCount++
        }
      } catch (error: any) {
        result.failedRecords.push({
          index: i,
          error: error.message,
          data: enterData
        })
      }
    }

    // 提交事务
    await transaction.commit()
    result.success = true

    console.log(
      `✓ 批量更新插入完成: 总数 ${result.totalCount}, 插入 ${result.insertedCount}, 更新 ${result.updatedCount}, 失败 ${result.failedRecords.length}`
    )
  } catch (error: any) {
    // 回滚事务
    if (transaction) {
      await transaction.rollback()
    }
    console.error('批量更新插入失败:', error)
    result.success = false

    // 如果是整体失败，将所有记录标记为失败
    if (result.failedRecords.length === 0) {
      result.failedRecords = enterDataList.map((data, index) => ({
        index,
        error: error.message,
        data
      }))
    }
  }

  return result
}

/**
 * 按房间ID查询进入记录，支持多条件筛选（包含分页和总数信息）
 * @param roomId 房间ID（必需）
 * @param filters 筛选条件
 * @returns 包含数据、总数和分页信息的结果
 */
export async function getEnterRecordsByRoom(
  roomId: string,
  filters?: {
    douyin_level?: { min?: number; max?: number; exact?: number }
    badge_level?: { min?: number; max?: number; exact?: number }
    enter_count?: { min?: number; max?: number; exact?: number }
    has_profile_video?: boolean
    has_showcase?: boolean
    follower_count?: { min?: number; max?: number; exact?: number }
    limit?: number
    offset?: number
    order_by?:
      | 'douyin_level'
      | 'badge_level'
      | 'enter_count'
      | 'follower_count'
      | 'created_at'
      | 'updated_at'
    order_direction?: 'ASC' | 'DESC'
  }
): Promise<{
  data: Enter[]
  total: number
  currentPage: number
  pageSize: number
  totalPages: number
}> {
  try {
    const EnterModel = SqliteDB.getEnterModel()
    const whereConditions: any = {
      room_id: roomId // 固定按房间ID查询
    }

    // 构建筛选条件
    if (filters?.douyin_level) {
      if (filters.douyin_level.exact !== undefined) {
        whereConditions.douyin_level = filters.douyin_level.exact
      } else {
        const levelCondition: any = {}
        if (filters.douyin_level.min !== undefined) {
          levelCondition[Op.gte] = filters.douyin_level.min
        }
        if (filters.douyin_level.max !== undefined) {
          levelCondition[Op.lte] = filters.douyin_level.max
        }
        // {{ AURA-X: Fix - 修复Symbol属性检测问题. Approval: 寸止. }}
        if (filters.douyin_level.min !== undefined || filters.douyin_level.max !== undefined) {
          whereConditions.douyin_level = levelCondition
        }
      }
    }

    if (filters?.badge_level) {
      if (filters.badge_level.exact !== undefined) {
        whereConditions.badge_level = filters.badge_level.exact
      } else {
        const badgeCondition: any = {}
        if (filters.badge_level.min !== undefined) {
          badgeCondition[Op.gte] = filters.badge_level.min
        }
        if (filters.badge_level.max !== undefined) {
          badgeCondition[Op.lte] = filters.badge_level.max
        }
        // {{ AURA-X: Fix - 修复Symbol属性检测问题. Approval: 寸止. }}
        if (filters.badge_level.min !== undefined || filters.badge_level.max !== undefined) {
          whereConditions.badge_level = badgeCondition
        }
      }
    }

    if (filters?.enter_count) {
      if (filters.enter_count.exact !== undefined) {
        whereConditions.enter_count = filters.enter_count.exact
      } else {
        const enterCondition: any = {}
        if (filters.enter_count.min !== undefined) {
          enterCondition[Op.gte] = filters.enter_count.min
        }
        if (filters.enter_count.max !== undefined) {
          enterCondition[Op.lte] = filters.enter_count.max
        }
        // {{ AURA-X: Fix - 修复Symbol属性检测问题. Approval: 寸止. }}
        if (filters.enter_count.min !== undefined || filters.enter_count.max !== undefined) {
          whereConditions.enter_count = enterCondition
        }
      }
    }

    if (filters?.follower_count) {
      if (filters.follower_count.exact !== undefined) {
        whereConditions.follower_count = filters.follower_count.exact
      } else {
        const followerCondition: any = {}
        if (filters.follower_count.min !== undefined) {
          followerCondition[Op.gte] = filters.follower_count.min
        }
        if (filters.follower_count.max !== undefined) {
          followerCondition[Op.lte] = filters.follower_count.max
        }
        // {{ AURA-X: Fix - 修复Symbol属性检测问题. Approval: 寸止. }}
        if (filters.follower_count.min !== undefined || filters.follower_count.max !== undefined) {
          whereConditions.follower_count = followerCondition
        }
      }
    }

    if (filters?.has_profile_video !== undefined) {
      whereConditions.has_profile_video = filters.has_profile_video
    }

    if (filters?.has_showcase !== undefined) {
      whereConditions.has_showcase = filters.has_showcase
    }

    // 分页参数
    const limit = filters?.limit || 100
    const offset = filters?.offset || 0
    const currentPage = Math.floor(offset / limit) + 1

    // 排序配置
    const orderBy = filters?.order_by || 'enter_count'
    const orderDirection = filters?.order_direction || 'DESC'

    // 并发执行数据查询和总数查询
    const [enterRecords, totalCount] = await Promise.all([
      EnterModel.findAll({
        where: whereConditions,
        limit,
        offset,
        order: [[orderBy, orderDirection]]
      }),
      EnterModel.count({
        where: whereConditions
      })
    ])

    const totalPages = Math.ceil(totalCount / limit)

    return {
      data: enterRecords.map((record) => record.toJSON() as Enter),
      total: totalCount,
      currentPage,
      pageSize: limit,
      totalPages
    }
  } catch (error: any) {
    console.error(`获取房间 ${roomId} 的进入记录失败:`, error)
    return {
      data: [],
      total: 0,
      currentPage: 1,
      pageSize: filters?.limit || 100,
      totalPages: 0
    }
  }
}

// ================================
// 简化的辅助方法
// ================================

/**
 * 根据用户ID获取进入记录
 * @param userId 用户ID
 * @returns 用户进入记录
 */
export async function getEnterByUserId(userId: string): Promise<Enter | null> {
  try {
    const EnterModel = SqliteDB.getEnterModel()
    const enterRecord = await EnterModel.findOne({
      where: { user_id: userId }
    })

    return enterRecord ? (enterRecord.toJSON() as Enter) : null
  } catch (error: any) {
    console.error(`获取用户 ${userId} 的进入记录失败:`, error)
    return null
  }
}

/**
 * 获取房间进入统计信息（支持筛选条件）
 * @param roomId 房间ID
 * @param filters 筛选条件（与 getEnterRecordsByRoom 相同）
 * @returns 统计信息
 */
export async function getRoomEnterStatistics(
  roomId: string,
  filters?: {
    douyin_level?: { min?: number; max?: number; exact?: number }
    badge_level?: { min?: number; max?: number; exact?: number }
    enter_count?: { min?: number; max?: number; exact?: number }
    has_profile_video?: boolean
    has_showcase?: boolean
    follower_count?: { min?: number; max?: number; exact?: number }
  }
): Promise<{
  totalUsers: number
  totalEnters: number
  avgEnterCount: number
  usersWithProfileVideo: number
  usersWithShowcase: number
}> {
  try {
    const EnterModel = SqliteDB.getEnterModel()

    // 构建基础 where 条件
    const baseWhere: any = { room_id: roomId }

    // 应用筛选条件（复用 getEnterRecordsByRoom 的筛选逻辑）
    if (filters) {
      // 抖音等级筛选
      if (filters.douyin_level) {
        if (filters.douyin_level.exact !== undefined) {
          baseWhere.douyin_level = filters.douyin_level.exact
        } else {
          const douyinConditions: any = {}
          if (filters.douyin_level.min !== undefined) {
            douyinConditions[Op.gte] = filters.douyin_level.min
          }
          if (filters.douyin_level.max !== undefined) {
            douyinConditions[Op.lte] = filters.douyin_level.max
          }
          // {{ AURA-X: Fix - 修复Symbol属性检测问题. Approval: 寸止. }}
          if (filters.douyin_level.min !== undefined || filters.douyin_level.max !== undefined) {
            baseWhere.douyin_level = douyinConditions
          }
        }
      }

      // 粉丝团等级筛选
      if (filters.badge_level) {
        if (filters.badge_level.exact !== undefined) {
          baseWhere.badge_level = filters.badge_level.exact
        } else {
          const badgeConditions: any = {}
          if (filters.badge_level.min !== undefined) {
            badgeConditions[Op.gte] = filters.badge_level.min
          }
          if (filters.badge_level.max !== undefined) {
            badgeConditions[Op.lte] = filters.badge_level.max
          }
          // {{ AURA-X: Fix - 修复Symbol属性检测问题. Approval: 寸止. }}
          if (filters.badge_level.min !== undefined || filters.badge_level.max !== undefined) {
            baseWhere.badge_level = badgeConditions
          }
        }
      }

      // 进入次数筛选
      if (filters.enter_count) {
        if (filters.enter_count.exact !== undefined) {
          baseWhere.enter_count = filters.enter_count.exact
        } else {
          const enterCountConditions: any = {}
          if (filters.enter_count.min !== undefined) {
            enterCountConditions[Op.gte] = filters.enter_count.min
          }
          if (filters.enter_count.max !== undefined) {
            enterCountConditions[Op.lte] = filters.enter_count.max
          }
          // {{ AURA-X: Fix - 修复Symbol属性检测问题. Approval: 寸止. }}
          // 使用 Object.getOwnPropertySymbols 或直接检查条件值来判断是否有筛选条件
          if (filters.enter_count.min !== undefined || filters.enter_count.max !== undefined) {
            baseWhere.enter_count = enterCountConditions
          }
        }
      }

      // 有视频筛选
      if (filters.has_profile_video !== undefined) {
        baseWhere.has_profile_video = filters.has_profile_video
      }

      // 有橱窗
      if (filters.has_showcase !== undefined) {
        baseWhere.has_showcase = filters.has_showcase
      }

      // 粉丝数筛选
      if (filters.follower_count) {
        if (filters.follower_count.exact !== undefined) {
          baseWhere.follower_count = filters.follower_count.exact
        } else {
          const followerConditions: any = {}
          if (filters.follower_count.min !== undefined) {
            followerConditions[Op.gte] = filters.follower_count.min
          }
          if (filters.follower_count.max !== undefined) {
            followerConditions[Op.lte] = filters.follower_count.max
          }
          // {{ AURA-X: Fix - 修复Symbol属性检测问题. Approval: 寸止. }}
          if (
            filters.follower_count.min !== undefined ||
            filters.follower_count.max !== undefined
          ) {
            baseWhere.follower_count = followerConditions
          }
        }
      }
    }

    // 并发执行统计查询
    const [totalUsers, totalEnters, avgEnterCount, usersWithProfileVideo, usersWithShowcase] =
      await Promise.all([
        // 总用户数
        EnterModel.count({ where: baseWhere }),
        // 总进入次数
        EnterModel.sum('enter_count', { where: baseWhere }),
        // 平均进入次数
        EnterModel.findOne({
          where: baseWhere,
          attributes: [[literal('AVG(enter_count)'), 'avg']]
        }),
        // 有头像视频的用户数
        EnterModel.count({ where: { ...baseWhere, has_profile_video: true } }),
        // 有展示柜的用户数
        EnterModel.count({ where: { ...baseWhere, has_showcase: true } })
      ])

    // 调试信息
    console.log(`📊 房间 ${roomId} 统计查询:`, {
      baseWhere,
      filters,
      totalUsers,
      totalEnters,
      avgEnterCount: (avgEnterCount as any)?.dataValues?.avg
    })

    return {
      totalUsers: totalUsers || 0,
      totalEnters: totalEnters || 0,
      avgEnterCount: parseFloat((avgEnterCount as any)?.dataValues?.avg || '0'),
      usersWithProfileVideo: usersWithProfileVideo || 0,
      usersWithShowcase: usersWithShowcase || 0
    }
  } catch (error: any) {
    console.error(`获取房间 ${roomId} 进入统计信息失败:`, error)
    return {
      totalUsers: 0,
      totalEnters: 0,
      avgEnterCount: 0,
      usersWithProfileVideo: 0,
      usersWithShowcase: 0
    }
  }
}

/**
 * 清空房间的所有进入记录
 * @param roomId 房间ID
 * @param confirm 确认标志，必须为true
 * @returns 清空是否成功
 */
export async function clearRoomEnterRecords(
  roomId: string,
  confirm: boolean = false
): Promise<boolean> {
  if (!confirm) {
    console.warn(`❗ 尝试清空房间 ${roomId} 的数据但未确认，操作被拒绝`)
    return false
  }

  try {
    const EnterModel = SqliteDB.getEnterModel()
    const deletedCount = await EnterModel.destroy({
      where: { room_id: roomId }
    })

    console.log(`✓ 已清空房间 ${roomId} 的 ${deletedCount} 条进入记录`)
    return true
  } catch (error: any) {
    console.error(`清空房间 ${roomId} 数据失败:`, error)
    return false
  }
}

// ================================
// 新增：数据检查和处理方法
// ================================

/**
 * 检查房间数据是否存在于数据库中
 * @param roomId 房间ID
 * @returns 是否存在数据
 */
export async function checkRoomDataExists(roomId: string): Promise<boolean> {
  try {
    const EnterModel = SqliteDB.getEnterModel()
    const count = await EnterModel.count({
      where: { room_id: roomId }
    })

    console.log(`🔍 房间 ${roomId} 数据检查: ${count > 0 ? '存在' : '不存在'} (${count} 条记录)`)
    return count > 0
  } catch (error: any) {
    console.error(`检查房间 ${roomId} 数据失败:`, error)
    return false
  }
}

/**
 * 处理并存储房间数据（从文件读取）
 * @param roomId 房间ID
 * @returns 处理结果
 */
export async function processAndStoreRoomData(roomId: string): Promise<{
  success: boolean
  processedCount: number
  insertedCount: number
  error?: string
}> {
  const result = {
    success: false,
    processedCount: 0,
    insertedCount: 0,
    error: undefined as string | undefined
  }

  try {
    console.log(`📁 开始处理房间 ${roomId} 的数据...`)

    // 读取文件数据
    const rawData = await ParseEntryUsers(roomId)

    if (!rawData || rawData.length === 0) {
      console.log(`📁 房间 ${roomId} 没有找到文件数据，跳过处理`)
      result.success = true // 没有文件也算成功，因为用户要求跳过
      return result
    }

    // 存储到数据库
    const upsertResult = await upsertEnterRecords(rawData)

    if (upsertResult.success) {
      result.success = true
      result.insertedCount = upsertResult.insertedCount + upsertResult.updatedCount
      console.log(
        `✓ 房间 ${roomId} 数据处理完成: 处理 ${result.processedCount} 条，存储 ${result.insertedCount} 条`
      )
    } else {
      result.error = `数据存储失败: ${upsertResult.failedRecords.length} 条记录失败`
      console.error(`❌ 房间 ${roomId} 数据存储失败:`, result.error)
    }
  } catch (error: any) {
    result.error = error.message
    console.error(`❌ 房间 ${roomId} 数据处理失败:`, error)
  }

  return result
}
