# 公告系统实现

## 功能描述

实现了一个完整的公告弹窗系统，允许后端发送带标题和内容的公告，前端接收后自动显示弹窗。

## 实现文件

### 1. 前端组件

- `src/renderer/src/components/modals/AnnouncementModal.vue` - 公告弹窗组件
- `src/renderer/src/composables/useAnnouncement.ts` - 公告状态管理

### 2. 后端服务

- `src/main/Service/LZYService.ts` - 添加了公告发送功能

### 3. 应用集成

- `src/renderer/src/App.vue` - 集成公告监听和显示

## 使用方法

### 后端发送公告

```typescript
// 在任何地方调用LZYService的静态方法
LZYService.sendAnnouncement('公告标题', '公告内容\n支持多行文本')
```

### 测试公告功能

在浏览器开发者工具的控制台中执行：

```javascript
// 发送测试公告
window.electron.ipcRenderer
  .invoke('test:sendAnnouncement', {
    title: '测试公告',
    content: '这是一条测试公告\n用于验证公告系统是否正常工作'
  })
  .then((result) => {
    console.log('发送结果:', result)
  })

// 发送欢迎公告
window.electron.ipcRenderer
  .invoke('test:sendAnnouncement', {
    title: '欢迎使用',
    content: '欢迎使用我们的应用程序！\n\n主要功能：\n- 直播数据分析\n- 订单管理\n- 字幕编辑'
  })
  .then((result) => {
    console.log('发送结果:', result)
  })

// 发送系统维护公告
window.electron.ipcRenderer
  .invoke('test:sendAnnouncement', {
    title: '系统维护通知',
    content:
      '系统将在今晚23:00-01:00进行维护，请提前保存数据。\n\n维护期间可能出现的情况：\n- 数据同步延迟\n- 部分功能暂时不可用\n\n感谢您的理解与配合！'
  })
  .then((result) => {
    console.log('发送结果:', result)
  })
```

## 技术特性

1. **响应式设计** - 弹窗在暗黑/明亮主题下均有良好表现
2. **多行支持** - 公告内容支持换行符自动转换为HTML换行
3. **全局监听** - 在App.vue中实现全局事件监听
4. **Bootstrap兼容** - 使用Bootstrap Modal确保UI一致性
5. **类型安全** - 完整的TypeScript类型定义

## 架构说明

### IPC通信流程

```
后端LZYService.sendAnnouncement()
  → BrowserWindow.webContents.send('announcement', data)
    → 前端ipcRenderer.on('announcement', callback)
      → useAnnouncement.showAnnouncement()
        → Bootstrap Modal显示
```

### 事件处理

- 后端通过`BrowserWindow.getAllWindows()`向所有窗口广播
- 前端通过`useAnnouncement` composable统一管理状态
- 弹窗使用Bootstrap Modal API控制显示/隐藏

## 扩展可能

1. **公告队列** - 支持多个公告排队显示
2. **公告历史** - 记录已显示的公告
3. **用户确认** - 记录用户是否已读
4. **定时公告** - 支持定时发送公告
5. **公告类型** - 支持不同类型的公告（警告、信息、成功等）
