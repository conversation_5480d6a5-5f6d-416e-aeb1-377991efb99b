# CanvasFunnel组件优化任务

## 任务背景

优化现有的CanvasFunnel.vue组件，减少自定义类名，使用Bootstrap和内联样式，为每个数据项添加El-Tooltip。

## 优化目标

1. 使用Bootstrap 5工具类替代自定义CSS
2. 为每个数据项添加El-Tooltip，显示中位值信息
3. 简化类名结构，保持现有布局效果
4. 使用硬编码中位值数据

## 执行计划

- [x] 分析现有代码结构
- [x] 优化模板结构，添加Bootstrap类
- [x] 为数据项添加El-Tooltip
- [x] 重构样式，移除冗余CSS
- [x] 添加中位值数据
- [x] 修复TypeScript类型错误
- [x] 为所有转化率添加El-Tooltip
- [x] 补充完整的中位值数据（包含转化率数据）
- [x] 更正成交人数和内容互动人数的中位值数据
- [x] 统一所有Tooltip的显示位置为top
- [x] 为成交人数和内容互动人数定制详细的tooltip内容
- [x] 测试验证功能

## 技术要点

- Bootstrap 5 已安装并可用
- Element Plus 已安装并可用
- 保留背景图片和特殊定位
- 使用内联样式处理精确定位
