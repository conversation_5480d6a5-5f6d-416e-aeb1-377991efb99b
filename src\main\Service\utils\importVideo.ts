import JsonDB from '../../module/JsonDB'
import * as fs from 'fs'
import path from 'path'
import API from '../../module/API'
import { getAPPBasePath } from '../../module/API'
import { insertRoomDataById } from '../business/RoomService'

function formatTimestampToBeijingTime(tsInSeconds) {
  const date = new Date(tsInSeconds * 1000)
  const beijingDate = new Date(date.getTime() + 8 * 60 * 60 * 1000)
  const year = beijingDate.getUTCFullYear()
  const month = String(beijingDate.getUTCMonth() + 1).padStart(2, '0')
  const day = String(beijingDate.getUTCDate()).padStart(2, '0')
  const hour = String(beijingDate.getUTCHours()).padStart(2, '0')
  const minute = String(beijingDate.getUTCMinutes()).padStart(2, '0')
  // const second = String(beijingDate.getUTCSeconds()).padStart(2, '0')
  // :${second}
  return `${year}/${month}/${day} ${hour}:${minute}`
}

/**
 * 把视频文件导入
 * @param date 直播时间
 * @param title 直播标题
 * @param flv 视频文件路径
 * @param startTime 开播时间
 * @param endTime 结束时间
 * @returns 需要把 data 、title 、flv 写进roomdata中
 */
export async function importFlv(
  date: any,
  title: string,
  flv: string,
  startTime?: string,
  endTime?: string
): Promise<any> {
  // 获取根目录
  const basePath = getAPPBasePath()
  const videoDir = path.join(basePath, `cache/video_${date}`)
  // 检查路径是否存在，不存在则创建
  if (!fs.existsSync(videoDir)) {
    fs.mkdirSync(videoDir, { recursive: true }) // 递归创建目录
    console.log(`目录已创建：${videoDir}`)
  }

  const video2m4aPath = fs.existsSync(path.join(videoDir, 'video.m4a'))
  const m4a_path_url = path.join(videoDir, 'video.m4a')

  // 构造完整对象
  const roomData = {
    nickname: title,
    live_end_time: endTime || '',
    live_end_ts: endTime ? new Date(endTime).getTime() / 1000 : '',
    live_start_time: startTime || formatTimestampToBeijingTime(date),
    live_start_ts: startTime ? new Date(startTime).getTime() / 1000 : date
  }

  JsonDB.setItem(`cache/room_${date}`, 'live_base_info', roomData)
  JsonDB.setItem(`cache/room_${date}`, 'videolink', flv)
  JsonDB.setItem(`cache/room_${date}`, 'room_id', date)

  const ifsegs = JsonDB.getItem(`cache/room_${date}`, 'segs', [])

  if (!ifsegs.length) {
    try {
      // 判断音频文件是否存在
      if (!video2m4aPath) {
        await API.video2m4a(flv, m4a_path_url)
      }
      const audio2seg = await API.audio2seg(m4a_path_url)
      JsonDB.setItem(`cache/room_${date}`, 'segs', audio2seg)
    } catch (e) {
      console.error('处理音视频失败:', e)
    }
  }
  await insertRoomDataById(date)
}
