# 直播分析跳转商品详情功能

## 需求描述

在直播分析页面的表格操作列中添加"商品详情"按钮，点击后：

1. 跳转到商品分析页面
2. 自动设置筛选条件：
   - 流量渠道筛选：直播
   - 时间范围筛选：该条直播的开播时间到结束时间
   - 基于数据字段：start_time_ts 和 end_time_ts

## 实现方案

**方案**：URL参数传递

- 通过路由query参数传递筛选条件
- 在商品分析页面监听路由参数并自动设置筛选

## 涉及文件

1. `src/renderer/src/views/dashboard/components/DashboardTable.vue` - 添加商品详情按钮
2. `src/renderer/src/views/product/components/ProductTable.vue` - 接收参数并设置筛选

## 实现详情

### 1. DashboardTable.vue 修改

- **操作列按钮**：添加"商品详情"按钮（btn-outline-success样式）
- **路由生成函数**：`generateProductAnalysisRoute(row)`
  - 所有参数通过 state 传递，不使用 query
  - state 包含：channel, startTime, endTime, liveId

### 2. ProductTable.vue 修改

- **参数应用**：`applyRouteFilters()` 函数
  - 从 history.state 获取所有参数
  - 只在组件挂载时检查一次，无需持续监听
  - 自动设置 channelFilter 和 dateTimeRange 的值
- **精确时间筛选**：
  - 添加 startTime/endTime 响应式变量存储时间戳
  - 在 filteredOrderList 中优先使用精确时间戳筛选
  - 添加 formatTimestamp 函数和用户提示
- **UI升级**：
  - 将日期选择器改为时间日期选择器（datetimerange）
  - 支持用户手动选择精确到秒的时间范围
  - 手动修改时间时清除来源时间戳，避免冲突
  - 优化来源提示：使用小标签+tooltip的方式显示
  - 添加清除所有筛选条件的按钮
- **生命周期**：只在 onMounted 中调用参数应用函数

## 执行状态

- [x] 需求分析
- [x] 方案设计
- [x] DashboardTable.vue 添加商品详情按钮
- [x] DashboardTable.vue 添加路由生成函数
- [x] ProductTable.vue 添加路由监听
- [x] ProductTable.vue 实现参数自动应用
- [x] 支持精确时间筛选（时间戳级别）
- [x] 添加精确时间筛选的用户提示
- [x] 升级为时间日期选择器（datetimerange）
- [x] 清理startDate/endDate冗余逻辑
- [x] 优化来源提示UI（标签+tooltip）
- [x] 添加清除所有筛选条件按钮
- [x] 实现页面刷新后不保留跳转逻辑（路由state方案）
- [x] 移除冗余的fromLiveAnalysis标识（极简化）
- [ ] 功能测试验证

## 参数格式

```javascript
// 路由state参数（极简方案）
{
  channel: '直播',           // 流量渠道
  startTime: 1704067200,     // 精确开播时间戳 (秒)
  endTime: 1704070800,       // 精确结束时间戳 (秒)
  liveId: 'live_123'         // 直播ID (可选，用于调试)
}
```

## 筛选逻辑

1. **参数检测**：通过检查 `history.state.startTime/endTime` 判断是否有传递的参数
2. **精确时间筛选**：使用 startTime 和 endTime 时间戳进行精确筛选
3. **UI显示**：将时间戳转换为 datetime 字符串显示在时间日期选择器中
4. **用户提示**：显示来源信息："来源：直播分析页面 (具体时间范围)"
5. **手动修改**：用户修改时间选择器时，清除来源时间戳，使用用户选择的时间范围
6. **刷新处理**：页面刷新后 state 自动清除，不再应用跳转参数
