<template>
  <div class="banned-words-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-title-wrapper">
        <div class="page-icon">
          <i class="bi bi-shield-exclamation text-primary"></i>
        </div>
        <div class="page-title-content">
          <h1 class="page-title mb-1">违禁词设置</h1>
          <p class="page-subtitle text-muted mb-0">管理系统违禁词库，保护平台内容健康</p>
        </div>
      </div>
    </div>

    <!-- 违禁词管理内容 -->
    <div class="banned-words-content">
      <div class="row g-4">
        <!-- 添加违禁词卡片 -->
        <div class="col-12">
          <div class="card shadow-sm border-0">
            <div class="card-header bg-transparent border-0 py-3">
              <h5 class="card-title mb-0 d-flex align-items-center">
                <i class="bi bi-plus-circle me-2 text-primary"></i>
                添加违禁词
              </h5>
              <p class="text-muted small mt-1 mb-0">批量添加违禁词，支持直接粘贴</p>
            </div>
            <div class="card-body">
              <div class="row g-3">
                <!-- 批量输入 -->
                <div class="col-12">
                  <label for="newWord" class="form-label fw-medium">
                    <i class="bi bi-type me-1"></i>违禁词内容
                  </label>
                  <textarea
                    id="newWord"
                    v-model="newWord"
                    class="form-control mb-3"
                    rows="4"
                    placeholder="请输入违禁词，支持：&#10;• 多个违禁词用逗号分隔（如：词1,词2,词3）&#10;• 直接粘贴大量内容&#10;• 按回车键快速添加"
                    @keyup.enter="addWords"
                    @paste="handlePaste"
                    style="resize: vertical; min-height: 100px"
                  ></textarea>
                  <div class="d-flex justify-content-end">
                    <button
                      type="button"
                      class="btn btn-primary"
                      :disabled="!newWord.trim()"
                      @click="addWords"
                    >
                      <i class="bi bi-plus-lg me-1"></i>添加违禁词
                    </button>
                  </div>
                  <div class="form-text mt-2">
                    <i class="bi bi-info-circle me-1"></i>
                    支持中英文逗号分隔，自动去重，可直接粘贴大量内容
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 违禁词列表卡片 -->
        <div class="col-12">
          <div class="card shadow-sm border-0">
            <div
              class="card-header bg-transparent border-0 py-3 d-flex justify-content-between align-items-center"
            >
              <div>
                <h5 class="card-title mb-0 d-flex align-items-center">
                  <i class="bi bi-list-ul me-2 text-primary"></i>
                  违禁词列表
                </h5>
                <p class="text-muted small mt-1 mb-0">当前共 {{ filteredWords.length }} 个违禁词</p>
              </div>
              <div class="d-flex gap-2">
                <!-- 搜索框 -->
                <div class="input-group" style="width: 300px">
                  <span class="input-group-text bg-transparent">
                    <i class="bi bi-search"></i>
                  </span>
                  <input
                    v-model="searchKeyword"
                    type="text"
                    class="form-control"
                    placeholder="搜索违禁词..."
                  />
                </div>
                <!-- 清空按钮 -->
                <button
                  type="button"
                  class="btn btn-outline-danger"
                  :disabled="bannedWords.length === 0"
                  @click="clearAllWords"
                >
                  <i class="bi bi-trash me-1"></i>清空全部
                </button>
              </div>
            </div>
            <div class="card-body p-0">
              <!-- 违禁词列表 -->
              <div v-if="filteredWords.length > 0" class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th style="width: 60px">#</th>
                      <th>违禁词</th>
                      <th style="width: 150px">添加时间</th>
                      <th style="width: 100px">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(word, index) in paginatedWords" :key="word.id">
                      <td class="text-muted">{{ (currentPage - 1) * pageSize + index + 1 }}</td>
                      <td>
                        <span class="badge bg-light text-dark border">{{ word.content }}</span>
                      </td>
                      <td class="text-muted small">{{ formatDate(word.createdAt) }}</td>
                      <td>
                        <button
                          type="button"
                          class="btn btn-outline-danger btn-sm"
                          @click="removeWord(word.id)"
                        >
                          <i class="bi bi-trash"></i>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <!-- 空状态 -->
              <div v-else class="text-center py-5">
                <i class="bi bi-search text-muted" style="font-size: 3rem"></i>
                <p class="text-muted mt-3 mb-0">
                  {{ searchKeyword ? '未找到匹配的违禁词' : '暂无违禁词，请添加' }}
                </p>
              </div>
            </div>
            <!-- 分页 -->
            <div v-if="filteredWords.length > pageSize" class="card-footer bg-transparent border-0">
              <nav>
                <ul class="pagination pagination-sm justify-content-center mb-0">
                  <li class="page-item" :class="{ disabled: currentPage === 1 }">
                    <button class="page-link" @click="currentPage = Math.max(1, currentPage - 1)">
                      上一页
                    </button>
                  </li>
                  <li
                    v-for="page in visiblePages"
                    :key="page"
                    class="page-item"
                    :class="{ active: page === currentPage }"
                  >
                    <button
                      v-if="typeof page === 'number'"
                      class="page-link"
                      @click="currentPage = page"
                    >
                      {{ page }}
                    </button>
                    <span v-else class="page-link disabled">{{ page }}</span>
                  </li>
                  <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                    <button
                      class="page-link"
                      @click="currentPage = Math.min(totalPages, currentPage + 1)"
                    >
                      下一页
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>

        <!-- 统计信息卡片 -->
        <div class="col-12">
          <div class="card shadow-sm border-0">
            <div class="card-header bg-transparent border-0 py-3">
              <h5 class="card-title mb-0 d-flex align-items-center">
                <i class="bi bi-bar-chart me-2 text-primary"></i>
                统计信息
              </h5>
            </div>
            <div class="card-body pt-4">
              <div class="row g-3">
                <div class="col-md-3">
                  <div class="text-center p-4 bg-light rounded">
                    <div class="text-primary mb-3 fw-bold">{{ bannedWords.length }}</div>
                    <div class="text-muted fw-medium">总违禁词数</div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="text-center p-4 bg-light rounded">
                    <div class="text-success mb-3 fw-bold">{{ todayAddedCount }}</div>
                    <div class="text-muted fw-medium">今日新增</div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="text-center p-4 bg-light rounded">
                    <div class="text-info mb-3 fw-bold">{{ averageLength }}</div>
                    <div class="text-muted fw-medium">平均长度</div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="text-center p-4 bg-light rounded">
                    <div class="text-warning mb-3 fw-bold">{{ lastUpdateTime }}</div>
                    <div class="text-muted fw-medium">最后更新</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 违禁词数据结构
interface BannedWord {
  id: string
  content: string
  createdAt: Date
}

// 响应式数据
const bannedWords = ref<BannedWord[]>([])
const newWord = ref('')
const searchKeyword = ref('')

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)

// 计算属性
const filteredWords = computed(() => {
  if (!searchKeyword.value.trim()) {
    return bannedWords.value
  }
  return bannedWords.value.filter((word) =>
    word.content.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const totalPages = computed(() => Math.ceil(filteredWords.value.length / pageSize.value))

const paginatedWords = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredWords.value.slice(start, end)
})

const visiblePages = computed((): (number | string)[] => {
  const pages: (number | string)[] = []
  const total = totalPages.value
  const current = currentPage.value

  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    } else if (current >= total - 3) {
      pages.push(1)
      pages.push('...')
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      pages.push(1)
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    }
  }

  return pages
})

// 统计信息
const todayAddedCount = computed(() => {
  const today = new Date().toDateString()
  return bannedWords.value.filter((word) => new Date(word.createdAt).toDateString() === today)
    .length
})

const averageLength = computed(() => {
  if (bannedWords.value.length === 0) return 0
  const totalLength = bannedWords.value.reduce((sum, word) => sum + word.content.length, 0)
  return Math.round((totalLength / bannedWords.value.length) * 10) / 10
})

const lastUpdateTime = computed(() => {
  if (bannedWords.value.length === 0) return '未更新'
  const lastWord = bannedWords.value.reduce((latest, word) =>
    new Date(word.createdAt) > new Date(latest.createdAt) ? word : latest
  )
  return formatDate(lastWord.createdAt)
})

// 方法
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

const formatDate = (date: Date): string => {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const addWords = async (): Promise<void> => {
  const input = newWord.value.trim()
  if (!input) {
    ElMessage.warning('请输入违禁词内容')
    return
  }

  // 按逗号分割并清理空白字符（支持中英文逗号）
  const words = input
    .split(/[,，]/)
    .map((word) => word.trim())
    .filter((word) => word.length > 0)

  if (words.length === 0) {
    ElMessage.warning('请输入有效的违禁词')
    return
  }

  let addedCount = 0
  let duplicateCount = 0

  words.forEach((word) => {
    // 检查重复
    if (!bannedWords.value.some((item) => item.content === word)) {
      const newBannedWord: BannedWord = {
        id: generateId(),
        content: word,
        createdAt: new Date()
      }
      bannedWords.value.unshift(newBannedWord)
      addedCount++
    } else {
      duplicateCount++
    }
  })

  newWord.value = ''

  if (addedCount > 0) {
    await saveBannedWords()
    ElMessage.success(
      `添加完成：新增 ${addedCount} 个违禁词${duplicateCount > 0 ? `，跳过 ${duplicateCount} 个重复项` : ''}`
    )
  } else if (duplicateCount > 0) {
    ElMessage.warning('所有违禁词都已存在')
  }
}

const removeWord = async (id: string): Promise<void> => {
  try {
    await ElMessageBox.confirm('确定要删除这个违禁词吗？', '删除确认', {
      type: 'warning',
      confirmButtonText: '删除',
      cancelButtonText: '取消'
    })

    bannedWords.value = bannedWords.value.filter((word) => word.id !== id)
    await saveBannedWords()
    ElMessage.success('违禁词删除成功')
  } catch {
    // 用户取消删除
  }
}

const clearAllWords = async (): Promise<void> => {
  try {
    await ElMessageBox.confirm(
      `确定要清空所有 ${bannedWords.value.length} 个违禁词吗？`,
      '清空确认',
      {
        type: 'warning',
        confirmButtonText: '确定清空',
        cancelButtonText: '取消'
      }
    )

    bannedWords.value = []
    currentPage.value = 1
    await saveBannedWords()
    ElMessage.success('违禁词已全部清空')
  } catch {
    // 用户取消清空
  }
}

// {{ AURA-X: Delete - 移除文件导入功能，改用富文本输入框. Approval: 寸止(ID:1738169200). }}

const saveBannedWords = async (): Promise<void> => {
  try {
    // 转换Date对象为字符串，以便通过IPC传递
    const serializedWords = bannedWords.value.map((word) => ({
      ...word,
      createdAt: word.createdAt.toISOString()
    }))

    const result = await window.electron.ipcRenderer.invoke(
      'config:setBannedWords',
      serializedWords
    )
    if (!result.success) {
      throw new Error(result.error)
    }
  } catch (error) {
    console.error('保存违禁词失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

const loadBannedWords = async (): Promise<void> => {
  try {
    const result = await window.electron.ipcRenderer.invoke('config:getBannedWords')
    if (result.success && result.words) {
      bannedWords.value = result.words.map((word: any) => ({
        ...word,
        createdAt: new Date(word.createdAt)
      }))
    }
  } catch (error) {
    console.error('加载违禁词失败:', error)
  }
}

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent): void => {
  // 防止默认粘贴行为
  event.preventDefault()

  // 获取粘贴的文本
  const pastedText = event.clipboardData?.getData('text') || ''

  // 如果输入框已有内容，添加逗号分隔（支持中英文逗号）
  const currentValue = newWord.value.trim()
  if (currentValue && !currentValue.endsWith(',') && !currentValue.endsWith('，')) {
    newWord.value = currentValue + ',' + pastedText
  } else {
    newWord.value = currentValue + pastedText
  }
}

// 组件挂载时加载数据
onMounted(async () => {
  await loadBannedWords()
})
</script>

<style scoped>
.banned-words-container {
  padding: 24px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-title-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #2c3e50;
}

.page-subtitle {
  font-size: 1rem;
}

.banned-words-content {
  max-width: 1200px;
}

.card {
  border-radius: 12px;
  overflow: hidden;
}

.card-header {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.card-title {
  font-weight: 600;
  color: #2c3e50;
}

.form-label {
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-control {
  border-radius: 8px;
  border: 1px solid #dee2e6;
  transition:
    border-color 0.2s ease,
    box-shadow 0.2s ease;
  box-shadow: 0 0 0 0 transparent;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.15rem rgba(0, 123, 255, 0.2);
  outline: none;
}

.btn {
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-1px);
}

.badge {
  font-size: 0.875rem;
  padding: 0.375em 0.75em;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
  font-size: 0.875rem;
}

.pagination-sm .page-link {
  border-radius: 6px;
  margin: 0 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banned-words-container {
    padding: 16px;
  }

  .page-title-wrapper {
    gap: 12px;
  }

  .page-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .input-group {
    width: 100% !important;
  }
}

/* 暗色主题支持 */
[data-bs-theme='dark'] .banned-words-container {
  background-color: #1a1a1a;
}

[data-bs-theme='dark'] .page-title {
  color: #ffffff;
}

[data-bs-theme='dark'] .page-subtitle {
  color: #b0b0b0 !important;
}

[data-bs-theme='dark'] .card {
  background-color: #2d2d2d;
  border-color: #404040;
}

[data-bs-theme='dark'] .card-header {
  background: linear-gradient(135deg, #2d2d2d, #404040);
}

[data-bs-theme='dark'] .card-title {
  color: #ffffff !important;
}

[data-bs-theme='dark'] .form-label {
  color: #e0e0e0 !important;
}

[data-bs-theme='dark'] .form-control {
  background-color: #3d3d3d;
  border-color: #555555;
  color: #ffffff;
}

[data-bs-theme='dark'] .form-control:focus {
  background-color: #404040;
  border-color: #007bff;
}

[data-bs-theme='dark'] .form-control::placeholder {
  color: #999999;
}

[data-bs-theme='dark'] .form-text {
  color: #b0b0b0 !important;
}

[data-bs-theme='dark'] .text-muted {
  color: #999999 !important;
}

[data-bs-theme='dark'] .table {
  color: #ffffff;
}

[data-bs-theme='dark'] .table-light {
  background-color: #404040 !important;
  color: #ffffff !important;
}

[data-bs-theme='dark'] .table-hover tbody tr:hover {
  background-color: #3d3d3d;
}

[data-bs-theme='dark'] .bg-light {
  background-color: #3d3d3d !important;
}

[data-bs-theme='dark'] .badge.bg-light {
  background-color: #555555 !important;
  color: #ffffff !important;
}

[data-bs-theme='dark'] .page-link {
  background-color: #3d3d3d;
  border-color: #555555;
  color: #ffffff;
}

[data-bs-theme='dark'] .page-link:hover {
  background-color: #404040;
  border-color: #666666;
  color: #ffffff;
}

[data-bs-theme='dark'] .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
}
</style>
