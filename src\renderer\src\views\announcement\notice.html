
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#212529',
                        secondary: '#343a40',
                        accent: '#0d6efd',
                        light: '#f8f9fa',
                        dark: '#1a1d20'
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .scrollbar-hide {
                -ms-overflow-style: none;
                scrollbar-width: none;
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .text-shadow {
                text-shadow: 0 2px 4px rgba(0,0,0,0.5);
            }
        }
    </style>
</head>
<body class="bg-primary text-light font-inter">
    <!-- 导航栏 -->
   
    <!-- 可滑动的banner区域 -->
    <section class="relative overflow-hidden">
        <div id="bannerSlider" class="relative h-[500px]">
            <!-- 轮播图容器 -->
            <div class="flex transition-transform duration-500 ease-in-out h-full" id="bannerWrapper">
                <!-- 轮播项1 -->
                <div class="min-w-full h-full relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-dark/90 to-dark/40 z-10"></div>
                    <img src="https://picsum.photos/id/1033/1920/1080" alt="重要系统升级公告" class="w-full h-full object-cover">
                    <div class="absolute inset-0 z-20 flex items-center">
                        <div class="container mx-auto px-6 md:px-12">
                            <span class="inline-block px-3 py-1 bg-accent/90 text-white text-sm font-semibold rounded-full mb-4">紧急公告</span>
                            <h2 class="text-[clamp(2rem,5vw,3.5rem)] font-bold text-shadow mb-4">系统重大升级计划</h2>
                            <p class="text-[clamp(1rem,2vw,1.25rem)] text-light/90 max-w-2xl mb-8">将于7月31日凌晨2点至6点进行系统全面升级，届时将暂停所有服务，请提前做好准备。</p>
                            <div class="flex flex-wrap gap-4">
                                <a href="#" class="px-6 py-3 bg-accent hover:bg-accent/90 text-white font-medium rounded-lg transition-all transform hover:scale-105 shadow-lg">
                                    查看详情 <i class="fa fa-arrow-right ml-2"></i>
                                </a>
                                <a href="#" class="px-6 py-3 bg-transparent border border-light/30 hover:bg-light/10 text-white font-medium rounded-lg transition-all">
                                    设置提醒 <i class="fa fa-bell ml-2"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 轮播项2 -->
                <div class="min-w-full h-full relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-dark/90 to-dark/40 z-10"></div>
                    <img src="https://picsum.photos/id/1076/1920/1080" alt="新功能上线公告" class="w-full h-full object-cover">
                    <div class="absolute inset-0 z-20 flex items-center">
                        <div class="container mx-auto px-6 md:px-12">
                            <span class="inline-block px-3 py-1 bg-green-500/90 text-white text-sm font-semibold rounded-full mb-4">新功能</span>
                            <h2 class="text-[clamp(2rem,5vw,3.5rem)] font-bold text-shadow mb-4">智能数据分析工具上线</h2>
                            <p class="text-[clamp(1rem,2vw,1.25rem)] text-light/90 max-w-2xl mb-8">全新升级的数据分析模块，支持实时数据可视化和多维数据挖掘，助力业务决策。</p>
                            <div class="flex flex-wrap gap-4">
                                <a href="#" class="px-6 py-3 bg-green-500 hover:bg-green-600 text-white font-medium rounded-lg transition-all transform hover:scale-105 shadow-lg">
                                    立即体验 <i class="fa fa-rocket ml-2"></i>
                                </a>
                                <a href="#" class="px-6 py-3 bg-transparent border border-light/30 hover:bg-light/10 text-white font-medium rounded-lg transition-all">
                                    观看教程 <i class="fa fa-play-circle ml-2"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 轮播项3 -->
                <div class="min-w-full h-full relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-dark/90 to-dark/40 z-10"></div>
                    <img src="https://picsum.photos/id/1015/1920/1080" alt="安全策略更新公告" class="w-full h-full object-cover">
                    <div class="absolute inset-0 z-20 flex items-center">
                        <div class="container mx-auto px-6 md:px-12">
                            <span class="inline-block px-3 py-1 bg-amber-500/90 text-white text-sm font-semibold rounded-full mb-4">安全提示</span>
                            <h2 class="text-[clamp(2rem,5vw,3.5rem)] font-bold text-shadow mb-4">账户安全策略升级</h2>
                            <p class="text-[clamp(1rem,2vw,1.25rem)] text-light/90 max-w-2xl mb-8">为保障您的账户安全，系统将强制要求启用两步验证，请在7日内完成设置。</p>
                            <div class="flex flex-wrap gap-4">
                                <a href="#" class="px-6 py-3 bg-amber-500 hover:bg-amber-600 text-white font-medium rounded-lg transition-all transform hover:scale-105 shadow-lg">
                                    立即设置 <i class="fa fa-shield ml-2"></i>
                                </a>
                                <a href="#" class="px-6 py-3 bg-transparent border border-light/30 hover:bg-light/10 text-white font-medium rounded-lg transition-all">
                                    了解详情 <i class="fa fa-info-circle ml-2"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 轮播控制按钮 -->
            <button id="prevBtn" class="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-dark/50 hover:bg-dark/80 flex items-center justify-center z-30 transition-all">
                <i class="fa fa-angle-left text-2xl"></i>
            </button>
            <button id="nextBtn" class="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-dark/50 hover:bg-dark/80 flex items-center justify-center z-30 transition-all">
                <i class="fa fa-angle-right text-2xl"></i>
            </button>
            
            <!-- 轮播指示器 -->
            <div class="absolute bottom-6 left-0 right-0 flex justify-center space-x-2 z-30">
                <button class="w-3 h-3 rounded-full bg-white/50 hover:bg-white focus:outline-none indicator-btn active" data-index="0"></button>
                <button class="w-3 h-3 rounded-full bg-white/30 hover:bg-white focus:outline-none indicator-btn" data-index="1"></button>
                <button class="w-3 h-3 rounded-full bg-white/30 hover:bg-white focus:outline-none indicator-btn" data-index="2"></button>
            </div>
        </div>
    </section>

    <!-- 公告列表区域 -->
    <section class="py-16 bg-secondary">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center mb-10">
                <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold">最新公告</h2>
                <div class="flex space-x-2">
                    <button id="filterAll" class="px-4 py-2 rounded-lg bg-accent text-white font-medium">全部</button>
                    <button id="filterImportant" class="px-4 py-2 rounded-lg bg-primary hover:bg-primary/80 text-white font-medium transition-colors">重要</button>
                    <button id="filterUpdate" class="px-4 py-2 rounded-lg bg-primary hover:bg-primary/80 text-white font-medium transition-colors">更新</button>
                    <button id="filterEvent" class="px-4 py-2 rounded-lg bg-primary hover:bg-primary/80 text-white font-medium transition-colors">活动</button>
                </div>
            </div>
            
            <!-- 公告卡片网格 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- 公告卡片1 -->
                <div class="bg-primary rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 duration-300 border border-gray-700/50">
                    <div class="relative">
                        <div class="absolute top-4 left-4">
                            <span class="px-2 py-1 bg-red-500 text-white text-xs font-semibold rounded-full">紧急</span>
                        </div>
                        <img src="https://picsum.photos/id/1060/600/400" alt="系统维护公告" class="w-full h-48 object-cover">
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-xs text-gray-400">2025-07-29</span>
                            <span class="text-xs bg-red-500/10 text-red-400 px-2 py-0.5 rounded-full">重要</span>
                        </div>
                        <h3 class="text-xl font-bold mb-2 hover:text-accent transition-colors">系统临时维护通知</h3>
                        <p class="text-gray-300 mb-4 line-clamp-3">由于服务器硬件升级，我们将于7月31日凌晨2点至6点进行系统维护，届时服务将暂停，请提前做好准备...</p>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <img src="https://picsum.photos/id/1005/40/40" alt="管理员头像" class="w-8 h-8 rounded-full mr-2">
                                <span class="text-sm text-gray-400">系统管理员</span>
                            </div>
                            <a href="#" class="text-accent hover:underline text-sm font-medium">阅读更多 <i class="fa fa-angle-right ml-1"></i></a>
                        </div>
                    </div>
                </div>
                
                <!-- 公告卡片2 -->
                <div class="bg-primary rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 duration-300 border border-gray-700/50">
                    <div class="relative">
                        <div class="absolute top-4 left-4">
                            <span class="px-2 py-1 bg-blue-500 text-white text-xs font-semibold rounded-full">更新</span>
                        </div>
                        <img src="https://picsum.photos/id/1/600/400" alt="功能更新公告" class="w-full h-48 object-cover">
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-xs text-gray-400">2025-07-28</span>
                            <span class="text-xs bg-blue-500/10 text-blue-400 px-2 py-0.5 rounded-full">更新</span>
                        </div>
                        <h3 class="text-xl font-bold mb-2 hover:text-accent transition-colors">数据分析功能增强</h3>
                        <p class="text-gray-300 mb-4 line-clamp-3">我们很高兴地宣布，平台数据分析模块已更新，新增了趋势预测、数据导出和自定义报表等功能，提升您的使用体验...</p>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <img src="https://picsum.photos/id/1012/40/40" alt="产品经理头像" class="w-8 h-8 rounded-full mr-2">
                                <span class="text-sm text-gray-400">产品经理</span>
                            </div>
                            <a href="#" class="text-accent hover:underline text-sm font-medium">阅读更多 <i class="fa fa-angle-right ml-1"></i></a>
                        </div>
                    </div>
                </div>
                
                <!-- 公告卡片3 -->
                <div class="bg-primary rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 duration-300 border border-gray-700/50">
                    <div class="relative">
                        <div class="absolute top-4 left-4">
                            <span class="px-2 py-1 bg-green-500 text-white text-xs font-semibold rounded-full">活动</span>
                        </div>
                        <img src="https://picsum.photos/id/1047/600/400" alt="用户活动公告" class="w-full h-48 object-cover">
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-xs text-gray-400">2025-07-27</span>
                            <span class="text-xs bg-green-500/10 text-green-400 px-2 py-0.5 rounded-full">活动</span>
                        </div>
                        <h3 class="text-xl font-bold mb-2 hover:text-accent transition-colors">夏季用户体验活动开启</h3>
                        <p class="text-gray-300 mb-4 line-clamp-3">为感谢广大用户的支持，我们将于8月1日至8月15日举办夏季用户体验活动，参与即可获得积分奖励和专属礼包...</p>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <img src="https://picsum.photos/id/1027/40/40" alt="运营专员头像" class="w-8 h-8 rounded-full mr-2">
                                <span class="text-sm text-gray-400">运营专员</span>
                            </div>
                            <a href="#" class="text-accent hover:underline text-sm font-medium">阅读更多 <i class="fa fa-angle-right ml-1"></i></a>
                        </div>
                    </div>
                </div>
                
                <!-- 公告卡片4 -->
                <div class="bg-primary rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 duration-300 border border-gray-700/50">
                    <div class="relative">
                        <div class="absolute top-4 left-4">
                            <span class="px-2 py-1 bg-amber-500 text-white text-xs font-semibold rounded-full">通知</span>
                        </div>
                        <img src="https://picsum.photos/id/1024/600/400" alt="政策更新公告" class="w-full h-48 object-cover">
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-xs text-gray-400">2025-07-25</span>
                            <span class="text-xs bg-amber-500/10 text-amber-400 px-2 py-0.5 rounded-full">通知</span>
                        </div>
                        <h3 class="text-xl font-bold mb-2 hover:text-accent transition-colors">隐私政策更新说明</h3>
                        <p class="text-gray-300 mb-4 line-clamp-3">根据最新法律法规要求，我们对平台隐私政策进行了更新，请您在使用前仔细阅读并确认，如有疑问请联系客服...</p>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <img src="https://picsum.photos/id/1025/40/40" alt="法务专员头像" class="w-8 h-8 rounded-full mr-2">
                                <span class="text-sm text-gray-400">法务专员</span>
                            </div>
                            <a href="#" class="text-accent hover:underline text-sm font-medium">阅读更多 <i class="fa fa-angle-right ml-1"></i></a>
                        </div>
                    </div>
                </div>
                
                <!-- 公告卡片5 -->
                <div class="bg-primary rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 duration-300 border border-gray-700/50">
                    <div class="relative">
                        <div class="absolute top-4 left-4">
                            <span class="px-2 py-1 bg-blue-500 text-white text-xs font-semibold rounded-full">更新</span>
                        </div>
                        <img src="https://picsum.photos/id/180/600/400" alt="移动端更新公告" class="w-full h-48 object-cover">
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-xs text-gray-400">2025-07-22</span>
                            <span class="text-xs bg-blue-500/10 text-blue-400 px-2 py-0.5 rounded-full">更新</span>
                        </div>
                        <h3 class="text-xl font-bold mb-2 hover:text-accent transition-colors">移动端应用更新发布</h3>
                        <p class="text-gray-300 mb-4 line-clamp-3">移动端应用v3.2.0版本已发布，优化了界面设计，提升了性能，并新增了离线缓存和推送通知等功能...</p>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <img src="https://picsum.photos/id/1074/40/40" alt="开发团队头像" class="w-8 h-8 rounded-full mr-2">
                                <span class="text-sm text-gray-400">开发团队</span>
                            </div>
                            <a href="#" class="text-accent hover:underline text-sm font-medium">阅读更多 <i class="fa fa-angle-right ml-1"></i></a>
                        </div>
                    </div>
                </div>
                
                <!-- 公告卡片6 -->
                <div class="bg-primary rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 duration-300 border border-gray-700/50">
                    <div class="relative">
                        <div class="absolute top-4 left-4">
                            <span class="px-2 py-1 bg-green-500 text-white text-xs font-semibold rounded-full">活动</span>
                        </div>
                        <img src="https://picsum.photos/id/1059/600/400" alt="培训活动公告" class="w-full h-48 object-cover">
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-xs text-gray-400">2025-07-20</span>
                            <span class="text-xs bg-green-500/10 text-green-400 px-2 py-0.5 rounded-full">活动</span>
                        </div>
                        <h3 class="text-xl font-bold mb-2 hover:text-accent transition-colors">新功能线上培训活动</h3>
                        <p class="text-gray-300 mb-4 line-clamp-3">我们将于8月5日举办新功能线上培训活动，由产品经理和技术专家为您详细介绍平台新增功能和使用技巧...</p>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <img src="https://picsum.photos/id/1025/40/40" alt="培训专员头像" class="w-8 h-8 rounded-full mr-2">
                                <span class="text-sm text-gray-400">培训专员</span>
                            </div>
                            <a href="#" class="text-accent hover:underline text-sm font-medium">阅读更多 <i class="fa fa-angle-right ml-1"></i></a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 加载更多按钮 -->
            <div class="mt-12 text-center">
                <button id="loadMore" class="px-8 py-3 bg-transparent border border-accent text-accent hover:bg-accent hover:text-white font-medium rounded-lg transition-all">
                    加载更多 <i class="fa fa-refresh ml-2"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- 订阅通知区域 -->
    <section class="py-16 bg-gradient-to-r from-dark to-secondary relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-0 left-0 w-full h-full bg-[url('https://picsum.photos/id/1048/1920/1080')] bg-cover bg-center"></div>
        </div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-3xl mx-auto text-center">
                <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold mb-4">不错过任何重要公告</h2>
                <p class="text-gray-300 mb-8 text-lg">订阅我们的通知服务，第一时间获取最新公告和重要消息</p>
                <div class="flex flex-col sm:flex-row gap-4 max-w-xl mx-auto">
                    <input type="email" placeholder="输入您的邮箱地址" class="flex-1 px-4 py-3 bg-primary/70 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent text-white placeholder-gray-400">
                    <button class="px-6 py-3 bg-accent hover:bg-accent/90 text-white font-medium rounded-lg transition-all transform hover:scale-105 shadow-lg">
                        立即订阅 <i class="fa fa-paper-plane ml-2"></i>
                    </button>
                </div>
                <p class="text-gray-400 text-sm mt-4">我们尊重您的隐私，绝不会向第三方分享您的信息</p>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <i class="fa fa-bullhorn text-accent text-2xl"></i>
                        <span class="text-xl font-bold">公告中心</span>
                    </div>
                    <p class="text-gray-400 mb-4">及时了解平台最新动态和重要通知</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                            <i class="fa fa-weibo text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                            <i class="fa fa-wechat text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                            <i class="fa fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                            <i class="fa fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-bold mb-4">快速链接</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-accent transition-colors">首页</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-accent transition-colors">所有公告</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-accent transition-colors">重要通知</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-accent transition-colors">更新日志</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-accent transition-colors">用户活动</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-bold mb-4">联系我们</h3>
                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <i class="fa fa-envelope text-accent mt-1 mr-3"></i>
                            <span class="text-gray-400">xxx</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fa fa-phone text-accent mt-1 mr-3"></i>
                            <span class="text-gray-400">xxx</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fa fa-map-marker text-accent mt-1 mr-3"></i>
                            <span class="text-gray-400">xxx</span>
                        </li>
                    </ul>
                </div>
                
              
            </div>
            
            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
                <p>&copy; 2025 公告中心. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <script>
        // 导航栏滚动效果
        const navbar = document.getElementById('navbar');
        window.addEventListener('scroll', () => {
            if (window.scrollY > 50) {
                navbar.classList.add('py-2', 'shadow-lg');
                navbar.classList.remove('py-3');
            } else {
                navbar.classList.add('py-3');
                navbar.classList.remove('py-2', 'shadow-lg');
            }
        });
        
        // 移动端菜单切换
        const menuBtn = document.getElementById('menuBtn');
        const mobileMenu = document.getElementById('mobileMenu');
        menuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
            if (mobileMenu.classList.contains('hidden')) {
                menuBtn.innerHTML = '<i class="fa fa-bars text-xl"></i>';
            } else {
                menuBtn.innerHTML = '<i class="fa fa-times text-xl"></i>';
            }
        });
        
        // 轮播图功能
        const bannerWrapper = document.getElementById('bannerWrapper');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const indicatorBtns = document.querySelectorAll('.indicator-btn');
        let currentIndex = 0;
        const totalSlides = indicatorBtns.length;
        
        // 初始化轮播图
        function initCarousel() {
            updateCarousel();
            setInterval(nextSlide, 5000);
        }
        
        // 更新轮播图显示
        function updateCarousel() {
            bannerWrapper.style.transform = `translateX(-${currentIndex * 100}%)`;
            
            // 更新指示器状态
            indicatorBtns.forEach((btn, index) => {
                if (index === currentIndex) {
                    btn.classList.add('bg-white/50');
                    btn.classList.remove('bg-white/30');
                } else {
                    btn.classList.add('bg-white/30');
                    btn.classList.remove('bg-white/50');
                }
            });
        }
        
        // 下一张幻灯片
        function nextSlide() {
            currentIndex = (currentIndex + 1) % totalSlides;
            updateCarousel();
        }
        
        // 上一张幻灯片
        function prevSlide() {
            currentIndex = (currentIndex - 1 + totalSlides) % totalSlides;
            updateCarousel();
        }
        
        // 事件监听
        prevBtn.addEventListener('click', prevSlide);
        nextBtn.addEventListener('click', nextSlide);
        
        // 点击指示器切换幻灯片
        indicatorBtns.forEach((btn, index) => {
            btn.addEventListener('click', () => {
                currentIndex = index;
                updateCarousel();
            });
        });
        
        // 公告过滤功能
        const filterButtons = [
            document.getElementById('filterAll'),
            document.getElementById('filterImportant'),
            document.getElementById('filterUpdate'),
            document.getElementById('filterEvent')
        ];
        
        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                // 重置所有按钮样式
                filterButtons.forEach(btn => {
                    btn.classList.add('bg-primary', 'hover:bg-primary/80');
                    btn.classList.remove('bg-accent');
                });
                
                // 设置当前按钮样式
                button.classList.add('bg-accent');
                button.classList.remove('bg-primary', 'hover:bg-primary/80');
                
                // 这里可以添加实际的过滤逻辑
                const filterType = button.id.replace('filter', '').toLowerCase();
                console.log(`过滤类型: ${filterType}`);
                // 实际项目中，这里应该根据filterType过滤公告列表
            });
        });
        
        // 加载更多功能
        const loadMoreBtn = document.getElementById('loadMore');
        loadMoreBtn.addEventListener('click', () => {
            // 显示加载状态
            loadMoreBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i> 加载中...';
            loadMoreBtn.disabled = true;
            
            // 模拟加载延迟
            setTimeout(() => {
                // 恢复按钮状态
                loadMoreBtn.innerHTML = '加载更多 <i class="fa fa-refresh ml-2"></i>';
                loadMoreBtn.disabled = false;
                
                // 实际项目中，这里应该加载更多公告内容
                console.log('加载更多公告');
            }, 1500);
        });
        
        // 页面加载完成后初始化轮播图
        window.addEventListener('load', initCarousel);
    </script>
</body>
</html>
    