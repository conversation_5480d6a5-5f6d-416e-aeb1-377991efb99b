/**
 * 日志工具函数
 * 提供应用程序的本地日志记录功能
 */
import * as fs from 'fs'
import * as path from 'path'
import { getAPPBasePath } from '../../module/API'

// 日志条目接口
interface LogEntry {
  /** 时间戳（毫秒） */
  timestamp: number
  /** 日志级别 */
  level: 'info' | 'warn' | 'error'
  /** 日志内容 */
  content: string
  /** 唯一标识符 */
  id: string
}

/**
 * 日志工具类
 * 提供info、warn、error三种级别的日志记录
 */
export default class Logger {
  /** 日志文件路径 */
  private static readonly LOG_FILE_PATH: string = path.join(
    getAPPBasePath(),
    'logs',
    'app.log.json'
  )

  /** 最大日志条数（超过后自动清理旧日志） */
  private static readonly MAX_LOG_ENTRIES = 1000

  /**
   * 记录信息级别日志
   * @param content 日志内容
   */
  public static info(content: string): void {
    this.writeLog('info', content)
  }

  /**
   * 记录警告级别日志
   * @param content 日志内容
   */
  public static warn(content: string): void {
    this.writeLog('warn', content)
  }

  /**
   * 记录错误级别日志
   * @param content 日志内容
   */
  public static error(content: string): void {
    this.writeLog('error', content)
  }

  /**
   * 写入日志到文件
   * @param level 日志等级
   * @param content 日志内容
   */
  private static writeLog(level: 'info' | 'warn' | 'error', content: string): void {
    try {
      // 确保日志目录存在
      const logDir = path.dirname(this.LOG_FILE_PATH)
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true })
      }

      // 读取现有日志或创建新的日志数组
      let logs: LogEntry[] = []
      if (fs.existsSync(this.LOG_FILE_PATH)) {
        try {
          const fileContent = fs.readFileSync(this.LOG_FILE_PATH, 'utf-8')
          logs = JSON.parse(fileContent)
        } catch (parseError) {
          // 文件损坏时重新创建
          console.warn('日志文件解析失败，将重新创建:', parseError)
          logs = []
        }
      }

      // 添加新日志
      const logEntry: LogEntry = {
        timestamp: Date.now(),
        level,
        content,
        id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
      }
      logs.push(logEntry)

      // 如果日志数量过多，只保留最新的条目
      if (logs.length > this.MAX_LOG_ENTRIES) {
        logs = logs.slice(-this.MAX_LOG_ENTRIES)
      }

      // 写入文件
      fs.writeFileSync(this.LOG_FILE_PATH, JSON.stringify(logs, null, 2), 'utf-8')
    } catch (error) {
      // 写入日志失败时输出到控制台，避免循环错误
      console.error('写入日志文件失败:', error)
    }
  }

  /**
   * 获取所有日志记录
   * @returns 日志记录数组
   */
  public static getAllLogs(): LogEntry[] {
    try {
      if (!fs.existsSync(this.LOG_FILE_PATH)) {
        return []
      }

      const fileContent = fs.readFileSync(this.LOG_FILE_PATH, 'utf-8')
      return JSON.parse(fileContent)
    } catch (error) {
      console.error('读取日志文件失败:', error)
      return []
    }
  }

  /**
   * 清空所有日志
   */
  public static clearLogs(): void {
    try {
      const logDir = path.dirname(this.LOG_FILE_PATH)
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true })
      }

      fs.writeFileSync(this.LOG_FILE_PATH, JSON.stringify([], null, 2), 'utf-8')
    } catch (error) {
      console.error('清空日志文件失败:', error)
    }
  }
}
