# 页面自动选择趋势分析第一分钟功能实现

## 任务描述

在DetailView页面加载数据完成后，需要自动选择趋势分析图表的第一分钟，让其他数据同步更新，相当于进入页面后自动点击了一下图表的第一分钟。

## 实现方案

采用在TrendAnalysisPanel组件内部自动选择的方案，在数据加载完成且图表初始化后，自动获取第一个时间点并调用highlightTimeRange函数。

## 实现步骤

### 步骤1：修改 useTrendAnalysis hook

- 文件：`src/renderer/src/views/detail/trends/hooks/useTrendAnalysis.ts`
- 添加自动选择状态控制：`hasAutoSelected`
- 添加 `autoSelectFirstMinute` 函数
- 在 `initChart` 函数中调用自动选择
- 监听数据变化重置自动选择状态

### 步骤2：功能逻辑

- `autoSelectFirstMinute` 函数：
  1. 检查是否已经自动选择过，防止重复触发
  2. 获取排序后的时间数据（xAxisData）
  3. 取第一个时间点
  4. 调用 `highlightTimeRange(firstTime, true)` 选择第一分钟并发送消息
- 数据监听：监听 `trendData` 变化，重置 `hasAutoSelected` 状态

### 步骤3：接口更新

- 更新 `TrendAnalysisResult` 接口，添加 `autoSelectFirstMinute` 方法
- 在返回对象中暴露该方法

## 实现细节

### 核心代码

```typescript
// 自动选择状态控制
const hasAutoSelected = ref<boolean>(false)

// 自动选择第一分钟
function autoSelectFirstMinute(): void {
  if (hasAutoSelected.value) return // 防止重复自动选择

  const chartData = trendData.value
  if (chartData.length === 0) return

  // 获取排序后的时间数据
  const xAxisData = [...new Set(chartData.map((item) => item.horizontal as string))]
    .filter((value): value is string => value !== undefined)
    .sort((a, b) => {
      const minutesA = parseInt(a.split(':')[0]) * 60 + parseInt(a.split(':')[1])
      const minutesB = parseInt(b.split(':')[0]) * 60 + parseInt(b.split(':')[1])
      return minutesA - minutesB
    })

  if (xAxisData.length > 0) {
    const firstTime = xAxisData[0]
    // 自动选择第一分钟，并发送消息同步其他组件
    highlightTimeRange(firstTime, true)
    hasAutoSelected.value = true
  }
}
```

### 触发时机

在 `initChart` 函数中，图表初始化完成后通过 `setTimeout` 延迟调用：

```typescript
// 图表初始化完成后，自动选择第一分钟
setTimeout(() => {
  autoSelectFirstMinute()
}, 100)
```

### 数据监听

```typescript
// 监听数据变化，重置自动选择状态
const unsubscribe = watch(
  trendData,
  () => {
    // 当趋势数据发生变化时，重置自动选择状态
    hasAutoSelected.value = false
  },
  { deep: true }
)
```

## 预期效果

1. 页面进入后自动选择第一分钟的数据
2. 触发相同的交互效果：
   - 高亮图表区域
   - 更新右侧指标数据
   - 同步实时数据组件
   - 视频跳转到对应时间
3. 不会重复触发自动选择
4. 保持现有功能不受影响

## 状态

- [x] 步骤1：修改 useTrendAnalysis hook
- [x] 步骤2：添加自动选择功能逻辑
- [x] 步骤3：更新接口定义
- [x] 步骤4：优化避免视频自动播放
- [ ] 测试验证功能是否正常工作

## 优化记录

### 2024年优化：避免自动选择时触发视频播放

**问题**：用户反馈不需要在linkage中自动播放视频，需要优化自动选择功能。

**解决方案**：

1. 创建专门的 `autoHighlightTimeRange` 函数用于自动选择
2. 该函数只发送消息给实时指标组件，不发送给 LinkAge 组件
3. 保持手动点击的完整功能不变

**核心代码**：

```typescript
// 专门用于自动选择的高亮函数，不触发视频跳转
function autoHighlightTimeRange(clickedTime: string): void {
  // ... 核心高亮逻辑 ...

  // 自动选择时只发送消息给实时指标组件，不触发视频跳转
  const timestamp = convertTimeStringToTimestamp(clickedTime)
  if (timestamp) {
    // 获取当前时间点的实时数据
    const currentTimeData = chartData.filter((item) => item.horizontal === clickedTime)
    // 只发送给实时指标组件，不发送给LinkAge组件
    timeSyncService.send(timestamp, TimeSyncSender.REALTIME_METRICS, {
      realTimeData: currentTimeData,
      clickedTime: clickedTime
    })
  }
}
```

**优化效果**：

- ✅ 自动选择第一分钟时不会触发视频播放
- ✅ 仍然会同步更新实时数据组件
- ✅ 手动点击图表的完整功能保持不变
- ✅ 高亮显示和数据计算功能正常
