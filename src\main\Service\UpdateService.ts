import { autoUpdater } from 'electron-updater'
import { app, IpcMainEvent, BrowserWindow } from 'electron'

/**
 * 自动更新服务
 */
class UpdateService {
  private mainWindow: Electron.BrowserWindow | null = null
  private updateAvailable = false
  private updateDownloaded = false

  /**
   * 初始化更新服务
   */
  constructor() {
    this.setupAutoUpdater()
  }

  /**
   * 设置主窗口引用
   */
  setMainWindow(window: Electron.BrowserWindow): void {
    this.mainWindow = window
  }

  /**
   * 配置 autoUpdater
   */
  private setupAutoUpdater(): void {
    // 设置更新服务器地址
    autoUpdater.setFeedURL({
      provider: 'generic',
      url: 'http://localhost:8080'
    })

    // 自动下载更新
    autoUpdater.autoDownload = false

    // 自动安装并重启
    autoUpdater.autoInstallOnAppQuit = true

    // 设置事件监听
    this.setupEventListeners()
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 检查更新错误
    autoUpdater.on('error', (error) => {
      console.error('Auto updater error:', error)
      this.sendToRenderer('update-error', { error: error.message })
    })

    // 检查更新
    autoUpdater.on('checking-for-update', () => {
      console.log('Checking for update...')
      this.sendToRenderer('checking-for-update')
    })

    // 有可用更新
    autoUpdater.on('update-available', (info) => {
      console.log('Update available:', info)
      this.updateAvailable = true
      this.sendToRenderer('update-available', info)
    })

    // 没有可用更新
    autoUpdater.on('update-not-available', (info) => {
      console.log('Update not available:', info)
      this.sendToRenderer('update-not-available', info)
    })

    // 更新下载进度
    autoUpdater.on('download-progress', (progressInfo) => {
      console.log('Download progress:', progressInfo)
      this.sendToRenderer('download-progress', progressInfo)
    })

    // 更新下载完成
    autoUpdater.on('update-downloaded', (info) => {
      console.log('Update downloaded:', info)
      this.updateDownloaded = true
      this.sendToRenderer('update-downloaded', info)
    })
  }

  /**
   * 向渲染进程发送消息
   */
  private sendToRenderer(channel: string, data?: any): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(channel, data)
    }
  }

  /**
   * 检查更新
   */
  checkForUpdates(): void {
    autoUpdater.checkForUpdatesAndNotify()
  }

  /**
   * 下载更新
   */
  downloadUpdate(): void {
    if (this.updateAvailable) {
      autoUpdater.downloadUpdate()
    }
  }

  /**
   * 安装更新并重启
   */
  installUpdate(): void {
    if (this.updateDownloaded) {
      autoUpdater.quitAndInstall()
    }
  }

  /**
   * 注册 IPC 事件处理程序
   */
  static hook(ipcMain: Electron.IpcMain): void {
    const updateService = new UpdateService()

    // 检查更新
    ipcMain.handle('check-for-updates', async () => {
      updateService.checkForUpdates()
    })

    // 下载更新
    ipcMain.handle('download-update', async () => {
      updateService.downloadUpdate()
    })

    // 安装更新
    ipcMain.handle('install-update', async () => {
      updateService.installUpdate()
    })

    // 获取更新状态
    ipcMain.handle('get-update-status', async () => {
      return {
        updateAvailable: updateService.updateAvailable,
        updateDownloaded: updateService.updateDownloaded
      }
    })

    // 获取应用版本
    ipcMain.handle('app:getVersion', async () => {
      return app.getVersion()
    })

    // 设置主窗口（需要在创建窗口后调用）
    ipcMain.on('set-main-window', (event: IpcMainEvent) => {
      const allWindows = BrowserWindow.getAllWindows()
      const window = allWindows.find((w) => w.webContents === event.sender)
      if (window) {
        updateService.setMainWindow(window)

        // 应用启动后自动检查更新
        setTimeout(() => {
          if (!app.isPackaged) {
            console.log('Development mode - auto update check enabled for testing')
          }
          updateService.checkForUpdates()
        }, 3000) // 延迟3秒检查
      }
    })
  }
}

export default UpdateService
