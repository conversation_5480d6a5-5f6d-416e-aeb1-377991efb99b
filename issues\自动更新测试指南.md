# 自动更新功能测试指南

## 功能概述

已成功集成 electron-updater 自动更新功能，支持：

- 应用启动后自动检查更新（延迟3秒）
- 手动检查更新（Header右上角按钮）
- 下载进度显示
- 用户确认安装流程

## 配置说明

- **服务器地址**：`http://localhost:8080`
- **更新文件**：服务器根目录下的 `latest.yml`
- **支持环境**：开发和生产环境均可测试

## 测试步骤

### 1. 启动应用

```bash
npm run dev
```

### 2. 观察控制台输出

应用启动后会看到：

- "MainLayout mounted, setting up update monitoring"
- 3秒后："Development mode - auto update check enabled for testing"
- 如果服务器有更新："Update available detected, showing notification"

### 3. 手动测试

- 点击右上角的刷新图标按钮
- 观察是否显示更新检查模态框

### 4. 模态框功能

更新模态框包含以下状态：

- **检查中**：显示旋转图标和"正在检查更新..."
- **发现更新**：显示版本信息和"下载更新"按钮
- **下载中**：显示进度条和下载速度
- **下载完成**：显示"立即安装"和"稍后安装"选项
- **无更新**：显示"当前已是最新版本"
- **错误**：显示错误信息

## 服务器设置要求

### latest.yml 文件格式

```yaml
version: 1.0.1
files:
  - url: electron-app-1.0.1-setup.exe
    sha512: [文件哈希值]
    size: [文件大小]
path: electron-app-1.0.1-setup.exe
sha512: [文件哈希值]
releaseDate: '2024-01-01T00:00:00.000Z'
```

### 服务器文件结构

```
localhost:8080/
├── latest.yml          # 更新信息文件
└── electron-app-1.0.1-setup.exe  # 更新包
```

## 调试信息

开启开发者工具查看：

1. **Console 标签**：查看更新日志
2. **Network 标签**：查看更新请求
3. **Application 标签**：查看应用信息

## 常见问题

### 1. 服务器连接失败

- 确认 localhost:8080 服务器正在运行
- 确认 latest.yml 文件存在且格式正确

### 2. 更新检查无响应

- 检查控制台是否有错误日志
- 确认 IPC 通信正常

### 3. 模态框不显示

- 检查 Bootstrap 是否正确加载
- 确认事件监听器正常工作

## 自动更新流程

1. **启动检查**：应用启动3秒后自动检查
2. **发现更新**：自动显示更新提示
3. **用户选择**：用户决定是否下载
4. **下载过程**：显示下载进度
5. **安装确认**：用户选择立即或稍后安装
6. **重启应用**：完成更新安装

## 注意事项

- 开发环境下仅用于测试，不会实际更新应用
- 生产环境下会执行真实的更新流程
- 更新服务器需要支持 CORS（如果需要）
