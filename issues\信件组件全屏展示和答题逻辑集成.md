# 信件组件全屏展示和答题逻辑集成

## 任务背景

用户希望为 `LetterComponent.vue` 补充展示逻辑和答题逻辑，同时保持其精美的信件模板样式不变。要求实现全屏覆盖显示，不使用Modal形式。

## 实现计划

### 步骤1：修改LetterComponent.vue的展示逻辑

- 添加全屏黑色半透明遮罩背景
- 集成useLetter状态管理
- 添加条件渲染逻辑（仅在需要显示时展示）

### 步骤2：为LetterComponent添加答题功能

- 在信件下方添加"我已阅读完毕"按钮
- 添加"开始答题"按钮（当有题目时）
- 实现答题页面覆盖显示
- 添加结果页面显示

### 步骤3：更新App.vue集成

- 确保LetterComponent在App.vue中正确显示
- 移除或调整可能冲突的LetterModal引用

### 步骤4：样式调整

- 确保全屏遮罩效果
- 保持原有信件的精美样式
- 适配暗黑模式

### 步骤5：测试验证

- 测试信件显示/隐藏
- 测试答题流程
- 验证样式完整性

## 技术要点

- 使用 `useLetter` 组合式函数管理状态
- 全屏fixed定位覆盖页面
- 多页面条件渲染：消息页→答题页→结果页
- 保持原有精美信件样式不变
- 支持暗黑模式
