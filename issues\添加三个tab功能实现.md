# 添加三个Tab功能实现

## 任务概述

在 `TrendMetricArea.vue` 页面的 MetricGrid 组件下方添加三个tab：数据、商品、互动。点击tab切换时要能切换上面的指标数据显示。

## 实现方案

在MetricGrid下方添加独立的Tab区域，使用Element Plus的el-tabs组件。

## 执行步骤

### 1. 模板结构修改

- ✅ 在 `data-content` div 内，MetricGrid组件下方添加tabs-section
- ✅ 使用 `el-tabs` 和 `el-tab-pane` 创建三个tab
- ✅ 添加tab图标和标签

### 2. 状态管理

- ✅ 添加 `activeTab` 响应式状态，默认值为 'data'
- ✅ 添加 `tabOptions` 配置数组，包含三个tab的配置

### 3. 数据切换逻辑

- ✅ 修改 `currentMetricsData` 计算属性，根据当前选中tab返回不同数据
- ✅ 数据tab显示原有指标数据
- ✅ 商品和互动tab暂时返回空数组

### 4. 样式优化

- ✅ 添加tab相关CSS样式
- ✅ 添加空状态显示样式
- ✅ 添加暗黑模式适配
- ✅ 自定义Element Plus tab组件样式

## 功能特性

1. **三个Tab**: 数据、商品、互动
2. **数据切换**: 点击tab切换上方MetricGrid显示的指标数据
3. **纯切换器**: tab下方无内容区域，只作为切换器使用
4. **响应式**: 支持暗黑模式
5. **样式统一**: 与项目整体设计风格保持一致

## 技术实现

- 使用 Vue 3 Composition API
- Element Plus el-tabs 组件
- 响应式数据绑定
- 计算属性进行数据过滤
- CSS深度选择器自定义样式

## 测试验证

- ✅ tab切换功能正常
- ✅ 数据tab显示原有指标数据
- ✅ 商品和互动tab切换时MetricGrid显示空数据
- ✅ tab作为纯切换器，下方无内容区域
- ✅ 样式符合设计要求

## 最终效果

- 使用 `tab-position="bottom"` 将tab固定在底部
- MetricGrid放在每个tab-pane中，结构更合理
- 上面的内容区域撑满空间，tab固定在下方
- 点击"数据"tab：显示完整指标数据
- 点击"商品"或"互动"tab：MetricGrid显示空状态
- 每个tab都有自己的内容区域
- 符合用户预期的底部tab + 内容区域设计

## 优化调整

- ✅ 将MetricGrid移入tab-pane中
- ✅ 使用flexbox布局让内容区域撑满
- ✅ tab固定在底部作为切换器
- ✅ 每个tab有独立的内容展示区域
