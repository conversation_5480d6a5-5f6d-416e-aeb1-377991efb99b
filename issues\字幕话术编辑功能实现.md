# 字幕话术编辑功能实现

## 任务概述

在字幕区添加"修改话术"按钮，点击后弹出超大模态框，包含左中右三个区域：

- 左侧：按分钟显示原始字幕话术（只读）
- 中间：修改话术输入框
- 右侧：备注输入框

## 实现方案

采用组件化设计，创建独立的 SubtitleEditModal 组件，便于维护和扩展。

## 已完成的工作

### 1. 创建字幕编辑模态框组件

- **文件**: `src/renderer/src/views/detail/linkAge/components/SubtitleEditModal.vue`
- **功能**:
  - 超大模态框（95vw × 90vh）
  - 三列布局设计
  - 分钟级字幕数据分组
  - 保存、重置、取消操作

### 2. 数据处理逻辑

- 基于时间分割线对字幕数据进行分组
- 每个分组包含该时间段内的所有字幕文本
- 自动将原始文本填入编辑框作为初始值

### 3. UI 设计特点

- **左列**: 渐变色头部（蓝紫色），显示原始话术，只读模式
- **中列**: 渐变色头部（粉红色），可编辑的话术输入框
- **右列**: 渐变色头部（蓝绿色），备注输入框
- 自定义滚动条样式
- 响应式设计，适配超大模态框

### 4. SubtitlePanel 组件集成

- **文件**: `src/renderer/src/views/detail/linkAge/components/SubtitlePanel.vue`
- 添加"修改话术"按钮（警告色，突出显示）
- 引入并使用 SubtitleEditModal 组件
- 实现模态框的打开和数据传递

## 技术实现细节

### 数据结构

```typescript
interface GroupedSubtitle {
  timeLabel: string // 时间标签（如：12:34:56）
  relativeTime: string // 相对开播时间（如：01:23）
  originalText: string // 原始字幕文本
  editedText: string // 编辑后的文本
  note: string // 备注
  timestamp: number // 时间戳
  segments: ProcessedSeg[] // 原始字幕段落
}
```

### 核心功能

1. **数据分组**: 基于 `divider` 类型的分割线进行分组
2. **文本聚合**: 将同一时间段内的多个字幕段落合并为单一文本
3. **双向绑定**: 编辑框和备注框支持实时编辑
4. **状态管理**: 保存、重置、取消操作

### 5. 数据持久化功能

- **后端接口**: 在 `RoomService.ts` 中添加 `saveSubtitleEdits` 和 `loadSubtitleEdits` 方法
- **存储方案**: 使用文件系统，存储在 `cache/segs/seg_{roomID}.json`
- **IPC 通信**: 在 `LZYService.ts` 中注册 `subtitle:save` 和 `subtitle:load` 处理器
- **数据结构**: JSON 格式存储，包含时间标签、编辑文本、备注等信息

### 6. 组件逻辑优化

- **智能加载**: 组件打开时自动尝试加载已保存的数据
- **默认逻辑保留**: 如果没有保存数据，使用 props 中的原始数据
- **数据覆盖**: 有保存数据时，用保存的编辑文本和备注覆盖默认值
- **异步保存**: 保存操作支持错误处理和用户反馈

## 当前状态

✅ 基本功能已实现
✅ UI 设计已完成
✅ 组件集成已完成
✅ 数据持久化已完成
✅ 后端接口已实现
✅ IPC 通信已配置
⚠️ 待测试完整功能

## 技术亮点

1. **智能数据处理**: 自动判断使用保存数据还是默认数据
2. **文件系统存储**: 简单可靠的 JSON 文件存储方案
3. **错误处理**: 完善的异常处理和用户提示
4. **联动滚动**: 三列完美对齐的联动滚动体验

## 下一步工作

1. 完整功能测试
2. 边界情况处理
3. 性能优化

## 相关文件

- `src/renderer/src/views/detail/linkAge/components/SubtitleEditModal.vue` (新建)
- `src/renderer/src/views/detail/linkAge/components/SubtitlePanel.vue` (修改)
- `src/main/Service/business/RoomService.ts` (新增字幕编辑接口)
- `src/main/Service/LZYService.ts` (新增IPC处理器)
- `cache/segs/` (数据存储目录)
- `src/renderer/src/views/detail/linkAge/hooks/useSubtitleProcessor.ts` (参考)
