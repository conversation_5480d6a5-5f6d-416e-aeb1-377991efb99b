"use strict";(()=>{var Ar=Object.create;var qt=Object.defineProperty;var kr=Object.getOwnPropertyDescriptor;var br=Object.getOwnPropertyNames;var Vr=Object.getPrototypeOf,Lr=Object.prototype.hasOwnProperty;var Mr=(t,e)=>()=>(t&&(e=t(t=0)),e);var rt=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var wr=(t,e,o,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of br(e))!Lr.call(t,r)&&r!==o&&qt(t,r,{get:()=>e[r],enumerable:!(n=kr(e,r))||n.enumerable});return t};var Zt=(t,e,o)=>(o=t!=null?Ar(Vr(t)):{},wr(e||!t||!t.__esModule?qt(o,"default",{value:t,enumerable:!0}):o,t));var a=Mr(()=>{});var to=rt((di,eo)=>{"use strict";a();eo.exports=Hr;function se(t){return t instanceof Buffer?Buffer.from(t):new t.constructor(t.buffer.slice(),t.byteOffset,t.length)}function Hr(t){if(t=t||{},t.circles)return Ur(t);let e=new Map;if(e.set(Date,i=>new Date(i)),e.set(Map,(i,p)=>new Map(n(Array.from(i),p))),e.set(Set,(i,p)=>new Set(n(Array.from(i),p))),t.constructorHandlers)for(let i of t.constructorHandlers)e.set(i[0],i[1]);let o=null;return t.proto?s:r;function n(i,p){let c=Object.keys(i),l=new Array(c.length);for(let m=0;m<c.length;m++){let _=c[m],E=i[_];typeof E!="object"||E===null?l[_]=E:E.constructor!==Object&&(o=e.get(E.constructor))?l[_]=o(E,p):ArrayBuffer.isView(E)?l[_]=se(E):l[_]=p(E)}return l}function r(i){if(typeof i!="object"||i===null)return i;if(Array.isArray(i))return n(i,r);if(i.constructor!==Object&&(o=e.get(i.constructor)))return o(i,r);let p={};for(let c in i){if(Object.hasOwnProperty.call(i,c)===!1)continue;let l=i[c];typeof l!="object"||l===null?p[c]=l:l.constructor!==Object&&(o=e.get(l.constructor))?p[c]=o(l,r):ArrayBuffer.isView(l)?p[c]=se(l):p[c]=r(l)}return p}function s(i){if(typeof i!="object"||i===null)return i;if(Array.isArray(i))return n(i,s);if(i.constructor!==Object&&(o=e.get(i.constructor)))return o(i,s);let p={};for(let c in i){let l=i[c];typeof l!="object"||l===null?p[c]=l:l.constructor!==Object&&(o=e.get(l.constructor))?p[c]=o(l,s):ArrayBuffer.isView(l)?p[c]=se(l):p[c]=s(l)}return p}}function Ur(t){let e=[],o=[],n=new Map;if(n.set(Date,c=>new Date(c)),n.set(Map,(c,l)=>new Map(s(Array.from(c),l))),n.set(Set,(c,l)=>new Set(s(Array.from(c),l))),t.constructorHandlers)for(let c of t.constructorHandlers)n.set(c[0],c[1]);let r=null;return t.proto?p:i;function s(c,l){let m=Object.keys(c),_=new Array(m.length);for(let E=0;E<m.length;E++){let y=m[E],d=c[y];if(typeof d!="object"||d===null)_[y]=d;else if(d.constructor!==Object&&(r=n.get(d.constructor)))_[y]=r(d,l);else if(ArrayBuffer.isView(d))_[y]=se(d);else{let g=e.indexOf(d);g!==-1?_[y]=o[g]:_[y]=l(d)}}return _}function i(c){if(typeof c!="object"||c===null)return c;if(Array.isArray(c))return s(c,i);if(c.constructor!==Object&&(r=n.get(c.constructor)))return r(c,i);let l={};e.push(c),o.push(l);for(let m in c){if(Object.hasOwnProperty.call(c,m)===!1)continue;let _=c[m];if(typeof _!="object"||_===null)l[m]=_;else if(_.constructor!==Object&&(r=n.get(_.constructor)))l[m]=r(_,i);else if(ArrayBuffer.isView(_))l[m]=se(_);else{let E=e.indexOf(_);E!==-1?l[m]=o[E]:l[m]=i(_)}}return e.pop(),o.pop(),l}function p(c){if(typeof c!="object"||c===null)return c;if(Array.isArray(c))return s(c,p);if(c.constructor!==Object&&(r=n.get(c.constructor)))return r(c,p);let l={};e.push(c),o.push(l);for(let m in c){let _=c[m];if(typeof _!="object"||_===null)l[m]=_;else if(_.constructor!==Object&&(r=n.get(_.constructor)))l[m]=r(_,p);else if(ArrayBuffer.isView(_))l[m]=se(_);else{let E=e.indexOf(_);E!==-1?l[m]=o[E]:l[m]=p(_)}}return e.pop(),o.pop(),l}}});var Mn=rt((Ln,Ne)=>{"use strict";a();(function(t){"use strict";var e={\u00C0:"A",\u00C1:"A",\u00C2:"A",\u00C3:"A",\u00C4:"Ae",\u00C5:"A",\u00C6:"AE",\u00C7:"C",\u00C8:"E",\u00C9:"E",\u00CA:"E",\u00CB:"E",\u00CC:"I",\u00CD:"I",\u00CE:"I",\u00CF:"I",\u00D0:"D",\u00D1:"N",\u00D2:"O",\u00D3:"O",\u00D4:"O",\u00D5:"O",\u00D6:"Oe",\u0150:"O",\u00D8:"O",\u00D9:"U",\u00DA:"U",\u00DB:"U",\u00DC:"Ue",\u0170:"U",\u00DD:"Y",\u00DE:"TH",\u00DF:"ss",\u00E0:"a",\u00E1:"a",\u00E2:"a",\u00E3:"a",\u00E4:"ae",\u00E5:"a",\u00E6:"ae",\u00E7:"c",\u00E8:"e",\u00E9:"e",\u00EA:"e",\u00EB:"e",\u00EC:"i",\u00ED:"i",\u00EE:"i",\u00EF:"i",\u00F0:"d",\u00F1:"n",\u00F2:"o",\u00F3:"o",\u00F4:"o",\u00F5:"o",\u00F6:"oe",\u0151:"o",\u00F8:"o",\u00F9:"u",\u00FA:"u",\u00FB:"u",\u00FC:"ue",\u0171:"u",\u00FD:"y",\u00FE:"th",\u00FF:"y","\u1E9E":"SS",\u0627:"a",\u0623:"a",\u0625:"i",\u0622:"aa",\u0624:"u",\u0626:"e",\u0621:"a",\u0628:"b",\u062A:"t",\u062B:"th",\u062C:"j",\u062D:"h",\u062E:"kh",\u062F:"d",\u0630:"th",\u0631:"r",\u0632:"z",\u0633:"s",\u0634:"sh",\u0635:"s",\u0636:"dh",\u0637:"t",\u0638:"z",\u0639:"a",\u063A:"gh",\u0641:"f",\u0642:"q",\u0643:"k",\u0644:"l",\u0645:"m",\u0646:"n",\u0647:"h",\u0648:"w",\u064A:"y",\u0649:"a",\u0629:"h",\uFEFB:"la",\uFEF7:"laa",\uFEF9:"lai",\uFEF5:"laa",\u06AF:"g",\u0686:"ch",\u067E:"p",\u0698:"zh",\u06A9:"k",\u06CC:"y","\u064E":"a","\u064B":"an","\u0650":"e","\u064D":"en","\u064F":"u","\u064C":"on","\u0652":"","\u0660":"0","\u0661":"1","\u0662":"2","\u0663":"3","\u0664":"4","\u0665":"5","\u0666":"6","\u0667":"7","\u0668":"8","\u0669":"9","\u06F0":"0","\u06F1":"1","\u06F2":"2","\u06F3":"3","\u06F4":"4","\u06F5":"5","\u06F6":"6","\u06F7":"7","\u06F8":"8","\u06F9":"9",\u1000:"k",\u1001:"kh",\u1002:"g",\u1003:"ga",\u1004:"ng",\u1005:"s",\u1006:"sa",\u1007:"z","\u1005\u103B":"za",\u100A:"ny",\u100B:"t",\u100C:"ta",\u100D:"d",\u100E:"da",\u100F:"na",\u1010:"t",\u1011:"ta",\u1012:"d",\u1013:"da",\u1014:"n",\u1015:"p",\u1016:"pa",\u1017:"b",\u1018:"ba",\u1019:"m",\u101A:"y",\u101B:"ya",\u101C:"l",\u101D:"w",\u101E:"th",\u101F:"h",\u1020:"la",\u1021:"a","\u103C":"y","\u103B":"ya","\u103D":"w","\u103C\u103D":"yw","\u103B\u103D":"ywa","\u103E":"h",\u1027:"e","\u104F":"-e",\u1023:"i",\u1024:"-i",\u1009:"u",\u1026:"-u",\u1029:"aw","\u101E\u103C\u1031\u102C":"aw",\u102A:"aw","\u1040":"0","\u1041":"1","\u1042":"2","\u1043":"3","\u1044":"4","\u1045":"5","\u1046":"6","\u1047":"7","\u1048":"8","\u1049":"9","\u1039":"","\u1037":"","\u1038":"",\u010D:"c",\u010F:"d",\u011B:"e",\u0148:"n",\u0159:"r",\u0161:"s",\u0165:"t",\u016F:"u",\u017E:"z",\u010C:"C",\u010E:"D",\u011A:"E",\u0147:"N",\u0158:"R",\u0160:"S",\u0164:"T",\u016E:"U",\u017D:"Z",\u0780:"h",\u0781:"sh",\u0782:"n",\u0783:"r",\u0784:"b",\u0785:"lh",\u0786:"k",\u0787:"a",\u0788:"v",\u0789:"m",\u078A:"f",\u078B:"dh",\u078C:"th",\u078D:"l",\u078E:"g",\u078F:"gn",\u0790:"s",\u0791:"d",\u0792:"z",\u0793:"t",\u0794:"y",\u0795:"p",\u0796:"j",\u0797:"ch",\u0798:"tt",\u0799:"hh",\u079A:"kh",\u079B:"th",\u079C:"z",\u079D:"sh",\u079E:"s",\u079F:"d",\u07A0:"t",\u07A1:"z",\u07A2:"a",\u07A3:"gh",\u07A4:"q",\u07A5:"w","\u07A6":"a","\u07A7":"aa","\u07A8":"i","\u07A9":"ee","\u07AA":"u","\u07AB":"oo","\u07AC":"e","\u07AD":"ey","\u07AE":"o","\u07AF":"oa","\u07B0":"",\u10D0:"a",\u10D1:"b",\u10D2:"g",\u10D3:"d",\u10D4:"e",\u10D5:"v",\u10D6:"z",\u10D7:"t",\u10D8:"i",\u10D9:"k",\u10DA:"l",\u10DB:"m",\u10DC:"n",\u10DD:"o",\u10DE:"p",\u10DF:"zh",\u10E0:"r",\u10E1:"s",\u10E2:"t",\u10E3:"u",\u10E4:"p",\u10E5:"k",\u10E6:"gh",\u10E7:"q",\u10E8:"sh",\u10E9:"ch",\u10EA:"ts",\u10EB:"dz",\u10EC:"ts",\u10ED:"ch",\u10EE:"kh",\u10EF:"j",\u10F0:"h",\u03B1:"a",\u03B2:"v",\u03B3:"g",\u03B4:"d",\u03B5:"e",\u03B6:"z",\u03B7:"i",\u03B8:"th",\u03B9:"i",\u03BA:"k",\u03BB:"l",\u03BC:"m",\u03BD:"n",\u03BE:"ks",\u03BF:"o",\u03C0:"p",\u03C1:"r",\u03C3:"s",\u03C4:"t",\u03C5:"y",\u03C6:"f",\u03C7:"x",\u03C8:"ps",\u03C9:"o",\u03AC:"a",\u03AD:"e",\u03AF:"i",\u03CC:"o",\u03CD:"y",\u03AE:"i",\u03CE:"o",\u03C2:"s",\u03CA:"i",\u03B0:"y",\u03CB:"y",\u0390:"i",\u0391:"A",\u0392:"B",\u0393:"G",\u0394:"D",\u0395:"E",\u0396:"Z",\u0397:"I",\u0398:"TH",\u0399:"I",\u039A:"K",\u039B:"L",\u039C:"M",\u039D:"N",\u039E:"KS",\u039F:"O",\u03A0:"P",\u03A1:"R",\u03A3:"S",\u03A4:"T",\u03A5:"Y",\u03A6:"F",\u03A7:"X",\u03A8:"PS",\u03A9:"O",\u0386:"A",\u0388:"E",\u038A:"I",\u038C:"O",\u038E:"Y",\u0389:"I",\u038F:"O",\u03AA:"I",\u03AB:"Y",\u0101:"a",\u0113:"e",\u0123:"g",\u012B:"i",\u0137:"k",\u013C:"l",\u0146:"n",\u016B:"u",\u0100:"A",\u0112:"E",\u0122:"G",\u012A:"I",\u0136:"k",\u013B:"L",\u0145:"N",\u016A:"U",\u040C:"Kj",\u045C:"kj",\u0409:"Lj",\u0459:"lj",\u040A:"Nj",\u045A:"nj",\u0422\u0441:"Ts",\u0442\u0441:"ts",\u0105:"a",\u0107:"c",\u0119:"e",\u0142:"l",\u0144:"n",\u015B:"s",\u017A:"z",\u017C:"z",\u0104:"A",\u0106:"C",\u0118:"E",\u0141:"L",\u0143:"N",\u015A:"S",\u0179:"Z",\u017B:"Z",\u0404:"Ye",\u0406:"I",\u0407:"Yi",\u0490:"G",\u0454:"ye",\u0456:"i",\u0457:"yi",\u0491:"g",\u0103:"a",\u0102:"A",\u0219:"s",\u0218:"S",\u021B:"t",\u021A:"T",\u0163:"t",\u0162:"T",\u0430:"a",\u0431:"b",\u0432:"v",\u0433:"g",\u0434:"d",\u0435:"e",\u0451:"yo",\u0436:"zh",\u0437:"z",\u0438:"i",\u0439:"i",\u043A:"k",\u043B:"l",\u043C:"m",\u043D:"n",\u043E:"o",\u043F:"p",\u0440:"r",\u0441:"s",\u0442:"t",\u0443:"u",\u0444:"f",\u0445:"kh",\u0446:"c",\u0447:"ch",\u0448:"sh",\u0449:"sh",\u044A:"",\u044B:"y",\u044C:"",\u044D:"e",\u044E:"yu",\u044F:"ya",\u0410:"A",\u0411:"B",\u0412:"V",\u0413:"G",\u0414:"D",\u0415:"E",\u0401:"Yo",\u0416:"Zh",\u0417:"Z",\u0418:"I",\u0419:"I",\u041A:"K",\u041B:"L",\u041C:"M",\u041D:"N",\u041E:"O",\u041F:"P",\u0420:"R",\u0421:"S",\u0422:"T",\u0423:"U",\u0424:"F",\u0425:"Kh",\u0426:"C",\u0427:"Ch",\u0428:"Sh",\u0429:"Sh",\u042A:"",\u042B:"Y",\u042C:"",\u042D:"E",\u042E:"Yu",\u042F:"Ya",\u0452:"dj",\u0458:"j",\u045B:"c",\u045F:"dz",\u0402:"Dj",\u0408:"j",\u040B:"C",\u040F:"Dz",\u013E:"l",\u013A:"l",\u0155:"r",\u013D:"L",\u0139:"L",\u0154:"R",\u015F:"s",\u015E:"S",\u0131:"i",\u0130:"I",\u011F:"g",\u011E:"G",\u1EA3:"a",\u1EA2:"A",\u1EB3:"a",\u1EB2:"A",\u1EA9:"a",\u1EA8:"A",\u0111:"d",\u0110:"D",\u1EB9:"e",\u1EB8:"E",\u1EBD:"e",\u1EBC:"E",\u1EBB:"e",\u1EBA:"E",\u1EBF:"e",\u1EBE:"E",\u1EC1:"e",\u1EC0:"E",\u1EC7:"e",\u1EC6:"E",\u1EC5:"e",\u1EC4:"E",\u1EC3:"e",\u1EC2:"E",\u1ECF:"o",\u1ECD:"o",\u1ECC:"o",\u1ED1:"o",\u1ED0:"O",\u1ED3:"o",\u1ED2:"O",\u1ED5:"o",\u1ED4:"O",\u1ED9:"o",\u1ED8:"O",\u1ED7:"o",\u1ED6:"O",\u01A1:"o",\u01A0:"O",\u1EDB:"o",\u1EDA:"O",\u1EDD:"o",\u1EDC:"O",\u1EE3:"o",\u1EE2:"O",\u1EE1:"o",\u1EE0:"O",\u1EDE:"o",\u1EDF:"o",\u1ECB:"i",\u1ECA:"I",\u0129:"i",\u0128:"I",\u1EC9:"i",\u1EC8:"i",\u1EE7:"u",\u1EE6:"U",\u1EE5:"u",\u1EE4:"U",\u0169:"u",\u0168:"U",\u01B0:"u",\u01AF:"U",\u1EE9:"u",\u1EE8:"U",\u1EEB:"u",\u1EEA:"U",\u1EF1:"u",\u1EF0:"U",\u1EEF:"u",\u1EEE:"U",\u1EED:"u",\u1EEC:"\u01B0",\u1EF7:"y",\u1EF6:"y",\u1EF3:"y",\u1EF2:"Y",\u1EF5:"y",\u1EF4:"Y",\u1EF9:"y",\u1EF8:"Y",\u1EA1:"a",\u1EA0:"A",\u1EA5:"a",\u1EA4:"A",\u1EA7:"a",\u1EA6:"A",\u1EAD:"a",\u1EAC:"A",\u1EAB:"a",\u1EAA:"A",\u1EAF:"a",\u1EAE:"A",\u1EB1:"a",\u1EB0:"A",\u1EB7:"a",\u1EB6:"A",\u1EB5:"a",\u1EB4:"A","\u24EA":"0","\u2460":"1","\u2461":"2","\u2462":"3","\u2463":"4","\u2464":"5","\u2465":"6","\u2466":"7","\u2467":"8","\u2468":"9","\u2469":"10","\u246A":"11","\u246B":"12","\u246C":"13","\u246D":"14","\u246E":"15","\u246F":"16","\u2470":"17","\u2471":"18","\u2472":"18","\u2473":"18","\u24F5":"1","\u24F6":"2","\u24F7":"3","\u24F8":"4","\u24F9":"5","\u24FA":"6","\u24FB":"7","\u24FC":"8","\u24FD":"9","\u24FE":"10","\u24FF":"0","\u24EB":"11","\u24EC":"12","\u24ED":"13","\u24EE":"14","\u24EF":"15","\u24F0":"16","\u24F1":"17","\u24F2":"18","\u24F3":"19","\u24F4":"20","\u24B6":"A","\u24B7":"B","\u24B8":"C","\u24B9":"D","\u24BA":"E","\u24BB":"F","\u24BC":"G","\u24BD":"H","\u24BE":"I","\u24BF":"J","\u24C0":"K","\u24C1":"L","\u24C2":"M","\u24C3":"N","\u24C4":"O","\u24C5":"P","\u24C6":"Q","\u24C7":"R","\u24C8":"S","\u24C9":"T","\u24CA":"U","\u24CB":"V","\u24CC":"W","\u24CD":"X","\u24CE":"Y","\u24CF":"Z","\u24D0":"a","\u24D1":"b","\u24D2":"c","\u24D3":"d","\u24D4":"e","\u24D5":"f","\u24D6":"g","\u24D7":"h","\u24D8":"i","\u24D9":"j","\u24DA":"k","\u24DB":"l","\u24DC":"m","\u24DD":"n","\u24DE":"o","\u24DF":"p","\u24E0":"q","\u24E1":"r","\u24E2":"s","\u24E3":"t","\u24E4":"u","\u24E6":"v","\u24E5":"w","\u24E7":"x","\u24E8":"y","\u24E9":"z","\u201C":'"',"\u201D":'"',"\u2018":"'","\u2019":"'","\u2202":"d",\u0192:"f","\u2122":"(TM)","\xA9":"(C)",\u0153:"oe",\u0152:"OE","\xAE":"(R)","\u2020":"+","\u2120":"(SM)","\u2026":"...","\u02DA":"o",\u00BA:"o",\u00AA:"a","\u2022":"*","\u104A":",","\u104B":".",$:"USD","\u20AC":"EUR","\u20A2":"BRN","\u20A3":"FRF","\xA3":"GBP","\u20A4":"ITL","\u20A6":"NGN","\u20A7":"ESP","\u20A9":"KRW","\u20AA":"ILS","\u20AB":"VND","\u20AD":"LAK","\u20AE":"MNT","\u20AF":"GRD","\u20B1":"ARS","\u20B2":"PYG","\u20B3":"ARA","\u20B4":"UAH","\u20B5":"GHS","\xA2":"cent","\xA5":"CNY",\u5143:"CNY",\u5186:"YEN","\uFDFC":"IRR","\u20A0":"EWE","\u0E3F":"THB","\u20A8":"INR","\u20B9":"INR","\u20B0":"PF","\u20BA":"TRY","\u060B":"AFN","\u20BC":"AZN",\u043B\u0432:"BGN","\u17DB":"KHR","\u20A1":"CRC","\u20B8":"KZT",\u0434\u0435\u043D:"MKD",z\u0142:"PLN","\u20BD":"RUB","\u20BE":"GEL"},o=["\u103A","\u07B0"],n={"\u102C":"a","\u102B":"a","\u1031":"e","\u1032":"e","\u102D":"i","\u102E":"i","\u102D\u102F":"o","\u102F":"u","\u1030":"u","\u1031\u102B\u1004\u103A":"aung","\u1031\u102C":"aw","\u1031\u102C\u103A":"aw","\u1031\u102B":"aw","\u1031\u102B\u103A":"aw","\u103A":"\u103A","\u1000\u103A":"et","\u102D\u102F\u1000\u103A":"aik","\u1031\u102C\u1000\u103A":"auk","\u1004\u103A":"in","\u102D\u102F\u1004\u103A":"aing","\u1031\u102C\u1004\u103A":"aung","\u1005\u103A":"it","\u100A\u103A":"i","\u1010\u103A":"at","\u102D\u1010\u103A":"eik","\u102F\u1010\u103A":"ok","\u103D\u1010\u103A":"ut","\u1031\u1010\u103A":"it","\u1012\u103A":"d","\u102D\u102F\u1012\u103A":"ok","\u102F\u1012\u103A":"ait","\u1014\u103A":"an","\u102C\u1014\u103A":"an","\u102D\u1014\u103A":"ein","\u102F\u1014\u103A":"on","\u103D\u1014\u103A":"un","\u1015\u103A":"at","\u102D\u1015\u103A":"eik","\u102F\u1015\u103A":"ok","\u103D\u1015\u103A":"ut","\u1014\u103A\u102F\u1015\u103A":"nub","\u1019\u103A":"an","\u102D\u1019\u103A":"ein","\u102F\u1019\u103A":"on","\u103D\u1019\u103A":"un","\u101A\u103A":"e","\u102D\u102F\u101C\u103A":"ol","\u1009\u103A":"in","\u1036":"an","\u102D\u1036":"ein","\u102F\u1036":"on","\u07A6\u0787\u07B0":"ah","\u07A6\u0781\u07B0":"ah"},r={en:{},az:{\u00E7:"c",\u0259:"e",\u011F:"g",\u0131:"i",\u00F6:"o",\u015F:"s",\u00FC:"u",\u00C7:"C",\u018F:"E",\u011E:"G",\u0130:"I",\u00D6:"O",\u015E:"S",\u00DC:"U"},cs:{\u010D:"c",\u010F:"d",\u011B:"e",\u0148:"n",\u0159:"r",\u0161:"s",\u0165:"t",\u016F:"u",\u017E:"z",\u010C:"C",\u010E:"D",\u011A:"E",\u0147:"N",\u0158:"R",\u0160:"S",\u0164:"T",\u016E:"U",\u017D:"Z"},fi:{\u00E4:"a",\u00C4:"A",\u00F6:"o",\u00D6:"O"},hu:{\u00E4:"a",\u00C4:"A",\u00F6:"o",\u00D6:"O",\u00FC:"u",\u00DC:"U",\u0171:"u",\u0170:"U"},lt:{\u0105:"a",\u010D:"c",\u0119:"e",\u0117:"e",\u012F:"i",\u0161:"s",\u0173:"u",\u016B:"u",\u017E:"z",\u0104:"A",\u010C:"C",\u0118:"E",\u0116:"E",\u012E:"I",\u0160:"S",\u0172:"U",\u016A:"U"},lv:{\u0101:"a",\u010D:"c",\u0113:"e",\u0123:"g",\u012B:"i",\u0137:"k",\u013C:"l",\u0146:"n",\u0161:"s",\u016B:"u",\u017E:"z",\u0100:"A",\u010C:"C",\u0112:"E",\u0122:"G",\u012A:"i",\u0136:"k",\u013B:"L",\u0145:"N",\u0160:"S",\u016A:"u",\u017D:"Z"},pl:{\u0105:"a",\u0107:"c",\u0119:"e",\u0142:"l",\u0144:"n",\u00F3:"o",\u015B:"s",\u017A:"z",\u017C:"z",\u0104:"A",\u0106:"C",\u0118:"e",\u0141:"L",\u0143:"N",\u00D3:"O",\u015A:"S",\u0179:"Z",\u017B:"Z"},sv:{\u00E4:"a",\u00C4:"A",\u00F6:"o",\u00D6:"O"},sk:{\u00E4:"a",\u00C4:"A"},sr:{\u0459:"lj",\u045A:"nj",\u0409:"Lj",\u040A:"Nj",\u0111:"dj",\u0110:"Dj"},tr:{\u00DC:"U",\u00D6:"O",\u00FC:"u",\u00F6:"o"}},s={ar:{"\u2206":"delta","\u221E":"la-nihaya","\u2665":"hob","&":"wa","|":"aw","<":"aqal-men",">":"akbar-men","\u2211":"majmou","\xA4":"omla"},az:{},ca:{"\u2206":"delta","\u221E":"infinit","\u2665":"amor","&":"i","|":"o","<":"menys que",">":"mes que","\u2211":"suma dels","\xA4":"moneda"},cs:{"\u2206":"delta","\u221E":"nekonecno","\u2665":"laska","&":"a","|":"nebo","<":"mensi nez",">":"vetsi nez","\u2211":"soucet","\xA4":"mena"},de:{"\u2206":"delta","\u221E":"unendlich","\u2665":"Liebe","&":"und","|":"oder","<":"kleiner als",">":"groesser als","\u2211":"Summe von","\xA4":"Waehrung"},dv:{"\u2206":"delta","\u221E":"kolunulaa","\u2665":"loabi","&":"aai","|":"noonee","<":"ah vure kuda",">":"ah vure bodu","\u2211":"jumula","\xA4":"faisaa"},en:{"\u2206":"delta","\u221E":"infinity","\u2665":"love","&":"and","|":"or","<":"less than",">":"greater than","\u2211":"sum","\xA4":"currency"},es:{"\u2206":"delta","\u221E":"infinito","\u2665":"amor","&":"y","|":"u","<":"menos que",">":"mas que","\u2211":"suma de los","\xA4":"moneda"},fa:{"\u2206":"delta","\u221E":"bi-nahayat","\u2665":"eshgh","&":"va","|":"ya","<":"kamtar-az",">":"bishtar-az","\u2211":"majmooe","\xA4":"vahed"},fi:{"\u2206":"delta","\u221E":"aarettomyys","\u2665":"rakkaus","&":"ja","|":"tai","<":"pienempi kuin",">":"suurempi kuin","\u2211":"summa","\xA4":"valuutta"},fr:{"\u2206":"delta","\u221E":"infiniment","\u2665":"Amour","&":"et","|":"ou","<":"moins que",">":"superieure a","\u2211":"somme des","\xA4":"monnaie"},ge:{"\u2206":"delta","\u221E":"usasruloba","\u2665":"siqvaruli","&":"da","|":"an","<":"naklebi",">":"meti","\u2211":"jami","\xA4":"valuta"},gr:{},hu:{"\u2206":"delta","\u221E":"vegtelen","\u2665":"szerelem","&":"es","|":"vagy","<":"kisebb mint",">":"nagyobb mint","\u2211":"szumma","\xA4":"penznem"},it:{"\u2206":"delta","\u221E":"infinito","\u2665":"amore","&":"e","|":"o","<":"minore di",">":"maggiore di","\u2211":"somma","\xA4":"moneta"},lt:{"\u2206":"delta","\u221E":"begalybe","\u2665":"meile","&":"ir","|":"ar","<":"maziau nei",">":"daugiau nei","\u2211":"suma","\xA4":"valiuta"},lv:{"\u2206":"delta","\u221E":"bezgaliba","\u2665":"milestiba","&":"un","|":"vai","<":"mazak neka",">":"lielaks neka","\u2211":"summa","\xA4":"valuta"},my:{"\u2206":"kwahkhyaet","\u221E":"asaonasme","\u2665":"akhyait","&":"nhin","|":"tho","<":"ngethaw",">":"kyithaw","\u2211":"paungld","\xA4":"ngwekye"},mk:{},nl:{"\u2206":"delta","\u221E":"oneindig","\u2665":"liefde","&":"en","|":"of","<":"kleiner dan",">":"groter dan","\u2211":"som","\xA4":"valuta"},pl:{"\u2206":"delta","\u221E":"nieskonczonosc","\u2665":"milosc","&":"i","|":"lub","<":"mniejsze niz",">":"wieksze niz","\u2211":"suma","\xA4":"waluta"},pt:{"\u2206":"delta","\u221E":"infinito","\u2665":"amor","&":"e","|":"ou","<":"menor que",">":"maior que","\u2211":"soma","\xA4":"moeda"},ro:{"\u2206":"delta","\u221E":"infinit","\u2665":"dragoste","&":"si","|":"sau","<":"mai mic ca",">":"mai mare ca","\u2211":"suma","\xA4":"valuta"},ru:{"\u2206":"delta","\u221E":"beskonechno","\u2665":"lubov","&":"i","|":"ili","<":"menshe",">":"bolshe","\u2211":"summa","\xA4":"valjuta"},sk:{"\u2206":"delta","\u221E":"nekonecno","\u2665":"laska","&":"a","|":"alebo","<":"menej ako",">":"viac ako","\u2211":"sucet","\xA4":"mena"},sr:{},tr:{"\u2206":"delta","\u221E":"sonsuzluk","\u2665":"ask","&":"ve","|":"veya","<":"kucuktur",">":"buyuktur","\u2211":"toplam","\xA4":"para birimi"},uk:{"\u2206":"delta","\u221E":"bezkinechnist","\u2665":"lubov","&":"i","|":"abo","<":"menshe",">":"bilshe","\u2211":"suma","\xA4":"valjuta"},vn:{"\u2206":"delta","\u221E":"vo cuc","\u2665":"yeu","&":"va","|":"hoac","<":"nho hon",">":"lon hon","\u2211":"tong","\xA4":"tien te"}},i=[";","?",":","@","&","=","+","$",",","/"].join(""),p=[";","?",":","@","&","=","+","$",","].join(""),c=[".","!","~","*","'","(",")"].join(""),l=function(d,g){var v="-",h="",H="",et=!0,U={},zt,De,Ie,tt,ot,Yt,Oe,ne,Xt,K,S,Pe,M,re,B="";if(typeof d!="string")return"";if(typeof g=="string"&&(v=g),Oe=s.en,ne=r.en,typeof g=="object"){zt=g.maintainCase||!1,U=g.custom&&typeof g.custom=="object"?g.custom:U,Ie=+g.truncate>1&&g.truncate||!1,tt=g.uric||!1,ot=g.uricNoSlash||!1,Yt=g.mark||!1,et=!(g.symbols===!1||g.lang===!1),v=g.separator||v,tt&&(B+=i),ot&&(B+=p),Yt&&(B+=c),Oe=g.lang&&s[g.lang]&&et?s[g.lang]:et?s.en:{},ne=g.lang&&r[g.lang]?r[g.lang]:g.lang===!1||g.lang===!0?{}:r.en,g.titleCase&&typeof g.titleCase.length=="number"&&Array.prototype.toString.call(g.titleCase)?(g.titleCase.forEach(function(A){U[A+""]=A+""}),De=!0):De=!!g.titleCase,g.custom&&typeof g.custom.length=="number"&&Array.prototype.toString.call(g.custom)&&g.custom.forEach(function(A){U[A+""]=A+""}),Object.keys(U).forEach(function(A){var ve;A.length>1?ve=new RegExp("\\b"+_(A)+"\\b","gi"):ve=new RegExp(_(A),"gi"),d=d.replace(ve,U[A])});for(S in U)B+=S}for(B+=v,B=_(B),d=d.replace(/(^\s+|\s+$)/g,""),M=!1,re=!1,K=0,Pe=d.length;K<Pe;K++)S=d[K],E(S,U)?M=!1:ne[S]?(S=M&&ne[S].match(/[A-Za-z0-9]/)?" "+ne[S]:ne[S],M=!1):S in e?(K+1<Pe&&o.indexOf(d[K+1])>=0?(H+=S,S=""):re===!0?(S=n[H]+e[S],H=""):S=M&&e[S].match(/[A-Za-z0-9]/)?" "+e[S]:e[S],M=!1,re=!1):S in n?(H+=S,S="",K===Pe-1&&(S=n[H]),re=!0):Oe[S]&&!(tt&&i.indexOf(S)!==-1)&&!(ot&&p.indexOf(S)!==-1)?(S=M||h.substr(-1).match(/[A-Za-z0-9]/)?v+Oe[S]:Oe[S],S+=d[K+1]!==void 0&&d[K+1].match(/[A-Za-z0-9]/)?v:"",M=!0):(re===!0?(S=n[H]+S,H="",re=!1):M&&(/[A-Za-z0-9]/.test(S)||h.substr(-1).match(/A-Za-z0-9]/))&&(S=" "+S),M=!1),h+=S.replace(new RegExp("[^\\w\\s"+B+"_-]","g"),v);return De&&(h=h.replace(/(\w)(\S*)/g,function(A,ve,Wt){var nt=ve.toUpperCase()+(Wt!==null?Wt:"");return Object.keys(U).indexOf(nt.toLowerCase())<0?nt:nt.toLowerCase()})),h=h.replace(/\s+/g,v).replace(new RegExp("\\"+v+"+","g"),v).replace(new RegExp("(^\\"+v+"+|\\"+v+"+$)","g"),""),Ie&&h.length>Ie&&(Xt=h.charAt(Ie)===v,h=h.slice(0,Ie),Xt||(h=h.slice(0,h.lastIndexOf(v)))),!zt&&!De&&(h=h.toLowerCase()),h},m=function(d){return function(v){return l(v,d)}},_=function(d){return d.replace(/[-\\^$*+?.()|[\]{}\/]/g,"\\$&")},E=function(y,d){for(var g in d)if(d[g]===y)return!0};if(typeof Ne!="undefined"&&Ne.exports)Ne.exports=l,Ne.exports.createSlug=m;else if(typeof define!="undefined"&&define.amd)define([],function(){return l});else try{if(t.getSlug||t.createSlug)throw"speakingurl: globals exists /(getSlug|createSlug)/";t.getSlug=l,t.createSlug=m}catch(y){}})(Ln)});var Hn=rt((jp,wn)=>{"use strict";a();wn.exports=Mn()});a();a();a();a();a();a();var q=typeof navigator!="undefined",u=typeof window!="undefined"?window:typeof globalThis!="undefined"?globalThis:typeof global!="undefined"?global:{},li=typeof u.chrome!="undefined"&&!!u.chrome.devtools,ci=q&&u.self!==u.top,Jt,pi=typeof navigator!="undefined"&&((Jt=navigator.userAgent)==null?void 0:Jt.toLowerCase().includes("electron")),Qt=typeof window!="undefined"&&!!window.__NUXT__;a();var oo=Zt(to(),1);var Kr=/(?:^|[-_/])(\w)/g,Fr=/-(\w)/g,Gr=/([a-z0-9])([A-Z])/g;function no(t,e){return e?e.toUpperCase():""}function xe(t){return t&&`${t}`.replace(Kr,no)}function ro(t){return t&&t.replace(Fr,no)}function so(t){return t&&t.replace(Gr,(e,o,n)=>`${o}-${n}`).toLowerCase()}function io(t,e){let o=t.replace(/^[a-z]:/i,"").replace(/\\/g,"/");o.endsWith(`index${e}`)&&(o=o.replace(`/index${e}`,e));let n=o.lastIndexOf("/"),r=o.substring(n+1);if(e){let s=r.lastIndexOf(e);return r.substring(0,s)}return""}var st=(0,oo.default)({circles:!0});a();function ao(t){if(u.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__){t();return}Object.defineProperty(u,"__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__",{set(e){e&&t()},configurable:!0})}a();a();a();a();a();function Br(t){var o;let e=t.name||t._componentTag||t.__VUE_DEVTOOLS_COMPONENT_GUSSED_NAME__||t.__name;return e==="index"&&((o=t.__file)!=null&&o.endsWith("index.vue"))?"":e}function jr(t){let e=t.__file;if(e)return xe(io(e,".vue"))}function lo(t,e){return t.type.__VUE_DEVTOOLS_COMPONENT_GUSSED_NAME__=e,e}function x(t){if(t.__VUE_DEVTOOLS_NEXT_APP_RECORD__)return t.__VUE_DEVTOOLS_NEXT_APP_RECORD__;if(t.root)return t.appContext.app.__VUE_DEVTOOLS_NEXT_APP_RECORD__}async function Re(t){let{app:e,uid:o,instance:n}=t;try{if(n.__VUE_DEVTOOLS_NEXT_UID__)return n.__VUE_DEVTOOLS_NEXT_UID__;let r=await x(e);if(!r)return null;let s=r.rootInstance===n;return`${r.id}:${s?"root":o}`}catch(r){}}function ie(t){var n,r;let e=(n=t.subTree)==null?void 0:n.type,o=x(t);return o?((r=o==null?void 0:o.types)==null?void 0:r.Fragment)===e:!1}function Ae(t){return t._isBeingDestroyed||t.isUnmounted}function N(t){var n,r,s;let e=Br((t==null?void 0:t.type)||{});if(e)return e;if((t==null?void 0:t.root)===t)return"Root";for(let i in(r=(n=t.parent)==null?void 0:n.type)==null?void 0:r.components)if(t.parent.type.components[i]===(t==null?void 0:t.type))return lo(t,i);for(let i in(s=t.appContext)==null?void 0:s.components)if(t.appContext.components[i]===(t==null?void 0:t.type))return lo(t,i);let o=jr((t==null?void 0:t.type)||{});return o||"Anonymous Component"}function ae(t){var n,r,s;let e=(s=(r=(n=t==null?void 0:t.appContext)==null?void 0:n.app)==null?void 0:r.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__)!=null?s:0,o=t===(t==null?void 0:t.root)?"root":t.uid;return`${e}:${o}`}function co(t){return t==null?"":typeof t=="number"?t:typeof t=="string"?`'${t}'`:Array.isArray(t)?"Array":"Object"}function F(t){try{return t()}catch(e){return e}}function k(t,e){return e=e||`${t.id}:root`,t.instanceMap.get(e)||t.instanceMap.get(":root")}function Z(t,e,o=!1){return o||typeof t=="object"&&t!==null?e in t:!1}function $r(){let t={top:0,bottom:0,left:0,right:0,get width(){return t.right-t.left},get height(){return t.bottom-t.top}};return t}var ke;function zr(t){return ke||(ke=document.createRange()),ke.selectNode(t),ke.getBoundingClientRect()}function Yr(t){let e=$r();if(!t.children)return e;for(let o=0,n=t.children.length;o<n;o++){let r=t.children[o],s;if(r.component)s=G(r.component);else if(r.el){let i=r.el;i.nodeType===1||i.getBoundingClientRect?s=i.getBoundingClientRect():i.nodeType===3&&i.data.trim()&&(s=zr(i))}s&&Xr(e,s)}return e}function Xr(t,e){return(!t.top||e.top<t.top)&&(t.top=e.top),(!t.bottom||e.bottom>t.bottom)&&(t.bottom=e.bottom),(!t.left||e.left<t.left)&&(t.left=e.left),(!t.right||e.right>t.right)&&(t.right=e.right),t}var po={top:0,left:0,right:0,bottom:0,width:0,height:0};function G(t){let e=t.subTree.el;return typeof window=="undefined"?po:ie(t)?Yr(t.subTree):(e==null?void 0:e.nodeType)===1?e==null?void 0:e.getBoundingClientRect():t.subTree.component?G(t.subTree.component):po}a();function j(t){return ie(t)?Wr(t.subTree):t.subTree?[t.subTree.el]:[]}function Wr(t){if(!t.children)return[];let e=[];return t.children.forEach(o=>{o.component?e.push(...j(o.component)):o!=null&&o.el&&e.push(o.el)}),e}var uo="__vue-devtools-component-inspector__",fo="__vue-devtools-component-inspector__card__",mo="__vue-devtools-component-inspector__name__",_o="__vue-devtools-component-inspector__indicator__",Eo={display:"block",zIndex:2147483640,position:"fixed",backgroundColor:"#42b88325",border:"1px solid #42b88350",borderRadius:"5px",transition:"all 0.1s ease-in",pointerEvents:"none"},qr={fontFamily:"Arial, Helvetica, sans-serif",padding:"5px 8px",borderRadius:"4px",textAlign:"left",position:"absolute",left:0,color:"#e9e9e9",fontSize:"14px",fontWeight:600,lineHeight:"24px",backgroundColor:"#42b883",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)"},Zr={display:"inline-block",fontWeight:400,fontStyle:"normal",fontSize:"12px",opacity:.7};function le(){return document.getElementById(uo)}function Jr(){return document.getElementById(fo)}function Qr(){return document.getElementById(_o)}function es(){return document.getElementById(mo)}function lt(t){return{left:`${Math.round(t.left*100)/100}px`,top:`${Math.round(t.top*100)/100}px`,width:`${Math.round(t.width*100)/100}px`,height:`${Math.round(t.height*100)/100}px`}}function ct(t){var s;let e=document.createElement("div");e.id=(s=t.elementId)!=null?s:uo,Object.assign(e.style,{...Eo,...lt(t.bounds),...t.style});let o=document.createElement("span");o.id=fo,Object.assign(o.style,{...qr,top:t.bounds.top<35?0:"-35px"});let n=document.createElement("span");n.id=mo,n.innerHTML=`&lt;${t.name}&gt;&nbsp;&nbsp;`;let r=document.createElement("i");return r.id=_o,r.innerHTML=`${Math.round(t.bounds.width*100)/100} x ${Math.round(t.bounds.height*100)/100}`,Object.assign(r.style,Zr),o.appendChild(n),o.appendChild(r),e.appendChild(o),document.body.appendChild(e),e}function pt(t){let e=le(),o=Jr(),n=es(),r=Qr();e&&(Object.assign(e.style,{...Eo,...lt(t.bounds)}),Object.assign(o.style,{top:t.bounds.top<35?0:"-35px"}),n.innerHTML=`&lt;${t.name}&gt;&nbsp;&nbsp;`,r.innerHTML=`${Math.round(t.bounds.width*100)/100} x ${Math.round(t.bounds.height*100)/100}`)}function go(t){let e=G(t);if(!e.width&&!e.height)return;let o=N(t);le()?pt({bounds:e,name:o}):ct({bounds:e,name:o})}function ut(){let t=le();t&&(t.style.display="none")}var it=null;function at(t){let e=t.target;if(e){let o=e.__vueParentComponent;if(o&&(it=o,o.vnode.el)){let r=G(o),s=N(o);le()?pt({bounds:r,name:s}):ct({bounds:r,name:s})}}}function ts(t,e){if(t.preventDefault(),t.stopPropagation(),it){let o=ae(it);e(o)}}var be=null;function To(){ut(),window.removeEventListener("mouseover",at),window.removeEventListener("click",be,!0),be=null}function yo(){return window.addEventListener("mouseover",at),new Promise(t=>{function e(o){o.preventDefault(),o.stopPropagation(),ts(o,n=>{window.removeEventListener("click",e,!0),be=null,window.removeEventListener("mouseover",at);let r=le();r&&(r.style.display="none"),t(JSON.stringify({id:n}))})}be=e,window.addEventListener("click",e,!0)})}function Io(t){let e=k(O.value,t.id);if(e){let[o]=j(e);if(typeof o.scrollIntoView=="function")o.scrollIntoView({behavior:"smooth"});else{let n=G(e),r=document.createElement("div"),s={...lt(n),position:"absolute"};Object.assign(r.style,s),document.body.appendChild(r),r.scrollIntoView({behavior:"smooth"}),setTimeout(()=>{document.body.removeChild(r)},2e3)}setTimeout(()=>{let n=G(e);if(n.width||n.height){let r=N(e),s=le();s?pt({...t,name:r,bounds:n}):ct({...t,name:r,bounds:n}),setTimeout(()=>{s&&(s.style.display="none")},1500)}},1200)}}a();var Oo,vo;(vo=(Oo=u).__VUE_DEVTOOLS_COMPONENT_INSPECTOR_ENABLED__)!=null||(Oo.__VUE_DEVTOOLS_COMPONENT_INSPECTOR_ENABLED__=!0);function os(t){let e=0,o=setInterval(()=>{u.__VUE_INSPECTOR__&&(clearInterval(o),e+=30,t()),e>=5e3&&clearInterval(o)},30)}function ns(){let t=u.__VUE_INSPECTOR__,e=t.openInEditor;t.openInEditor=async(...o)=>{t.disable(),e(...o)}}function So(){return new Promise(t=>{function e(){ns(),t(u.__VUE_INSPECTOR__)}u.__VUE_INSPECTOR__?e():os(()=>{e()})})}a();a();function rs(t){return!!(t&&t.__v_isReadonly)}function ft(t){return rs(t)?ft(t.__v_raw):!!(t&&t.__v_isReactive)}function Ve(t){return!!(t&&t.__v_isRef===!0)}function ce(t){let e=t&&t.__v_raw;return e?ce(e):t}var Bi=Symbol.for("v-fgt");var Se=class{constructor(){this.refEditor=new dt}set(e,o,n,r){let s=Array.isArray(o)?o:o.split("."),i=!1;for(;s.length>1;){let l=s.shift();e instanceof Map?e=e.get(l):e instanceof Set?e=Array.from(e.values())[l]:e=e[l],this.refEditor.isRef(e)&&(e=this.refEditor.get(e))}let p=s[0],c=this.refEditor.get(e)[p];r?r(e,p,n):this.refEditor.isRef(c)?this.refEditor.set(c,n):e[p]=n}get(e,o){let n=Array.isArray(o)?o:o.split(".");for(let r=0;r<n.length;r++)if(e instanceof Map?e=e.get(n[r]):e=e[n[r]],this.refEditor.isRef(e)&&(e=this.refEditor.get(e)),!e)return;return e}has(e,o,n=!1){if(typeof e=="undefined")return!1;let r=Array.isArray(o)?o.slice():o.split("."),s=n?2:1;for(;e&&r.length>s;){let i=r.shift();e=e[i],this.refEditor.isRef(e)&&(e=this.refEditor.get(e))}return e!=null&&Object.prototype.hasOwnProperty.call(e,r[0])}createDefaultSetCallback(e){return(o,n,r)=>{if((e.remove||e.newKey)&&(Array.isArray(o)?o.splice(n,1):ce(o)instanceof Map?o.delete(n):ce(o)instanceof Set?o.delete(Array.from(o.values())[n]):Reflect.deleteProperty(o,n)),!e.remove){let s=o[e.newKey||n];this.refEditor.isRef(s)?this.refEditor.set(s,r):ce(o)instanceof Map?o.set(e.newKey||n,r):ce(o)instanceof Set?o.add(r):o[e.newKey||n]=r}}}},dt=class{set(e,o){if(Ve(e))e.value=o;else{if(e instanceof Set&&Array.isArray(o)){e.clear(),o.forEach(s=>e.add(s));return}let n=Object.keys(o);if(e instanceof Map){let s=new Set(e.keys());n.forEach(i=>{e.set(i,Reflect.get(o,i)),s.delete(i)}),s.forEach(i=>e.delete(i));return}let r=new Set(Object.keys(e));n.forEach(s=>{Reflect.set(e,s,Reflect.get(o,s)),r.delete(s)}),r.forEach(s=>Reflect.deleteProperty(e,s))}}get(e){return Ve(e)?e.value:e}isRef(e){return Ve(e)||ft(e)}};async function ss(t,e){let{path:o,nodeId:n,state:r,type:s}=t,i=k(O.value,n);if(!i)return;let p=o.slice(),c;Object.keys(i.props).includes(o[0])?c=i.props:i.devtoolsRawSetupState&&Object.keys(i.devtoolsRawSetupState).includes(o[0])?c=i.devtoolsRawSetupState:i.data&&Object.keys(i.data).includes(o[0])?c=i.data:c=i.proxy,c&&p&&(r.type,e.set(c,p,r.value,e.createDefaultSetCallback(r)))}var is=new Se;async function ho(t){ss(t,is)}a();a();a();var as={trailing:!0};function R(t,e=25,o={}){if(o={...as,...o},!Number.isFinite(e))throw new TypeError("Expected `wait` to be a finite number");let n,r,s=[],i,p,c=(l,m)=>(i=ls(t,l,m),i.finally(()=>{if(i=null,o.trailing&&p&&!r){let _=c(l,p);return p=null,_}}),i);return function(...l){return i?(o.trailing&&(p=l),i):new Promise(m=>{let _=!r&&o.leading;clearTimeout(r),r=setTimeout(()=>{r=null;let E=o.leading?n:c(this,l);for(let y of s)y(E);s=[]},e),_?(n=c(this,l),m(n)):s.push(m)})}}async function ls(t,e,o){return await t.apply(e,o)}a();var cs="__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS_STATE__";function Co(){if(!q||typeof localStorage=="undefined"||localStorage===null)return{recordingState:!1,mouseEventEnabled:!1,keyboardEventEnabled:!1,componentEventEnabled:!1,performanceEventEnabled:!1,selected:""};let t=localStorage.getItem(cs);return t?JSON.parse(t):{recordingState:!1,mouseEventEnabled:!1,keyboardEventEnabled:!1,componentEventEnabled:!1,performanceEventEnabled:!1,selected:""}}a();a();function _t(t,e={},o){for(let n in t){let r=t[n],s=o?`${o}:${n}`:n;typeof r=="object"&&r!==null?_t(r,e,s):typeof r=="function"&&(e[s]=r)}return e}var ps={run:t=>t()},us=()=>ps,No=typeof console.createTask!="undefined"?console.createTask:us;function fs(t,e){let o=e.shift(),n=No(o);return t.reduce((r,s)=>r.then(()=>n.run(()=>s(...e))),Promise.resolve())}function ds(t,e){let o=e.shift(),n=No(o);return Promise.all(t.map(r=>n.run(()=>r(...e))))}function mt(t,e){for(let o of[...t])o(e)}var Et=class{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(e,o,n={}){if(!e||typeof o!="function")return()=>{};let r=e,s;for(;this._deprecatedHooks[e];)s=this._deprecatedHooks[e],e=s.to;if(s&&!n.allowDeprecated){let i=s.message;i||(i=`${r} hook has been deprecated`+(s.to?`, please use ${s.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(i)||(console.warn(i),this._deprecatedMessages.add(i))}if(!o.name)try{Object.defineProperty(o,"name",{get:()=>"_"+e.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch(i){}return this._hooks[e]=this._hooks[e]||[],this._hooks[e].push(o),()=>{o&&(this.removeHook(e,o),o=void 0)}}hookOnce(e,o){let n,r=(...s)=>(typeof n=="function"&&n(),n=void 0,r=void 0,o(...s));return n=this.hook(e,r),n}removeHook(e,o){if(this._hooks[e]){let n=this._hooks[e].indexOf(o);n!==-1&&this._hooks[e].splice(n,1),this._hooks[e].length===0&&delete this._hooks[e]}}deprecateHook(e,o){this._deprecatedHooks[e]=typeof o=="string"?{to:o}:o;let n=this._hooks[e]||[];delete this._hooks[e];for(let r of n)this.hook(e,r)}deprecateHooks(e){Object.assign(this._deprecatedHooks,e);for(let o in e)this.deprecateHook(o,e[o])}addHooks(e){let o=_t(e),n=Object.keys(o).map(r=>this.hook(r,o[r]));return()=>{for(let r of n.splice(0,n.length))r()}}removeHooks(e){let o=_t(e);for(let n in o)this.removeHook(n,o[n])}removeAllHooks(){for(let e in this._hooks)delete this._hooks[e]}callHook(e,...o){return o.unshift(e),this.callHookWith(fs,e,...o)}callHookParallel(e,...o){return o.unshift(e),this.callHookWith(ds,e,...o)}callHookWith(e,o,...n){let r=this._before||this._after?{name:o,args:n,context:{}}:void 0;this._before&&mt(this._before,r);let s=e(o in this._hooks?[...this._hooks[o]]:[],n);return s instanceof Promise?s.finally(()=>{this._after&&r&&mt(this._after,r)}):(this._after&&r&&mt(this._after,r),s)}beforeEach(e){return this._before=this._before||[],this._before.push(e),()=>{if(this._before!==void 0){let o=this._before.indexOf(e);o!==-1&&this._before.splice(o,1)}}}afterEach(e){return this._after=this._after||[],this._after.push(e),()=>{if(this._after!==void 0){let o=this._after.indexOf(e);o!==-1&&this._after.splice(o,1)}}}};function Le(){return new Et}a();a();var Do,Po;(Po=(Do=u).__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS)!=null||(Do.__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS=[]);var xo=new Proxy(u.__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS,{get(t,e,o){return Reflect.get(t,e,o)}});function Ro(t,e){T.timelineLayersState[e.id]=!1,xo.push({...t,descriptorId:e.id,appRecord:x(e.app)})}var Ao,ko;(ko=(Ao=u).__VUE_DEVTOOLS_KIT_INSPECTOR__)!=null||(Ao.__VUE_DEVTOOLS_KIT_INSPECTOR__=[]);var gt=new Proxy(u.__VUE_DEVTOOLS_KIT_INSPECTOR__,{get(t,e,o){return Reflect.get(t,e,o)}}),Tt=R(()=>{D.hooks.callHook("sendInspectorToClient",yt())});function bo(t,e){var o,n;gt.push({options:t,descriptor:e,treeFilterPlaceholder:(o=t.treeFilterPlaceholder)!=null?o:"Search tree...",stateFilterPlaceholder:(n=t.stateFilterPlaceholder)!=null?n:"Search state...",treeFilter:"",selectedNodeId:"",appRecord:x(e.app)}),Tt()}function yt(){return gt.filter(t=>t.descriptor.app===O.value.app).filter(t=>t.descriptor.id!=="components").map(t=>{var n;let e=t.descriptor,o=t.options;return{id:o.id,label:o.label,logo:e.logo,icon:`custom-ic-baseline-${(n=o==null?void 0:o.icon)==null?void 0:n.replace(/_/g,"-")}`,packageName:e.packageName,homepage:e.homepage,pluginId:e.id}})}function pe(t,e){return gt.find(o=>o.options.id===t&&(e?o.descriptor.app===e:!0))}function Vo(){let t=Le();t.hook("addInspector",({inspector:n,plugin:r})=>{bo(n,r.descriptor)});let e=R(async({inspectorId:n,plugin:r})=>{var p;if(!n||!((p=r==null?void 0:r.descriptor)!=null&&p.app)||T.highPerfModeEnabled)return;let s=pe(n,r.descriptor.app),i={app:r.descriptor.app,inspectorId:n,filter:(s==null?void 0:s.treeFilter)||"",rootNodes:[]};await new Promise(c=>{t.callHookWith(async l=>{await Promise.all(l.map(m=>m(i))),c()},"getInspectorTree")}),t.callHookWith(async c=>{await Promise.all(c.map(l=>l({inspectorId:n,rootNodes:i.rootNodes})))},"sendInspectorTreeToClient")},120);t.hook("sendInspectorTree",e);let o=R(async({inspectorId:n,plugin:r})=>{var c;if(!n||!((c=r==null?void 0:r.descriptor)!=null&&c.app)||T.highPerfModeEnabled)return;let s=pe(n,r.descriptor.app),i={app:r.descriptor.app,inspectorId:n,nodeId:(s==null?void 0:s.selectedNodeId)||"",state:null},p={currentTab:`custom-inspector:${n}`};i.nodeId&&await new Promise(l=>{t.callHookWith(async m=>{await Promise.all(m.map(_=>_(i,p))),l()},"getInspectorState")}),t.callHookWith(async l=>{await Promise.all(l.map(m=>m({inspectorId:n,nodeId:i.nodeId,state:i.state})))},"sendInspectorStateToClient")},120);return t.hook("sendInspectorState",o),t.hook("customInspectorSelectNode",({inspectorId:n,nodeId:r,plugin:s})=>{let i=pe(n,s.descriptor.app);i&&(i.selectedNodeId=r)}),t.hook("timelineLayerAdded",({options:n,plugin:r})=>{Ro(n,r.descriptor)}),t.hook("timelineEventAdded",({options:n,plugin:r})=>{var i;let s=["performance","component-event","keyboard","mouse"];T.highPerfModeEnabled||!((i=T.timelineLayersState)!=null&&i[r.descriptor.id])&&!s.includes(n.layerId)||t.callHookWith(async p=>{await Promise.all(p.map(c=>c(n)))},"sendTimelineEventToClient")}),t.hook("getComponentInstances",async({app:n})=>{let r=n.__VUE_DEVTOOLS_NEXT_APP_RECORD__;if(!r)return null;let s=r.id.toString();return[...r.instanceMap].filter(([p])=>p.split(":")[0]===s).map(([,p])=>p)}),t.hook("getComponentBounds",async({instance:n})=>G(n)),t.hook("getComponentName",({instance:n})=>N(n)),t.hook("componentHighlight",({uid:n})=>{let r=O.value.instanceMap.get(n);r&&go(r)}),t.hook("componentUnhighlight",()=>{ut()}),t}var Lo,Mo;(Mo=(Lo=u).__VUE_DEVTOOLS_KIT_APP_RECORDS__)!=null||(Lo.__VUE_DEVTOOLS_KIT_APP_RECORDS__=[]);var wo,Ho;(Ho=(wo=u).__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__)!=null||(wo.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__={});var Uo,Ko;(Ko=(Uo=u).__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__)!=null||(Uo.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__="");var Fo,Go;(Go=(Fo=u).__VUE_DEVTOOLS_KIT_CUSTOM_TABS__)!=null||(Fo.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__=[]);var Bo,jo;(jo=(Bo=u).__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__)!=null||(Bo.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__=[]);var b="__VUE_DEVTOOLS_KIT_GLOBAL_STATE__";function ms(){return{connected:!1,clientConnected:!1,vitePluginDetected:!0,appRecords:[],activeAppRecordId:"",tabs:[],commands:[],highPerfModeEnabled:!0,devtoolsClientDetected:{},perfUniqueGroupId:0,timelineLayersState:Co()}}var $o,zo;(zo=($o=u)[b])!=null||($o[b]=ms());var _s=R(t=>{D.hooks.callHook("devtoolsStateUpdated",{state:t})}),Es=R((t,e)=>{D.hooks.callHook("devtoolsConnectedUpdated",{state:t,oldState:e})}),V=new Proxy(u.__VUE_DEVTOOLS_KIT_APP_RECORDS__,{get(t,e,o){return e==="value"?u.__VUE_DEVTOOLS_KIT_APP_RECORDS__:u.__VUE_DEVTOOLS_KIT_APP_RECORDS__[e]}}),Yo=t=>{u.__VUE_DEVTOOLS_KIT_APP_RECORDS__=[...u.__VUE_DEVTOOLS_KIT_APP_RECORDS__,t]},Xo=t=>{u.__VUE_DEVTOOLS_KIT_APP_RECORDS__=V.value.filter(e=>e.app!==t)},O=new Proxy(u.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__,{get(t,e,o){return e==="value"?u.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__:e==="id"?u.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__:u.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__[e]}});function Ot(){_s({...u[b],appRecords:V.value,activeAppRecordId:O.id,tabs:u.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__,commands:u.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__})}function Ce(t){u.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__=t,Ot()}function Me(t){u.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__=t,Ot()}var T=new Proxy(u[b],{get(t,e){return e==="appRecords"?V:e==="activeAppRecordId"?O.id:e==="tabs"?u.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__:e==="commands"?u.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__:u[b][e]},deleteProperty(t,e){return delete t[e],!0},set(t,e,o){let n={...u[b]};return t[e]=o,u[b][e]=o,!0}});function he(t){let e={...u[b],appRecords:V.value,activeAppRecordId:O.id};(e.connected!==t.connected&&t.connected||e.clientConnected!==t.clientConnected&&t.clientConnected)&&Es(u[b],e),Object.assign(u[b],t),Ot()}function Wo(t={}){var i,p,c;let{file:e,host:o,baseUrl:n=window.location.origin,line:r=0,column:s=0}=t;if(e){if(o==="chrome-extension"){let l=e.replace(/\\/g,"\\\\"),m=(p=(i=window.VUE_DEVTOOLS_CONFIG)==null?void 0:i.openInEditorHost)!=null?p:"/";fetch(`${m}__open-in-editor?file=${encodeURI(e)}`).then(_=>{if(!_.ok){let E=`Opening component ${l} failed`;console.log(`%c${E}`,"color:red")}})}else if(T.vitePluginDetected){let l=(c=u.__VUE_DEVTOOLS_OPEN_IN_EDITOR_BASE_URL__)!=null?c:n;u.__VUE_INSPECTOR__.openInEditor(l,e,r,s)}}}a();a();a();a();a();var qo,Zo;(Zo=(qo=u).__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__)!=null||(qo.__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__=[]);var $=new Proxy(u.__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__,{get(t,e,o){return Reflect.get(t,e,o)}});function Jo(t,e){$.push([t,e])}function vt(t){let e={};return Object.keys(t).forEach(o=>{e[o]=t[o].defaultValue}),e}function ht(t){return`__VUE_DEVTOOLS_NEXT_PLUGIN_SETTINGS__${t}__`}function Qo(t){var o,n,r;let e=(n=(o=$.find(s=>{var i;return s[0].id===t&&!!((i=s[0])!=null&&i.settings)}))==null?void 0:o[0])!=null?n:null;return(r=e==null?void 0:e.settings)!=null?r:null}function we(t,e){var n,r,s;let o=ht(t);if(o){let i=localStorage.getItem(o);if(i)return JSON.parse(i)}if(t){let i=(r=(n=$.find(p=>p[0].id===t))==null?void 0:n[0])!=null?r:null;return vt((s=i==null?void 0:i.settings)!=null?s:{})}return vt(e)}function He(t,e){let o=ht(t);localStorage.getItem(o)||localStorage.setItem(o,JSON.stringify(vt(e)))}function en(t,e,o){let n=ht(t),r=localStorage.getItem(n),s=JSON.parse(r||"{}"),i={...s,[e]:o};localStorage.setItem(n,JSON.stringify(i)),D.hooks.callHookWith(p=>{p.forEach(c=>c({pluginId:t,key:e,oldValue:s[e],newValue:o,settings:i}))},"setPluginSettings")}a();a();a();a();a();a();a();a();a();a();a();var tn,on,C=(on=(tn=u).__VUE_DEVTOOLS_HOOK)!=null?on:tn.__VUE_DEVTOOLS_HOOK=Le(),gs={vueAppInit(t){C.hook("app:init",t)},vueAppUnmount(t){C.hook("app:unmount",t)},vueAppConnected(t){C.hook("app:connected",t)},componentAdded(t){return C.hook("component:added",t)},componentEmit(t){return C.hook("component:emit",t)},componentUpdated(t){return C.hook("component:updated",t)},componentRemoved(t){return C.hook("component:removed",t)},setupDevtoolsPlugin(t){C.hook("devtools-plugin:setup",t)},perfStart(t){return C.hook("perf:start",t)},perfEnd(t){return C.hook("perf:end",t)}};function rn(){return{id:"vue-devtools-next",devtoolsVersion:"7.0",enabled:!1,appRecords:[],apps:[],events:new Map,on(t,e){var o;return this.events.has(t)||this.events.set(t,[]),(o=this.events.get(t))==null||o.push(e),()=>this.off(t,e)},once(t,e){let o=(...n)=>{this.off(t,o),e(...n)};return this.on(t,o),[t,o]},off(t,e){if(this.events.has(t)){let o=this.events.get(t),n=o.indexOf(e);n!==-1&&o.splice(n,1)}},emit(t,...e){this.events.has(t)&&this.events.get(t).forEach(o=>o(...e))}}}function sn(t){t.on("app:init",(e,o,n)=>{var r,s,i;(i=(s=(r=e==null?void 0:e._instance)==null?void 0:r.type)==null?void 0:s.devtools)!=null&&i.hide||C.callHook("app:init",e,o,n)}),t.on("app:unmount",e=>{C.callHook("app:unmount",e)}),t.on("component:added",async(e,o,n,r)=>{var s,i,p;(p=(i=(s=e==null?void 0:e._instance)==null?void 0:s.type)==null?void 0:i.devtools)!=null&&p.hide||T.highPerfModeEnabled||!e||typeof o!="number"&&!o||!r||C.callHook("component:added",e,o,n,r)}),t.on("component:updated",(e,o,n,r)=>{!e||typeof o!="number"&&!o||!r||T.highPerfModeEnabled||C.callHook("component:updated",e,o,n,r)}),t.on("component:removed",async(e,o,n,r)=>{!e||typeof o!="number"&&!o||!r||T.highPerfModeEnabled||C.callHook("component:removed",e,o,n,r)}),t.on("component:emit",async(e,o,n,r)=>{!e||!o||T.highPerfModeEnabled||C.callHook("component:emit",e,o,n,r)}),t.on("perf:start",(e,o,n,r,s)=>{!e||T.highPerfModeEnabled||C.callHook("perf:start",e,o,n,r,s)}),t.on("perf:end",(e,o,n,r,s)=>{!e||T.highPerfModeEnabled||C.callHook("perf:end",e,o,n,r,s)}),t.on("devtools-plugin:setup",(e,o,n)=>{(n==null?void 0:n.target)!=="legacy"&&C.callHook("devtools-plugin:setup",e,o)})}var P={on:gs,setupDevToolsPlugin(t,e){return C.callHook("devtools-plugin:setup",t,e)}};var Ue=class{constructor({plugin:e,ctx:o}){this.hooks=o.hooks,this.plugin=e}get on(){return{visitComponentTree:e=>{this.hooks.hook("visitComponentTree",e)},inspectComponent:e=>{this.hooks.hook("inspectComponent",e)},editComponentState:e=>{this.hooks.hook("editComponentState",e)},getInspectorTree:e=>{this.hooks.hook("getInspectorTree",e)},getInspectorState:e=>{this.hooks.hook("getInspectorState",e)},editInspectorState:e=>{this.hooks.hook("editInspectorState",e)},inspectTimelineEvent:e=>{this.hooks.hook("inspectTimelineEvent",e)},timelineCleared:e=>{this.hooks.hook("timelineCleared",e)},setPluginSettings:e=>{this.hooks.hook("setPluginSettings",e)}}}notifyComponentUpdate(e){var n;if(T.highPerfModeEnabled)return;let o=yt().find(r=>r.packageName===this.plugin.descriptor.packageName);if(o!=null&&o.id){if(e){let r=[e.appContext.app,e.uid,(n=e.parent)==null?void 0:n.uid,e];C.callHook("component:updated",...r)}else C.callHook("component:updated");this.hooks.callHook("sendInspectorState",{inspectorId:o.id,plugin:this.plugin})}}addInspector(e){this.hooks.callHook("addInspector",{inspector:e,plugin:this.plugin}),this.plugin.descriptor.settings&&He(e.id,this.plugin.descriptor.settings)}sendInspectorTree(e){T.highPerfModeEnabled||this.hooks.callHook("sendInspectorTree",{inspectorId:e,plugin:this.plugin})}sendInspectorState(e){T.highPerfModeEnabled||this.hooks.callHook("sendInspectorState",{inspectorId:e,plugin:this.plugin})}selectInspectorNode(e,o){this.hooks.callHook("customInspectorSelectNode",{inspectorId:e,nodeId:o,plugin:this.plugin})}visitComponentTree(e){return this.hooks.callHook("visitComponentTree",e)}now(){return T.highPerfModeEnabled?0:Date.now()}addTimelineLayer(e){this.hooks.callHook("timelineLayerAdded",{options:e,plugin:this.plugin})}addTimelineEvent(e){T.highPerfModeEnabled||this.hooks.callHook("timelineEventAdded",{options:e,plugin:this.plugin})}getSettings(e){return we(e!=null?e:this.plugin.descriptor.id,this.plugin.descriptor.settings)}getComponentInstances(e){return this.hooks.callHook("getComponentInstances",{app:e})}getComponentBounds(e){return this.hooks.callHook("getComponentBounds",{instance:e})}getComponentName(e){return this.hooks.callHook("getComponentName",{instance:e})}highlightElement(e){let o=e.__VUE_DEVTOOLS_NEXT_UID__;return this.hooks.callHook("componentHighlight",{uid:o})}unhighlightElement(){return this.hooks.callHook("componentUnhighlight")}};var an=Ue;a();a();a();a();var ln=new Set(["nextTick","defineComponent","defineAsyncComponent","defineCustomElement","ref","computed","reactive","readonly","watchEffect","watchPostEffect","watchSyncEffect","watch","isRef","unref","toRef","toRefs","isProxy","isReactive","isReadonly","shallowRef","triggerRef","customRef","shallowReactive","shallowReadonly","toRaw","markRaw","effectScope","getCurrentScope","onScopeDispose","onMounted","onUpdated","onUnmounted","onBeforeMount","onBeforeUpdate","onBeforeUnmount","onErrorCaptured","onRenderTracked","onRenderTriggered","onActivated","onDeactivated","onServerPrefetch","provide","inject","h","mergeProps","cloneVNode","isVNode","resolveComponent","resolveDirective","withDirectives","withModifiers"]);var cn=/^(?:function|class) (\w+)/;var ue="__vue_devtool_undefined__",fe="__vue_devtool_infinity__",de="__vue_devtool_negative_infinity__",me="__vue_devtool_nan__";a();a();function Ct(t){return!!t.__v_isRef}function pn(t){return Ct(t)&&!!t.effect}function un(t){return!!t.__v_isReactive}function fn(t){return!!t.__v_isReadonly}var ys={[ue]:"undefined",[me]:"NaN",[fe]:"Infinity",[de]:"-Infinity"},ql=Object.entries(ys).reduce((t,[e,o])=>(t[o]=e,t),{});function Nt(t){if(Array.isArray(t))return t.map(o=>Nt(o)).join(" or ");if(t==null)return"null";let e=t.toString().match(cn);return typeof t=="function"&&e&&e[1]||"any"}function Dt(t){try{return{ref:Ct(t),computed:pn(t),reactive:un(t),readonly:fn(t)}}catch(e){return{ref:!1,computed:!1,reactive:!1,readonly:!1}}}function Pt(t){return t!=null&&t.__v_raw?t.__v_raw:t}function Ke(t,e,o){if(typeof e=="function"&&(e=e.options),!e)return t;let{mixins:n,extends:r}=e;r&&Ke(t,r,o),n&&n.forEach(s=>Ke(t,s,o));for(let s of["computed","inject"])Object.prototype.hasOwnProperty.call(e,s)&&(t[s]?Object.assign(t[s],e[s]):t[s]=e[s]);return t}function Is(t){let e=t==null?void 0:t.type;if(!e)return{};let{mixins:o,extends:n}=e,r=t.appContext.mixins;if(!r.length&&!o&&!n)return e;let s={};return r.forEach(i=>Ke(s,i,t)),Ke(s,e,t),s}function Os(t){var n;let e=[],o=(n=t==null?void 0:t.type)==null?void 0:n.props;for(let r in t==null?void 0:t.props){let s=o?o[r]:null,i=ro(r);e.push({type:"props",key:i,value:F(()=>t.props[r]),editable:!0,meta:s?{type:s.type?Nt(s.type):"any",required:!!s.required,...s.default?{default:s.default.toString()}:{}}:{type:"invalid"}})}return e}function vs(t){let e=t.type,o=e==null?void 0:e.props,n=e.vuex&&e.vuex.getters,r=e.computed,s={...t.data,...t.renderContext};return Object.keys(s).filter(i=>!(o&&i in o)&&!(n&&i in n)&&!(r&&i in r)).map(i=>({key:i,type:"data",value:F(()=>s[i]),editable:!0}))}function Ss(t){let e=t.computed?"computed":t.ref?"ref":t.reactive?"reactive":null,o=e?`${e.charAt(0).toUpperCase()}${e.slice(1)}`:null;return{stateType:e,stateTypeName:o}}function hs(t){let e=t.devtoolsRawSetupState||{};return Object.keys(t.setupState).filter(o=>!ln.has(o)&&o.split(/(?=[A-Z])/)[0]!=="use").map(o=>{var l,m,_,E;let n=F(()=>Pt(t.setupState[o])),r=n instanceof Error,s=e[o],i,p=r||typeof n=="function"||Z(n,"render")&&typeof n.render=="function"||Z(n,"__asyncLoader")&&typeof n.__asyncLoader=="function"||typeof n=="object"&&n&&("setup"in n||"props"in n)||/^v[A-Z]/.test(o);if(s&&!r){let y=Dt(s),{stateType:d,stateTypeName:g}=Ss(y),v=y.ref||y.computed||y.reactive,h=Z(s,"effect")?((m=(l=s.effect)==null?void 0:l.raw)==null?void 0:m.toString())||((E=(_=s.effect)==null?void 0:_.fn)==null?void 0:E.toString()):null;d&&(p=!1),i={...d?{stateType:d,stateTypeName:g}:{},...h?{raw:h}:{},editable:v&&!y.readonly}}return{key:o,value:n,type:p?"setup (other)":"setup",...i}})}function Cs(t,e){let o=e,n=[],r=o.computed||{};for(let s in r){let i=r[s],p=typeof i=="function"&&i.vuex?"vuex bindings":"computed";n.push({type:p,key:s,value:F(()=>{var c;return(c=t==null?void 0:t.proxy)==null?void 0:c[s]}),editable:typeof i.set=="function"})}return n}function Ns(t){return Object.keys(t.attrs).map(e=>({type:"attrs",key:e,value:F(()=>t.attrs[e])}))}function Ds(t){return Reflect.ownKeys(t.provides).map(e=>({type:"provided",key:e.toString(),value:F(()=>t.provides[e])}))}function Ps(t,e){if(!(e!=null&&e.inject))return[];let o=[],n;return Array.isArray(e.inject)?o=e.inject.map(r=>({key:r,originalKey:r})):o=Reflect.ownKeys(e.inject).map(r=>{let s=e.inject[r],i;return typeof s=="string"||typeof s=="symbol"?i=s:(i=s.from,n=s.default),{key:r,originalKey:i}}),o.map(({key:r,originalKey:s})=>({type:"injected",key:s&&r!==s?`${s.toString()} \u279E ${r.toString()}`:r.toString(),value:F(()=>t.ctx.hasOwnProperty(r)?t.ctx[r]:t.provides.hasOwnProperty(s)?t.provides[s]:n)}))}function xs(t){return Object.keys(t.refs).map(e=>({type:"template refs",key:e,value:F(()=>t.refs[e])}))}function Rs(t){var s,i;let e=t.type.emits,o=Array.isArray(e)?e:Object.keys(e!=null?e:{}),n=Object.keys((i=(s=t==null?void 0:t.vnode)==null?void 0:s.props)!=null?i:{}),r=[];for(let p of n){let[c,...l]=p.split(/(?=[A-Z])/);if(c==="on"){let m=l.join("-").toLowerCase(),_=o.includes(m);r.push({type:"event listeners",key:m,value:{_custom:{displayText:_?"\u2705 Declared":"\u26A0\uFE0F Not declared",key:_?"\u2705 Declared":"\u26A0\uFE0F Not declared",value:_?"\u2705 Declared":"\u26A0\uFE0F Not declared",tooltipText:_?null:`The event <code>${m}</code> is not declared in the <code>emits</code> option. It will leak into the component's attributes (<code>$attrs</code>).`}}})}}return r}function xt(t){let e=Is(t);return Os(t).concat(vs(t),hs(t),Cs(t,e),Ns(t),Ds(t),Ps(t,e),xs(t),Rs(t))}function mn(t){var i;let e=k(O.value,t.instanceId),o=ae(e),n=N(e),r=(i=e==null?void 0:e.type)==null?void 0:i.__file,s=xt(e);return{id:o,name:n,file:r,state:s,instance:e}}a();a();var Rt=class{constructor(e){this.filter=e||""}isQualified(e){let o=N(e);return xe(o).toLowerCase().includes(this.filter)||so(o).toLowerCase().includes(this.filter)}};function _n(t){return new Rt(t)}var Fe=class{constructor(e){this.captureIds=new Map;let{filterText:o="",maxDepth:n,recursively:r,api:s}=e;this.componentFilter=_n(o),this.maxDepth=n,this.recursively=r,this.api=s}getComponentTree(e){return this.captureIds=new Map,this.findQualifiedChildren(e,0)}getComponentParents(e){this.captureIds=new Map;let o=[];this.captureId(e);let n=e;for(;n=n.parent;)this.captureId(n),o.push(n);return o}captureId(e){if(!e)return null;let o=e.__VUE_DEVTOOLS_NEXT_UID__!=null?e.__VUE_DEVTOOLS_NEXT_UID__:ae(e);return e.__VUE_DEVTOOLS_NEXT_UID__=o,this.captureIds.has(o)?null:(this.captureIds.set(o,void 0),this.mark(e),o)}async capture(e,o){var _;if(!e)return null;let n=this.captureId(e),r=N(e),s=this.getInternalInstanceChildren(e.subTree).filter(E=>!Ae(E)),i=this.getComponentParents(e)||[],p=!!e.isDeactivated||i.some(E=>E.isDeactivated),c={uid:e.uid,id:n,name:r,renderKey:co(e.vnode?e.vnode.key:null),inactive:p,children:[],isFragment:ie(e),tags:typeof e.type!="function"?[]:[{label:"functional",textColor:5592405,backgroundColor:15658734}],autoOpen:this.recursively,file:e.type.__file||""};if((o<this.maxDepth||e.type.__isKeepAlive||i.some(E=>E.type.__isKeepAlive))&&(c.children=await Promise.all(s.map(E=>this.capture(E,o+1)).filter(Boolean))),this.isKeepAlive(e)){let E=this.getKeepAliveCachedInstances(e),y=s.map(d=>d.__VUE_DEVTOOLS_NEXT_UID__);for(let d of E)if(!y.includes(d.__VUE_DEVTOOLS_NEXT_UID__)){let g=await this.capture({...d,isDeactivated:!0},o+1);g&&c.children.push(g)}}let m=j(e)[0];if(m!=null&&m.parentElement){let E=e.parent,y=E?j(E):[],d=m,g=[];do g.push(Array.from(d.parentElement.childNodes).indexOf(d)),d=d.parentElement;while(d.parentElement&&y.length&&!y.includes(d));c.domOrder=g.reverse()}else c.domOrder=[-1];return(_=e.suspense)!=null&&_.suspenseKey&&(c.tags.push({label:e.suspense.suspenseKey,backgroundColor:14979812,textColor:16777215}),this.mark(e,!0)),this.api.visitComponentTree({treeNode:c,componentInstance:e,app:e.appContext.app,filter:this.componentFilter.filter}),c}async findQualifiedChildren(e,o){var n;if(this.componentFilter.isQualified(e)&&!((n=e.type.devtools)!=null&&n.hide))return[await this.capture(e,o)];if(e.subTree){let r=this.isKeepAlive(e)?this.getKeepAliveCachedInstances(e):this.getInternalInstanceChildren(e.subTree);return this.findQualifiedChildrenFromList(r,o)}else return[]}async findQualifiedChildrenFromList(e,o){return e=e.filter(n=>{var r;return!Ae(n)&&!((r=n.type.devtools)!=null&&r.hide)}),this.componentFilter.filter?Array.prototype.concat.apply([],await Promise.all(e.map(n=>this.findQualifiedChildren(n,o)))):Promise.all(e.map(n=>this.capture(n,o)))}getInternalInstanceChildren(e,o=null){let n=[];if(e)if(e.component)o?n.push({...e.component,suspense:o}):n.push(e.component);else if(e.suspense){let r=e.suspense.isInFallback?"suspense fallback":"suspense default";n.push(...this.getInternalInstanceChildren(e.suspense.activeBranch,{...e.suspense,suspenseKey:r}))}else Array.isArray(e.children)&&e.children.forEach(r=>{r.component?o?n.push({...r.component,suspense:o}):n.push(r.component):n.push(...this.getInternalInstanceChildren(r,o))});return n.filter(r=>{var s;return!Ae(r)&&!((s=r.type.devtools)!=null&&s.hide)})}mark(e,o=!1){let n=x(e).instanceMap;(o||!n.has(e.__VUE_DEVTOOLS_NEXT_UID__))&&(n.set(e.__VUE_DEVTOOLS_NEXT_UID__,e),O.value.instanceMap=n)}isKeepAlive(e){return e.type.__isKeepAlive&&e.__v_cache}getKeepAliveCachedInstances(e){return Array.from(e.__v_cache.values()).map(o=>o.component).filter(Boolean)}};a();a();var Ge=new Map,Be="performance";async function En(t,e,o,n,r,s){let i=await x(e);if(!i)return;let p=N(n)||"Unknown Component",c=T.perfUniqueGroupId++,l=`${o}-${r}`;if(i.perfGroupIds.set(l,{groupId:c,time:s}),await t.addTimelineEvent({layerId:Be,event:{time:Date.now(),data:{component:p,type:r,measure:"start"},title:p,subtitle:r,groupId:c}}),Ge.has(l)){let{app:m,uid:_,instance:E,type:y,time:d}=Ge.get(l);Ge.delete(l),await At(t,m,_,E,y,d)}}function At(t,e,o,n,r,s){let i=x(e);if(!i)return;let p=N(n)||"Unknown Component",c=`${o}-${r}`,l=i.perfGroupIds.get(c);if(l){let m=l.groupId,_=l.time,E=s-_;t.addTimelineEvent({layerId:Be,event:{time:Date.now(),data:{component:p,type:r,measure:"end",duration:{_custom:{type:"Duration",value:E,display:`${E} ms`}}},title:p,subtitle:r,groupId:m}})}else Ge.set(c,{app:e,uid:o,instance:n,type:r,time:s})}var gn="component-event";function Tn(t){q&&(t.addTimelineLayer({id:"mouse",label:"Mouse",color:10768815}),["mousedown","mouseup","click","dblclick"].forEach(e=>{!T.timelineLayersState.recordingState||!T.timelineLayersState.mouseEventEnabled||window.addEventListener(e,async o=>{await t.addTimelineEvent({layerId:"mouse",event:{time:Date.now(),data:{type:e,x:o.clientX,y:o.clientY},title:e}})},{capture:!0,passive:!0})}),t.addTimelineLayer({id:"keyboard",label:"Keyboard",color:8475055}),["keyup","keydown","keypress"].forEach(e=>{window.addEventListener(e,async o=>{!T.timelineLayersState.recordingState||!T.timelineLayersState.keyboardEventEnabled||await t.addTimelineEvent({layerId:"keyboard",event:{time:Date.now(),data:{type:e,key:o.key,ctrlKey:o.ctrlKey,shiftKey:o.shiftKey,altKey:o.altKey,metaKey:o.metaKey},title:o.key}})},{capture:!0,passive:!0})}),t.addTimelineLayer({id:gn,label:"Component events",color:5226637}),P.on.componentEmit(async(e,o,n,r)=>{if(!T.timelineLayersState.recordingState||!T.timelineLayersState.componentEventEnabled)return;let s=await x(e);if(!s)return;let i=`${s.id}:${o.uid}`,p=N(o)||"Unknown Component";t.addTimelineEvent({layerId:gn,event:{time:Date.now(),data:{component:{_custom:{type:"component-definition",display:p}},event:n,params:r},title:n,subtitle:`by ${p}`,meta:{componentId:i}}})}),t.addTimelineLayer({id:"performance",label:Be,color:4307050}),P.on.perfStart((e,o,n,r,s)=>{!T.timelineLayersState.recordingState||!T.timelineLayersState.performanceEventEnabled||En(t,e,o,n,r,s)}),P.on.perfEnd((e,o,n,r,s)=>{!T.timelineLayersState.recordingState||!T.timelineLayersState.performanceEventEnabled||At(t,e,o,n,r,s)}))}a();var J=[];function yn(t){if(typeof window=="undefined")return;let e=window;if(t&&(e.$vm=t,J[0]!==t)){J.length>=10&&J.pop();for(let o=J.length;o>0;o--)e[`$vm${o}`]=J[o]=J[o-1];e.$vm0=J[0]=t}}var Q="components";function In(t){return[{id:Q,label:"Components",app:t},n=>{n.addInspector({id:Q,label:"Components",treeFilterPlaceholder:"Search components"}),Tn(n),n.on.getInspectorTree(async l=>{if(l.app===t&&l.inspectorId===Q){let m=k(O.value,l.instanceId);if(m){let _=new Fe({filterText:l.filter,maxDepth:100,recursively:!1,api:n});l.rootNodes=await _.getComponentTree(m)}}}),n.on.getInspectorState(async l=>{var m;if(l.app===t&&l.inspectorId===Q){let _=mn({instanceId:l.nodeId}),E=_.instance,y=(m=_.instance)==null?void 0:m.appContext.app,d={componentInstance:E,app:y,instanceData:_};D.hooks.callHookWith(g=>{g.forEach(v=>v(d))},"inspectComponent"),l.state=_,yn(E)}}),n.on.editInspectorState(async l=>{l.app===t&&l.inspectorId===Q&&(ho(l),await n.sendInspectorState("components"))});let r=R(()=>{n.sendInspectorTree(Q)},120),s=R(()=>{n.sendInspectorState(Q)},120),i=P.on.componentAdded(async(l,m,_,E)=>{var g,v,h;if(T.highPerfModeEnabled||(h=(v=(g=l==null?void 0:l._instance)==null?void 0:g.type)==null?void 0:v.devtools)!=null&&h.hide||!l||typeof m!="number"&&!m||!E)return;let y=await Re({app:l,uid:m,instance:E}),d=await x(l);E&&(E.__VUE_DEVTOOLS_NEXT_UID__==null&&(E.__VUE_DEVTOOLS_NEXT_UID__=y),d!=null&&d.instanceMap.has(y)||(d==null||d.instanceMap.set(y,E),O.value.id===(d==null?void 0:d.id)&&(O.value.instanceMap=d.instanceMap))),d&&r()}),p=P.on.componentUpdated(async(l,m,_,E)=>{var g,v,h;if(T.highPerfModeEnabled||(h=(v=(g=l==null?void 0:l._instance)==null?void 0:g.type)==null?void 0:v.devtools)!=null&&h.hide||!l||typeof m!="number"&&!m||!E)return;let y=await Re({app:l,uid:m,instance:E}),d=await x(l);E&&(E.__VUE_DEVTOOLS_NEXT_UID__==null&&(E.__VUE_DEVTOOLS_NEXT_UID__=y),d!=null&&d.instanceMap.has(y)||(d==null||d.instanceMap.set(y,E),O.value.id===(d==null?void 0:d.id)&&(O.value.instanceMap=d.instanceMap))),d&&(r(),s())}),c=P.on.componentRemoved(async(l,m,_,E)=>{var g,v,h;if(T.highPerfModeEnabled||(h=(v=(g=l==null?void 0:l._instance)==null?void 0:g.type)==null?void 0:v.devtools)!=null&&h.hide||!l||typeof m!="number"&&!m||!E)return;let y=await x(l);if(!y)return;let d=await Re({app:l,uid:m,instance:E});y==null||y.instanceMap.delete(d),O.value.id===(y==null?void 0:y.id)&&(O.value.instanceMap=y.instanceMap),r()})}]}var On,vn;(vn=(On=u).__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__)!=null||(On.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__=new Set);function kt(t,e){return P.setupDevToolsPlugin(t,e)}function bt(t,e){let[o,n]=t;if(o.app!==e)return;let r=new an({plugin:{setupFn:n,descriptor:o},ctx:D});o.packageName==="vuex"&&r.on.editInspectorState(s=>{r.sendInspectorState(s.inspectorId)}),n(r)}function Sn(t){u.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.delete(t)}function _e(t,e){u.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.has(t)||T.highPerfModeEnabled&&!(e!=null&&e.inspectingComponent)||(u.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.add(t),$.forEach(o=>{bt(o,t)}))}a();a();var ee="__VUE_DEVTOOLS_ROUTER__",z="__VUE_DEVTOOLS_ROUTER_INFO__",hn,Cn;(Cn=(hn=u)[z])!=null||(hn[z]={currentRoute:null,routes:[]});var Nn,Dn;(Dn=(Nn=u)[ee])!=null||(Nn[ee]={});var Qc=new Proxy(u[z],{get(t,e){return u[z][e]}}),ep=new Proxy(u[ee],{get(t,e){if(e==="value")return u[ee]}});function As(t){let e=new Map;return((t==null?void 0:t.getRoutes())||[]).filter(o=>!e.has(o.path)&&e.set(o.path,1))}function Vt(t){return t.map(e=>{let{path:o,name:n,children:r,meta:s}=e;return r!=null&&r.length&&(r=Vt(r)),{path:o,name:n,children:r,meta:s}})}function ks(t){if(t){let{fullPath:e,hash:o,href:n,path:r,name:s,matched:i,params:p,query:c}=t;return{fullPath:e,hash:o,href:n,path:r,name:s,params:p,query:c,matched:Vt(i)}}return t}function je(t,e){function o(){var p;let n=(p=t.app)==null?void 0:p.config.globalProperties.$router,r=ks(n==null?void 0:n.currentRoute.value),s=Vt(As(n)),i=console.warn;console.warn=()=>{},u[z]={currentRoute:r?st(r):{},routes:st(s)},u[ee]=n,console.warn=i}o(),P.on.componentUpdated(R(()=>{var n;((n=e.value)==null?void 0:n.app)===t.app&&(o(),!T.highPerfModeEnabled&&D.hooks.callHook("routerInfoUpdated",{state:u[z]}))},200))}function Pn(t){return{async getInspectorTree(e){let o={...e,app:O.value.app,rootNodes:[]};return await new Promise(n=>{t.callHookWith(async r=>{await Promise.all(r.map(s=>s(o))),n()},"getInspectorTree")}),o.rootNodes},async getInspectorState(e){let o={...e,app:O.value.app,state:null},n={currentTab:`custom-inspector:${e.inspectorId}`};return await new Promise(r=>{t.callHookWith(async s=>{await Promise.all(s.map(i=>i(o,n))),r()},"getInspectorState")}),o.state},editInspectorState(e){let o=new Se,n={...e,app:O.value.app,set:(r,s=e.path,i=e.state.value,p)=>{o.set(r,s,i,p||o.createDefaultSetCallback(e.state))}};t.callHookWith(r=>{r.forEach(s=>s(n))},"editInspectorState")},sendInspectorState(e){let o=pe(e);t.callHook("sendInspectorState",{inspectorId:e,plugin:{descriptor:o.descriptor,setupFn:()=>({})}})},inspectComponentInspector(){return yo()},cancelInspectComponentInspector(){return To()},getComponentRenderCode(e){let o=k(O.value,e);if(o)return typeof(o==null?void 0:o.type)!="function"?o.render.toString():o.type.toString()},scrollToComponent(e){return Io({id:e})},openInEditor:Wo,getVueInspector:So,toggleApp(e,o){let n=V.value.find(r=>r.id===e);n&&(Me(e),Ce(n),je(n,O),Tt(),_e(n.app,o))},inspectDOM(e){let o=k(O.value,e);if(o){let[n]=j(o);n&&(u.__VUE_DEVTOOLS_INSPECT_DOM_TARGET__=n)}},updatePluginSettings(e,o,n){en(e,o,n)},getPluginSettings(e){return{options:Qo(e),values:we(e)}}}}a();var xn,Rn;(Rn=(xn=u).__VUE_DEVTOOLS_ENV__)!=null||(xn.__VUE_DEVTOOLS_ENV__={vitePluginDetected:!1});function An(){return u.__VUE_DEVTOOLS_ENV__}var kn=Vo(),bn,Vn;(Vn=(bn=u).__VUE_DEVTOOLS_KIT_CONTEXT__)!=null||(bn.__VUE_DEVTOOLS_KIT_CONTEXT__={hooks:kn,get state(){return{...T,activeAppRecordId:O.id,activeAppRecord:O.value,appRecords:V.value}},api:Pn(kn)});var D=u.__VUE_DEVTOOLS_KIT_CONTEXT__;a();var Fn=Zt(Hn(),1),Un,Kn,Y=(Kn=(Un=u).__VUE_DEVTOOLS_NEXT_APP_RECORD_INFO__)!=null?Kn:Un.__VUE_DEVTOOLS_NEXT_APP_RECORD_INFO__={id:0,appIds:new Set};function bs(t,e){var o;return((o=t==null?void 0:t._component)==null?void 0:o.name)||`App ${e}`}function Vs(t){var e,o,n,r;if(t._instance)return t._instance;if((o=(e=t._container)==null?void 0:e._vnode)!=null&&o.component)return(r=(n=t._container)==null?void 0:n._vnode)==null?void 0:r.component}function Gn(t){let e=t.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__;e!=null&&(Y.appIds.delete(e),Y.id--)}function Ls(t,e){if(t.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__!=null)return t.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__;let o=e!=null?e:(Y.id++).toString();if(e&&Y.appIds.has(o)){let n=1;for(;Y.appIds.has(`${e}_${n}`);)n++;o=`${e}_${n}`}return Y.appIds.add(o),t.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__=o,o}function Bn(t,e){let o=Vs(t);if(o){Y.id++;let n=bs(t,Y.id.toString()),s={id:Ls(t,(0,Fn.default)(n)),name:n,types:e,instanceMap:new Map,perfGroupIds:new Map,rootInstance:o};t.__VUE_DEVTOOLS_NEXT_APP_RECORD__=s;let i=`${s.id}:root`;return s.instanceMap.set(i,s.rootInstance),s.rootInstance.__VUE_DEVTOOLS_NEXT_UID__=i,s}else return{}}function jn(){var o;he({vitePluginDetected:An().vitePluginDetected});let t=((o=u.__VUE_DEVTOOLS_GLOBAL_HOOK__)==null?void 0:o.id)==="vue-devtools-next";if(u.__VUE_DEVTOOLS_GLOBAL_HOOK__&&t)return;let e=rn();if(u.__VUE_DEVTOOLS_HOOK_REPLAY__)try{u.__VUE_DEVTOOLS_HOOK_REPLAY__.forEach(n=>n(e)),u.__VUE_DEVTOOLS_HOOK_REPLAY__=[]}catch(n){console.error("[vue-devtools] Error during hook replay",n)}e.once("init",n=>{u.__VUE_DEVTOOLS_VUE2_APP_DETECTED__=!0,console.log("%c[_____Vue DevTools v7 log_____]","color: red; font-bold: 600; font-size: 16px;"),console.log("%cVue DevTools v7 detected in your Vue2 project. v7 only supports Vue3 and will not work.","font-bold: 500; font-size: 14px;"),console.log("%cThe legacy version that supports both Vue 2 and Vue 3 has been moved to %c https://chromewebstore.google.com/detail/vuejs-devtools/iaajmlceplecbljialhhkmedjlpdblhp","font-size: 14px;","text-decoration: underline; cursor: pointer;font-size: 14px;"),console.log("%cPlease install and enable only the legacy version for your Vue2 app.","font-bold: 500; font-size: 14px;"),console.log("%c[_____Vue DevTools v7 log_____]","color: red; font-bold: 600; font-size: 16px;")}),P.on.setupDevtoolsPlugin((n,r)=>{var i;Jo(n,r);let{app:s}=(i=O)!=null?i:{};n.settings&&He(n.id,n.settings),s&&bt([n,r],s)}),ao(()=>{$.filter(([r])=>r.id!=="components").forEach(([r,s])=>{e.emit("devtools-plugin:setup",r,s,{target:"legacy"})})}),P.on.vueAppInit(async(n,r,s)=>{let p={...Bn(n,s),app:n,version:r};Yo(p),V.value.length===1&&(Ce(p),Me(p.id),je(p,O),_e(p.app)),kt(...In(p.app)),he({connected:!0}),e.apps.push(n)}),P.on.vueAppUnmount(async n=>{let r=V.value.filter(s=>s.app!==n);r.length===0&&he({connected:!1}),Xo(n),Gn(n),O.value.app===n&&(Ce(r[0]),D.hooks.callHook("sendActiveAppUpdatedToClient")),u.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps.splice(u.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps.indexOf(n),1),Sn(n)}),sn(e),u.__VUE_DEVTOOLS_GLOBAL_HOOK__?Qt||Object.assign(__VUE_DEVTOOLS_GLOBAL_HOOK__,e):Object.defineProperty(u,"__VUE_DEVTOOLS_GLOBAL_HOOK__",{get(){return e}})}a();function Lt(t){T.highPerfModeEnabled=t!=null?t:!T.highPerfModeEnabled,!t&&O.value&&_e(O.value.app)}a();a();a();function Xn(t){T.devtoolsClientDetected={...T.devtoolsClientDetected,...t};let e=Object.values(T.devtoolsClientDetected).some(Boolean);Lt(!e)}var zn,Yn;(Yn=(zn=u).__VUE_DEVTOOLS_UPDATE_CLIENT_DETECTED__)!=null||(zn.__VUE_DEVTOOLS_UPDATE_CLIENT_DETECTED__=Xn);a();a();a();a();a();a();a();var $e=class{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(e,o){this.keyToValue.set(e,o),this.valueToKey.set(o,e)}getByKey(e){return this.keyToValue.get(e)}getByValue(e){return this.valueToKey.get(e)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}};var Ee=class{constructor(e){this.generateIdentifier=e,this.kv=new $e}register(e,o){this.kv.getByValue(e)||(o||(o=this.generateIdentifier(e)),this.kv.set(o,e))}clear(){this.kv.clear()}getIdentifier(e){return this.kv.getByValue(e)}getValue(e){return this.kv.getByKey(e)}};var ze=class extends Ee{constructor(){super(e=>e.name),this.classToAllowedProps=new Map}register(e,o){typeof o=="object"?(o.allowProps&&this.classToAllowedProps.set(e,o.allowProps),super.register(e,o.identifier)):super.register(e,o)}getAllowedProps(e){return this.classToAllowedProps.get(e)}};a();a();function Hs(t){if("values"in Object)return Object.values(t);let e=[];for(let o in t)t.hasOwnProperty(o)&&e.push(t[o]);return e}function Wn(t,e){let o=Hs(t);if("find"in o)return o.find(e);let n=o;for(let r=0;r<n.length;r++){let s=n[r];if(e(s))return s}}function te(t,e){Object.entries(t).forEach(([o,n])=>e(n,o))}function ge(t,e){return t.indexOf(e)!==-1}function Mt(t,e){for(let o=0;o<t.length;o++){let n=t[o];if(e(n))return n}}var Ye=class{constructor(){this.transfomers={}}register(e){this.transfomers[e.name]=e}findApplicable(e){return Wn(this.transfomers,o=>o.isApplicable(e))}findByName(e){return this.transfomers[e]}};a();a();var Us=t=>Object.prototype.toString.call(t).slice(8,-1),wt=t=>typeof t=="undefined",Ks=t=>t===null,oe=t=>typeof t!="object"||t===null||t===Object.prototype?!1:Object.getPrototypeOf(t)===null?!0:Object.getPrototypeOf(t)===Object.prototype,Xe=t=>oe(t)&&Object.keys(t).length===0,L=t=>Array.isArray(t),Fs=t=>typeof t=="string",Gs=t=>typeof t=="number"&&!isNaN(t),Bs=t=>typeof t=="boolean",qn=t=>t instanceof RegExp,X=t=>t instanceof Map,W=t=>t instanceof Set,Ht=t=>Us(t)==="Symbol",Zn=t=>t instanceof Date&&!isNaN(t.valueOf()),Jn=t=>t instanceof Error,Ut=t=>typeof t=="number"&&isNaN(t),Qn=t=>Bs(t)||Ks(t)||wt(t)||Gs(t)||Fs(t)||Ht(t),er=t=>typeof t=="bigint",tr=t=>t===1/0||t===-1/0,or=t=>ArrayBuffer.isView(t)&&!(t instanceof DataView),nr=t=>t instanceof URL;a();var Kt=t=>t.replace(/\./g,"\\."),We=t=>t.map(String).map(Kt).join("."),Te=t=>{let e=[],o="";for(let r=0;r<t.length;r++){let s=t.charAt(r);if(s==="\\"&&t.charAt(r+1)==="."){o+=".",r++;continue}if(s==="."){e.push(o),o="";continue}o+=s}let n=o;return e.push(n),e};a();function w(t,e,o,n){return{isApplicable:t,annotation:e,transform:o,untransform:n}}var rr=[w(wt,"undefined",()=>null,()=>{}),w(er,"bigint",t=>t.toString(),t=>typeof BigInt!="undefined"?BigInt(t):(console.error("Please add a BigInt polyfill."),t)),w(Zn,"Date",t=>t.toISOString(),t=>new Date(t)),w(Jn,"Error",(t,e)=>{let o={name:t.name,message:t.message};return e.allowedErrorProps.forEach(n=>{o[n]=t[n]}),o},(t,e)=>{let o=new Error(t.message);return o.name=t.name,o.stack=t.stack,e.allowedErrorProps.forEach(n=>{o[n]=t[n]}),o}),w(qn,"regexp",t=>""+t,t=>{let e=t.slice(1,t.lastIndexOf("/")),o=t.slice(t.lastIndexOf("/")+1);return new RegExp(e,o)}),w(W,"set",t=>[...t.values()],t=>new Set(t)),w(X,"map",t=>[...t.entries()],t=>new Map(t)),w(t=>Ut(t)||tr(t),"number",t=>Ut(t)?"NaN":t>0?"Infinity":"-Infinity",Number),w(t=>t===0&&1/t===-1/0,"number",()=>"-0",Number),w(nr,"URL",t=>t.toString(),t=>new URL(t))];function qe(t,e,o,n){return{isApplicable:t,annotation:e,transform:o,untransform:n}}var sr=qe((t,e)=>Ht(t)?!!e.symbolRegistry.getIdentifier(t):!1,(t,e)=>["symbol",e.symbolRegistry.getIdentifier(t)],t=>t.description,(t,e,o)=>{let n=o.symbolRegistry.getValue(e[1]);if(!n)throw new Error("Trying to deserialize unknown symbol");return n}),js=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce((t,e)=>(t[e.name]=e,t),{}),ir=qe(or,t=>["typed-array",t.constructor.name],t=>[...t],(t,e)=>{let o=js[e[1]];if(!o)throw new Error("Trying to deserialize unknown typed array");return new o(t)});function Ft(t,e){return t!=null&&t.constructor?!!e.classRegistry.getIdentifier(t.constructor):!1}var ar=qe(Ft,(t,e)=>["class",e.classRegistry.getIdentifier(t.constructor)],(t,e)=>{let o=e.classRegistry.getAllowedProps(t.constructor);if(!o)return{...t};let n={};return o.forEach(r=>{n[r]=t[r]}),n},(t,e,o)=>{let n=o.classRegistry.getValue(e[1]);if(!n)throw new Error(`Trying to deserialize unknown class '${e[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);return Object.assign(Object.create(n.prototype),t)}),lr=qe((t,e)=>!!e.customTransformerRegistry.findApplicable(t),(t,e)=>["custom",e.customTransformerRegistry.findApplicable(t).name],(t,e)=>e.customTransformerRegistry.findApplicable(t).serialize(t),(t,e,o)=>{let n=o.customTransformerRegistry.findByName(e[1]);if(!n)throw new Error("Trying to deserialize unknown custom value");return n.deserialize(t)}),$s=[ar,sr,lr,ir],Gt=(t,e)=>{let o=Mt($s,r=>r.isApplicable(t,e));if(o)return{value:o.transform(t,e),type:o.annotation(t,e)};let n=Mt(rr,r=>r.isApplicable(t,e));if(n)return{value:n.transform(t,e),type:n.annotation}},cr={};rr.forEach(t=>{cr[t.annotation]=t});var pr=(t,e,o)=>{if(L(e))switch(e[0]){case"symbol":return sr.untransform(t,e,o);case"class":return ar.untransform(t,e,o);case"custom":return lr.untransform(t,e,o);case"typed-array":return ir.untransform(t,e,o);default:throw new Error("Unknown transformation: "+e)}else{let n=cr[e];if(!n)throw new Error("Unknown transformation: "+e);return n.untransform(t,o)}};a();var ye=(t,e)=>{if(e>t.size)throw new Error("index out of bounds");let o=t.keys();for(;e>0;)o.next(),e--;return o.next().value};function ur(t){if(ge(t,"__proto__"))throw new Error("__proto__ is not allowed as a property");if(ge(t,"prototype"))throw new Error("prototype is not allowed as a property");if(ge(t,"constructor"))throw new Error("constructor is not allowed as a property")}var fr=(t,e)=>{ur(e);for(let o=0;o<e.length;o++){let n=e[o];if(W(t))t=ye(t,+n);else if(X(t)){let r=+n,s=+e[++o]==0?"key":"value",i=ye(t,r);switch(s){case"key":t=i;break;case"value":t=t.get(i);break}}else t=t[n]}return t},Ze=(t,e,o)=>{if(ur(e),e.length===0)return o(t);let n=t;for(let s=0;s<e.length-1;s++){let i=e[s];if(L(n)){let p=+i;n=n[p]}else if(oe(n))n=n[i];else if(W(n)){let p=+i;n=ye(n,p)}else if(X(n)){if(s===e.length-2)break;let c=+i,l=+e[++s]==0?"key":"value",m=ye(n,c);switch(l){case"key":n=m;break;case"value":n=n.get(m);break}}}let r=e[e.length-1];if(L(n)?n[+r]=o(n[+r]):oe(n)&&(n[r]=o(n[r])),W(n)){let s=ye(n,+r),i=o(s);s!==i&&(n.delete(s),n.add(i))}if(X(n)){let s=+e[e.length-2],i=ye(n,s);switch(+r==0?"key":"value"){case"key":{let c=o(i);n.set(c,n.get(i)),c!==i&&n.delete(i);break}case"value":{n.set(i,o(n.get(i)));break}}}return t};function Bt(t,e,o=[]){if(!t)return;if(!L(t)){te(t,(s,i)=>Bt(s,e,[...o,...Te(i)]));return}let[n,r]=t;r&&te(r,(s,i)=>{Bt(s,e,[...o,...Te(i)])}),e(n,o)}function dr(t,e,o){return Bt(e,(n,r)=>{t=Ze(t,r,s=>pr(s,n,o))}),t}function mr(t,e){function o(n,r){let s=fr(t,Te(r));n.map(Te).forEach(i=>{t=Ze(t,i,()=>s)})}if(L(e)){let[n,r]=e;n.forEach(s=>{t=Ze(t,Te(s),()=>t)}),r&&te(r,o)}else te(e,o);return t}var zs=(t,e)=>oe(t)||L(t)||X(t)||W(t)||Ft(t,e);function Ys(t,e,o){let n=o.get(t);n?n.push(e):o.set(t,[e])}function _r(t,e){let o={},n;return t.forEach(r=>{if(r.length<=1)return;e||(r=r.map(p=>p.map(String)).sort((p,c)=>p.length-c.length));let[s,...i]=r;s.length===0?n=i.map(We):o[We(s)]=i.map(We)}),n?Xe(o)?[n]:[n,o]:Xe(o)?void 0:o}var jt=(t,e,o,n,r=[],s=[],i=new Map)=>{var y;let p=Qn(t);if(!p){Ys(t,r,e);let d=i.get(t);if(d)return n?{transformedValue:null}:d}if(!zs(t,o)){let d=Gt(t,o),g=d?{transformedValue:d.value,annotations:[d.type]}:{transformedValue:t};return p||i.set(t,g),g}if(ge(s,t))return{transformedValue:null};let c=Gt(t,o),l=(y=c==null?void 0:c.value)!=null?y:t,m=L(l)?[]:{},_={};te(l,(d,g)=>{if(g==="__proto__"||g==="constructor"||g==="prototype")throw new Error(`Detected property ${g}. This is a prototype pollution risk, please remove it from your object.`);let v=jt(d,e,o,n,[...r,g],[...s,t],i);m[g]=v.transformedValue,L(v.annotations)?_[g]=v.annotations:oe(v.annotations)&&te(v.annotations,(h,H)=>{_[Kt(g)+"."+H]=h})});let E=Xe(_)?{transformedValue:m,annotations:c?[c.type]:void 0}:{transformedValue:m,annotations:c?[c.type,_]:_};return p||i.set(t,E),E};a();a();function Je(t){return Object.prototype.toString.call(t).slice(8,-1)}function $t(t){return Je(t)==="Array"}function Er(t){if(Je(t)!=="Object")return!1;let e=Object.getPrototypeOf(t);return!!e&&e.constructor===Object&&e===Object.prototype}function Xs(t){return Je(t)==="Null"}function Ws(t,e,o,n,r){return s=>t(s)||e(s)||!!o&&o(s)||!!n&&n(s)||!!r&&r(s)}function qs(t){return Je(t)==="Undefined"}var uf=Ws(Xs,qs);function Zs(t,e,o,n,r){let s={}.propertyIsEnumerable.call(n,e)?"enumerable":"nonenumerable";s==="enumerable"&&(t[e]=o),r&&s==="nonenumerable"&&Object.defineProperty(t,e,{value:o,enumerable:!1,writable:!0,configurable:!0})}function Qe(t,e={}){if($t(t))return t.map(r=>Qe(r,e));if(!Er(t))return t;let o=Object.getOwnPropertyNames(t),n=Object.getOwnPropertySymbols(t);return[...o,...n].reduce((r,s)=>{if($t(e.props)&&!e.props.includes(s))return r;let i=t[s],p=Qe(i,e);return Zs(r,s,p,t,e.nonenumerable),r},{})}var I=class{constructor({dedupe:e=!1}={}){this.classRegistry=new ze,this.symbolRegistry=new Ee(o=>{var n;return(n=o.description)!=null?n:""}),this.customTransformerRegistry=new Ye,this.allowedErrorProps=[],this.dedupe=e}serialize(e){let o=new Map,n=jt(e,o,this,this.dedupe),r={json:n.transformedValue};n.annotations&&(r.meta={...r.meta,values:n.annotations});let s=_r(o,this.dedupe);return s&&(r.meta={...r.meta,referentialEqualities:s}),r}deserialize(e){let{json:o,meta:n}=e,r=Qe(o);return n!=null&&n.values&&(r=dr(r,n.values,this)),n!=null&&n.referentialEqualities&&(r=mr(r,n.referentialEqualities)),r}stringify(e){return JSON.stringify(this.serialize(e))}parse(e){return this.deserialize(JSON.parse(e))}registerClass(e,o){this.classRegistry.register(e,o)}registerSymbol(e,o){this.symbolRegistry.register(e,o)}registerCustom(e,o){this.customTransformerRegistry.register({name:o,...e})}allowErrorProps(...e){this.allowedErrorProps.push(...e)}};I.defaultInstance=new I;I.serialize=I.defaultInstance.serialize.bind(I.defaultInstance);I.deserialize=I.defaultInstance.deserialize.bind(I.defaultInstance);I.stringify=I.defaultInstance.stringify.bind(I.defaultInstance);I.parse=I.defaultInstance.parse.bind(I.defaultInstance);I.registerClass=I.defaultInstance.registerClass.bind(I.defaultInstance);I.registerSymbol=I.defaultInstance.registerSymbol.bind(I.defaultInstance);I.registerCustom=I.defaultInstance.registerCustom.bind(I.defaultInstance);I.allowErrorProps=I.defaultInstance.allowErrorProps.bind(I.defaultInstance);var vf=I.serialize,Sf=I.deserialize,hf=I.stringify,Cf=I.parse,Nf=I.registerClass,Df=I.registerCustom,Pf=I.registerSymbol,xf=I.allowErrorProps;a();a();a();a();a();a();a();a();a();a();a();a();a();a();a();a();a();a();a();a();a();a();a();var Tr,yr;(yr=(Tr=u).__VUE_DEVTOOLS_KIT_MESSAGE_CHANNELS__)!=null||(Tr.__VUE_DEVTOOLS_KIT_MESSAGE_CHANNELS__=[]);var Ir,Or;(Or=(Ir=u).__VUE_DEVTOOLS_KIT_RPC_CLIENT__)!=null||(Ir.__VUE_DEVTOOLS_KIT_RPC_CLIENT__=null);var vr,Sr;(Sr=(vr=u).__VUE_DEVTOOLS_KIT_RPC_SERVER__)!=null||(vr.__VUE_DEVTOOLS_KIT_RPC_SERVER__=null);var hr,Cr;(Cr=(hr=u).__VUE_DEVTOOLS_KIT_VITE_RPC_CLIENT__)!=null||(hr.__VUE_DEVTOOLS_KIT_VITE_RPC_CLIENT__=null);var Nr,Dr;(Dr=(Nr=u).__VUE_DEVTOOLS_KIT_VITE_RPC_SERVER__)!=null||(Nr.__VUE_DEVTOOLS_KIT_VITE_RPC_SERVER__=null);var Pr,xr;(xr=(Pr=u).__VUE_DEVTOOLS_KIT_BROADCAST_RPC_SERVER__)!=null||(Pr.__VUE_DEVTOOLS_KIT_BROADCAST_RPC_SERVER__=null);a();a();a();a();a();a();a();var K_=2*1024*1024;var Rr={hook:P,init:()=>{jn()},get ctx(){return D},get api(){return D.api}};Rr.init();window.__VUE_DEVTOOLS_BROWSER_EXTENSION_DETECTED__=!0;})();
