import moment from 'moment'
import fs from 'fs'
import { dirname, resolve } from 'path'
import https from 'https'
import { join, basename } from 'path'
import { Client } from '@gradio/client'
import { spawn } from 'child_process'
import JsonDB from './JsonDB'

export function getBaseCachePath(create_dir = true) {
  const dir = getAPPBasePath() + 'cache/'
  if (create_dir) {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }
  }
  return dir
}

export function getRoomBaseCachePath(roomid, prefix = 'room_', create_dir = true) {
  let dir
  switch (prefix) {
    case 'room_':
      dir = getBaseCachePath() + prefix + roomid
      break
    default:
      dir = getBaseCachePath() + prefix + roomid + '/'
      if (create_dir) {
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true })
        }
      }
  }

  return dir
}

export function getNextKey(currentKey: string, roomList: any): string {
  const keys = roomList.map((item) => item.live_id)
  const currentIndex = keys.findIndex((item) => item === currentKey)

  if (currentIndex === -1) {
    throw new Error(`Key ${currentKey} not found in the roomList`)
  }

  const nextIndex = (currentIndex + 1) % keys.length
  return keys[nextIndex]
}
export function getAPPBasePath() {
  const currentDirectory = dirname(resolve(process.execPath))

  //如果 currentDirectory 包含node_modules，则说明是开发环境，否则是生产环境
  if (currentDirectory.includes('node_modules')) {
    //返回node_modules 之前的部分

    const nodeModulesIndex = currentDirectory.indexOf('node_modules')
    return currentDirectory.substring(0, nodeModulesIndex)
  } else {
    return currentDirectory + '\\'
  }
}

const downloadFile = async (tsFile: string, targetDirectory: string) => {
  const tsPath = join(targetDirectory, basename(tsFile))
  console.log('下载ts文件-->' + tsFile)

  const file = fs.createWriteStream(tsPath)

  return new Promise((resolve, reject) => {
    https
      .request(tsFile, (res) => {
        // let totalBytes = parseInt(res.headers['content-length'], 10);
        // let downloadedBytes = 0;

        res.pipe(file)

        res.on('data', () => {
          //  downloadedBytes += chunk.length;
          // const progress = (downloadedBytes / totalBytes) * 100;
        })

        file.on('finish', () => {
          file.close()
          console.log(`\nTS file saved to ${tsPath}`)
          resolve(undefined)
        })
      })
      .on('error', (err) => {
        fs.unlink(tsPath, () => {
          console.error(`Error: ${err.message}`)
          reject(err)
        })
      })
      .end()
  })
}

class API {
  public static async audio2seg(path) {
    try {
      // 从配置获取音频服务地址
      const speechServiceUrl = JsonDB.getItem(
        'cache/config.json',
        'audio_src_serve_ip',
        'http://127.0.0.1:5000'
      )
      console.log('使用音频转文字服务地址:', speechServiceUrl)

      // 读取文件内容并转换为 Blob
      const fileContent = fs.readFileSync(path)
      // 在 Node.js 环境中创建 Blob
      const audioBlob = new Blob([fileContent], { type: 'audio/m4a' })

      // 连接到 Gradio 服务
      const client = await Client.connect(speechServiceUrl)

      // 调用音频转文字 API，使用正确的参数名
      const result = await client.predict('/process', {
        audio_file: audioBlob
      })
      // 根据文档，返回 result.data
      return JSON.parse(result.data)?.seg || []
    } catch (error: any) {
      console.error('Error:', error)
      throw new Error(error)
    }
  }

  public static calculateMetrics(data) {
    let totalWatchUcnt = 0
    let totalAvgWatchDuration = 0
    let totalPayOrderCnt = 0
    let totalPayGmv = 0

    for (const key in data) {
      const liveData = data[key]
      totalWatchUcnt += parseInt(liveData.watch_ucnt?.toString()?.replace('万', '0000'))
      totalAvgWatchDuration += parseInt(liveData.avg_watch_duration?.toString()?.split('分')[0])
      totalPayOrderCnt += parseInt(liveData.pay_order_cnt)
      totalPayGmv += parseFloat(liveData.pay_gmv?.replace('¥', '').replace?.(',', ''))
    }

    return {
      totalWatchUcnt,
      totalAvgWatchDuration: `${Math.floor(totalAvgWatchDuration / Object.keys(data).length)}分`,
      totalPayOrderCnt,
      totalPayGmv: `¥${totalPayGmv.toFixed(2)}`,
      gpm: `¥${(totalPayGmv / totalAvgWatchDuration).toFixed(2)}`
    }
  }

  public static filterRecentData(data, end = 0, start = 30) {
    // 获取当前日期

    // 计算30天前的日期

    const startday = moment().subtract(start, 'days')
    const endday = moment().subtract(end, 'days')

    // 筛选30天内的数据
    const recentData = {}
    for (const roomId in data) {
      const room = data[roomId]

      // 将开始时间转换为moment对象
      const startTime = moment(room.start_time, 'YYYY/MM/DD HH:mm')

      // 检查开始日期是否在30天之内

      if (startTime.isSameOrAfter(startday) && startTime.isSameOrBefore(endday)) {
        recentData[roomId] = room
      }
    }

    return recentData
  }

  //
  public static video2m4a(hls: string, m4a_path: string): Promise<string> {
    const ffmpeg_base_path = getAPPBasePath() + 'bin\\'
    // const hls_path=  getAPPBasePath() + `cache/video_${roomid}/video.m3u8`
    //  const m4a_path = getAPPBasePath()+`cache/video_${roomid}/voice.m4a`
    const path = hls //debug path
    console.log('this debug_path need remove in product', path)
    //let path=hls
    // let output_path = output
    return new Promise((resolve, reject) => {
      const command = `ffmpeg -i ${path} -vn -acodec copy ${m4a_path} -y`
      const process = spawn(command, {
        shell: true,
        cwd: ffmpeg_base_path
      })

      let result = ''

      process.stdout.on('data', (data) => {
        console.log(`stdout: ${data}`)
        result += data
      })

      process.stderr.on('data', (data) => {
        console.error(`stderr: ${data}`)
      })

      process.on('close', (code) => {
        if (code === 0) {
          resolve(result)
        } else {
          reject(new Error(`FFmpeg process exited with code ${code}`))
        }
      })
    })
  }

  public static downloadM3U8AndTSFilesAsync(m3u8Url, targetDirectory) {
    // 返回一个 Promise 使函数可以被 await
    return new Promise((resolve, reject) => {
      console.log('下载 m3u8 文件到目录:', targetDirectory)

      // 如果 targetDirectory 不存在，则创建它
      if (!fs.existsSync(targetDirectory)) {
        fs.mkdirSync(targetDirectory)
      }

      https
        .get(m3u8Url, (res) => {
          let data = ''

          res.on('data', (chunk) => {
            data += chunk
          })

          res.on('end', async () => {
            try {
              const lines = data.split('\n')
              let tsFiles = lines.filter((line) => line.endsWith('.ts'))

              // 处理每一行，只保留文件名部分
              for (const index in lines) {
                lines[index] = basename(lines[index])
              }

              // 写入处理后的 m3u8 文件

              // 并行任务队列函数
              const queue = async (
                tasks: (() => Promise<void>)[],
                concurrency: number
              ): Promise<void[]> => {
                const executing: Promise<void>[] = []
                for (const task of tasks) {
                  const p = task().then(() => executing.splice(executing.indexOf(p), 1))
                  executing.push(p)
                  if (executing.length >= concurrency) {
                    await Promise.race(executing)
                  }
                }
                return Promise.all(executing)
              }

              // 过滤掉已经存在的 ts 文件
              tsFiles = tsFiles.filter(
                (tsFile) => !fs.existsSync(join(targetDirectory, basename(tsFile)))
              )
              console.log('准备下载的 ts 文件:', tsFiles.length)

              // 创建下载任务
              const tasks = tsFiles.map((tsFile) => () => downloadFile(tsFile, targetDirectory))

              // 执行并行下载并等待所有任务完成
              await queue(tasks as (() => Promise<void>)[], 3)
              fs.writeFileSync(join(targetDirectory, 'video.m3u8'), lines.join('\n'))

              // 所有任务成功完成后 resolve
              resolve(undefined)
            } catch (err) {
              // 捕获并 reject 任何异步错误
              reject(err)
            }
          })
        })
        .on('error', (err) => {
          // 处理 http 请求错误
          console.error('HTTP 请求错误:', err.message)
          reject(err)
        })
    })
  }
  public static downloadM3U8AndTSFiles(m3u8Url, targetDirectory) {
    console.log('下载m3u8文件' + targetDirectory)
    //如果targetDirectory不存在，则创建它
    if (!fs.existsSync(targetDirectory)) {
      fs.mkdirSync(targetDirectory)
    }
    https
      .get(m3u8Url, (res) => {
        let data = ''

        res.on('data', (chunk) => {
          data += chunk
        })

        res.on('end', () => {
          const lines = data.split('\n')
          let tsFiles = lines.filter((line) => line.endsWith('.ts'))

          for (const index in lines) {
            lines[index] = basename(lines[index])
          }
          //写入文件
          fs.writeFileSync(join(targetDirectory, 'video.m3u8'), lines.join('\n'))

          const queue = async (tasks: (() => Promise<void>)[], concurrency: number) => {
            const executing: Promise<void>[] = []
            for (const task of tasks) {
              const p = task().then(() => executing.splice(executing.indexOf(p), 1))
              executing.push(p)
              if (executing.length >= concurrency) {
                await Promise.race(executing)
              }
            }
            return Promise.all(executing)
          }

          //tsFiles 需要先过滤掉已经存在的文件
          tsFiles = tsFiles.filter(
            (tsFile) => !fs.existsSync(join(targetDirectory, basename(tsFile)))
          )
          console.log('download', tsFiles)
          const tasks = tsFiles.map((tsFile) => () => downloadFile(tsFile, targetDirectory))
          queue(tasks as (() => Promise<void>)[], 3).catch((err) => console.error(err))
        })
      })
      .on('error', (err) => {
        console.error('Error: ' + err.message)
      })
  }
}

export default API
