/**
 * 刘周宇
 */
import { IpcMain, BrowserWindow, shell } from 'electron'
import {
  insertRoomDataById,
  batchInsertRoomDataByIds,
  getAccountMetrics,
  getRoomListData,
  getRoomList,
  getOrderList,
  getOrderMetrics,
  getProducts,
  getProductMetrics,
  extractProductsFromOrdersToDatabase,
  saveSubtitleEdits,
  loadSubtitleEdits,
  createOrder,
  createRoomDataByUrl
} from './business'

import JsonDB from '../module/JsonDB'
import { getRoomCoreDataByIdFromFile } from './utils'
import { getAPPBasePath } from '../module/API'
import path from 'path'
import * as fs from 'fs'

//创建类
export default class LZYService {
  public static hook(ipcMain: IpcMain): void {
    // 注册获取房间列表处理器
    ipcMain.handle('lzy:getRoomList', async (_, args) => {
      return await getRoomList(args)
    })

    // 注册批量插入房间数据处理器
    ipcMain.handle('lzy:batchInsertRoomData', async (_, ids: string[]) => {
      return await batchInsertRoomDataByIds(ids)
    })

    // 注册单个插入房间数据处理器
    ipcMain.handle('lzy:insertRoomData', async (_, id: string) => {
      return await insertRoomDataById(id)
    })

    // 注册通过ID获取转换后的房间数据处理器
    ipcMain.handle('lzy:getRoomCoreDataById', async (_, id: string) => {
      return await getRoomCoreDataByIdFromFile(id)
    })

    // 监听获取账户指标事件
    ipcMain.handle('getAccountMetrics', async (_, args: any) => {
      // 获取请求参数中的天数，默认为30天
      const days = args && args.days ? args.days : 30

      try {
        // 使用账户指标服务获取数据
        const metricsData = await getAccountMetrics(days)
        // 发送数据到前端
        return metricsData
      } catch (error) {
        console.error('获取账户指标失败:', error)
        return {
          error: (error as Error).message || '获取账户指标失败',
          currentMonth: null,
          previousMonth: null
        }
      }
    })

    // 注册获取订单列表处理器
    ipcMain.handle('lzy:getOrderList', async (_, args) => {
      return await getOrderList(args)
    })

    // 注册获取订单统计指标处理器
    ipcMain.handle('lzy:getOrderMetrics', async (_, filters) => {
      return await getOrderMetrics(filters)
    })

    // 注册批量创建订单处理器
    ipcMain.handle('lzy:createOrders', async (_, orders: any[]) => {
      try {
        console.log(`收到批量创建订单请求，共 ${orders.length} 条订单`)
        const result = await createOrder(orders)
        console.log(
          `批量创建订单完成：成功 ${result.successCount}/${result.totalOrders}，失败 ${result.failedOrders.length} 条`
        )
        return result
      } catch (error) {
        console.error('批量创建订单失败:', error)
        return {
          error: (error as Error).message || '批量创建订单失败',
          totalOrders: orders.length,
          successCount: 0,
          failedOrders: []
        }
      }
    })

    // 监听获取房间列表事件
    ipcMain.on('/room/list', async (event: any, args: any) => {
      // 使用房间列表服务获取数据
      const result = await getRoomListData(args)

      // 发送数据到前端
      event.reply('/room/list/reply', result)
    })

    // 注册获取商品列表处理器（支持分页）
    ipcMain.handle('getProducts', async (_, options) => {
      return await getProducts(options)
    })

    // 注册获取商品统计指标处理器
    ipcMain.handle('lzy:getProductMetrics', async (_, filters) => {
      return await getProductMetrics(filters)
    })

    // 注册商品数据迁移处理器
    ipcMain.handle('extractProductsFromOrders', async () => {
      try {
        return await extractProductsFromOrdersToDatabase()
      } catch (error) {
        console.error('商品数据迁移失败:', error)
        return {
          error: (error as Error).message || '商品数据迁移失败',
          totalOrdersScanned: 0,
          totalProductsExtracted: 0,
          productResult: {
            totalProducts: 0,
            successCount: 0,
            failedProducts: []
          }
        }
      }
    })

    // 注册保存字幕编辑数据处理器
    ipcMain.handle('subtitle:save', async (_, roomId: string, editData: any[]) => {
      try {
        return await saveSubtitleEdits(roomId, editData)
      } catch (error) {
        console.error('保存字幕编辑数据失败:', error)
        return {
          success: false,
          error: (error as Error).message || '保存字幕编辑数据失败'
        }
      }
    })

    // 注册读取字幕编辑数据处理器
    ipcMain.handle('subtitle:load', async (_, roomId: string) => {
      try {
        return await loadSubtitleEdits(roomId)
      } catch (error) {
        console.error('读取字幕编辑数据失败:', error)
        return {
          success: false,
          error: (error as Error).message || '读取字幕编辑数据失败'
        }
      }
    })

    // 注册创建房间数据处理器
    ipcMain.handle(
      'createRoomDataByUrl',
      async (
        _,
        data: {
          live_room_id: string
          start_time: string
          end_time: string
          live_duration: string
        }
      ) => {
        return await createRoomDataByUrl(data)
      }
    )

    // 注册获取音频服务IP配置处理器
    ipcMain.handle('config:getAudioSrcServeIp', async () => {
      try {
        const audioSrcServeIp = JsonDB.getItem(
          'cache/config.json',
          'audio_src_serve_ip',
          'http://127.0.0.1:5000'
        )
        return { success: true, ip: audioSrcServeIp }
      } catch (error) {
        console.error('获取音频服务IP配置失败:', error)
        return {
          success: false,
          error: (error as Error).message || '获取音频服务IP配置失败',
          ip: 'http://127.0.0.1:5000'
        }
      }
    })

    // 注册设置音频服务IP配置处理器
    ipcMain.handle('config:setAudioSrcServeIp', async (_, ip: string) => {
      try {
        JsonDB.setItem('cache/config.json', 'audio_src_serve_ip', ip)
        return { success: true, message: '音频服务IP配置已更新' }
      } catch (error) {
        console.error('设置音频服务IP配置失败:', error)
        return {
          success: false,
          error: (error as Error).message || '设置音频服务IP配置失败'
        }
      }
    })

    // 注册获取AI配置处理器
    ipcMain.handle('config:getAIConfig', async () => {
      try {
        const aiConfig = JsonDB.getItem('cache/config.json', 'ai_config', {})
        return { success: true, config: aiConfig }
      } catch (error) {
        console.error('获取AI配置失败:', error)
        return {
          success: false,
          error: (error as Error).message || '获取AI配置失败',
          config: {}
        }
      }
    })

    // 注册保存AI配置处理器
    ipcMain.handle('config:setAIConfig', async (_, config: any) => {
      try {
        JsonDB.setItem('cache/config.json', 'ai_config', config)
        return { success: true, message: 'AI配置已保存' }
      } catch (error) {
        console.error('保存AI配置失败:', error)
        return {
          success: false,
          error: (error as Error).message || '保存AI配置失败'
        }
      }
    })

    // 注册音频服务状态检测处理器
    ipcMain.handle('config:checkAudioServiceStatus', async (_, serviceUrl?: string) => {
      try {
        // 如果没有提供服务地址，从配置中获取
        const speechServiceUrl =
          serviceUrl ||
          JsonDB.getItem('cache/config.json', 'audio_src_serve_ip', 'http://127.0.0.1:5000')

        // 使用 @gradio/client 检测服务状态
        const { Client } = await import('@gradio/client')
        const client = await Client.connect(speechServiceUrl)

        // 调用 check_status API
        const statusResult = await client.predict('/check_status', {})
        // 解析返回结果
        const statusData = JSON.parse(statusResult.data[0]).status || 'false'
        const isSuccess = statusData === 'success'

        return {
          success: true,
          status: isSuccess ? 'success' : 'error',
          message: isSuccess ? '服务连接正常' : '服务状态异常',
          serviceUrl: speechServiceUrl,
          timestamp: new Date().toLocaleString()
        }
      } catch (error) {
        console.error('音频服务状态检测失败:', error)
        return {
          success: false,
          status: 'error',
          message: `连接失败: ${error instanceof Error ? error.message : '未知错误'}`,
          serviceUrl: serviceUrl || 'http://127.0.0.1:5000',
          timestamp: new Date().toLocaleString()
        }
      }
    })

    // 注册获取违禁词配置处理器
    ipcMain.handle('config:getBannedWords', async () => {
      try {
        const bannedWords = JsonDB.getItem('cache/config.json', 'banned_words', [])
        return { success: true, words: bannedWords }
      } catch (error) {
        console.error('获取违禁词配置失败:', error)
        return {
          success: false,
          error: (error as Error).message || '获取违禁词配置失败',
          words: []
        }
      }
    })

    // 注册保存违禁词配置处理器
    ipcMain.handle('config:setBannedWords', async (_, words: any[]) => {
      try {
        console.log('保存违禁词配置:', words)
        JsonDB.setItem('cache/config.json', 'banned_words', words)
        return { success: true, message: '违禁词配置已保存' }
      } catch (error) {
        console.error('保存违禁词配置失败:', error)
        return {
          success: false,
          error: (error as Error).message || '保存违禁词配置失败'
        }
      }
    })

    // 删除视频数据
    ipcMain.handle('/baiying/delete-video', async (_, { room_id }) => {
      try {
        console.log('开始删除视频数据，房间ID:', room_id)

        // 1. 删除 video_{room_id} 对应的文件夹
        const basePath = getAPPBasePath()
        const videoDir = path.join(basePath, `cache/video_${room_id}`)

        if (fs.existsSync(videoDir)) {
          // 递归删除文件夹及其内容
          fs.rmSync(videoDir, { recursive: true, force: true })
          console.log(`已删除视频文件夹: ${videoDir}`)
        } else {
          console.log(`视频文件夹不存在: ${videoDir}`)
        }

        // 2. 将对应的 room_{room_id} 里面的 segs 置空
        const roomKey = `cache/room_${room_id}`
        JsonDB.setItem(roomKey, 'segs', [])
        console.log(`已清空房间 ${room_id} 的 segs 数据`)

        // 3. 发送成功通知
        return {
          success: true,
          room_id,
          message: '视频数据删除成功'
        }
      } catch (error) {
        console.error('删除视频数据失败:', error)
        return {
          success: false,
          room_id,
          error: error instanceof Error ? error.message : String(error)
        }
      }
    })

    // 注册openurl消息处理器
    ipcMain.on('openurl', (_, url: string) => {
      try {
        console.log('收到openurl请求:', url)
        shell.openExternal(url)
      } catch (error) {
        console.error('打开URL失败:', error)
      }
    })

    // 注册发送全局公告处理器
    ipcMain.on('send-global-message', (_, title: string, content: string) => {
      this.sendAnnouncement(title, content)
    })

    // 注册发送一封信消息处理器
    ipcMain.on('send-letter', () => {
      const mockData = {
        message: '<div>这是一封测试信</div>',
        qa: [{ q: '1: 请问，。。。。。', c: ['1 ', '2', '3', '4'], a: [1, 2] }]
      }
      this.sendLetter(mockData)
    })
  }

  /**
   * 发送公告给前端
   * @param title 公告标题
   * @param content 公告内容
   */
  public static sendAnnouncement(title: string, content: string): void {
    // 获取所有窗口
    const windows = BrowserWindow.getAllWindows()

    // 向所有窗口发送公告
    windows.forEach((window) => {
      if (!window.isDestroyed()) {
        window.webContents.send('announcement', {
          title,
          content
        })
      }
    })

    console.log(`发送公告: ${title} - ${content}`)
  }

  /**
   * 发送一封信给前端
   * @param letterData 一封信数据，格式为 {message: string, qa: [{q: string, c: string[], a: number[]}]}
   */
  public static sendLetter(letterData: {
    message: string
    qa: Array<{ q: string; c: string[]; a: number[] }>
  }): void {
    // 获取所有窗口
    const windows = BrowserWindow.getAllWindows()

    // 向所有窗口发送一封信
    windows.forEach((window) => {
      if (!window.isDestroyed()) {
        window.webContents.send('letter', letterData)
      }
    })

    console.log(
      `发送一封信: 消息长度=${letterData.message.length}, 题目数量=${letterData.qa.length}`
    )
  }
}
