# 优化segs分钟边界换行逻辑

## 任务背景

字幕显示需要根据时间边界智能添加换行，而不是简单的句号换行。

## 需求分析

1. 当某条seg跨越分钟线时添加换行（如从59.74秒到64.325秒）
2. 当某条seg在59秒结束且下一条从下一分钟开始时添加换行
3. 移除原有的句号换行逻辑

## 实现方案

1. 创建 `shouldAddLineBreak` 函数检测分钟边界
2. 创建 `processedSegs` 计算属性处理换行逻辑
3. 更新template使用新的数据结构
4. 修复props直接修改的问题，改用emit

## 技术细节

- 时间计算：毫秒转分钟 `Math.floor(timestamp / 60000)`
- 秒数计算：`Math.floor((timestamp % 60000) / 1000)`
- 换行标记：使用 `<br><br>` 实现段落分隔

## 执行状态

✅ 已完成 - 2025年6月24日
