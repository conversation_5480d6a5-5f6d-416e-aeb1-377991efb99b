# 全局模态框系统使用指南

## 概述

全局模态框系统是一个统一的弹窗解决方案，用于替代原生的 `alert()` 函数，提供更好的用户体验和更灵活的自定义选项。

## 功能特性

- 🎨 统一的UI设计，支持亮色/暗色主题
- 🔧 多种调用方式，灵活易用
- 🎯 支持回调函数，可在确认后执行特定操作
- 📱 响应式设计，适配各种屏幕尺寸
- 🌐 兼顾后端消息推送和前端调用

## 安装和配置

系统已经自动集成到主应用中，无需额外配置。相关文件：

- `src/renderer/src/composables/useGlobalModal.ts` - 核心逻辑
- `src/renderer/src/components/modals/GlobalModal.vue` - 模态框组件
- `src/renderer/src/App.vue` - 主应用集成

## 使用方法

### 1. 基础用法

```typescript
import { useGlobalModal } from '@/composables/useGlobalModal'

const { showModal } = useGlobalModal()

// 基础调用
showModal('标题', '内容')
```

### 2. 带回调函数的调用

```typescript
showModal('确认操作', '您确定要执行此操作吗？', () => {
  console.log('用户点击了确认')
  // 执行具体操作
})
```

### 3. 对象参数调用

```typescript
showModal({
  title: '自定义标题',
  content: '自定义内容',
  confirmButtonText: '确定',
  onConfirm: () => {
    console.log('确认回调')
  }
})
```

### 4. 快捷方法

```typescript
const { showSuccess, showError, showInfo } = useGlobalModal()

// 成功提示
showSuccess('操作成功！')

// 错误提示
showError('操作失败！')

// 信息提示
showInfo('温馨提示信息')

// 带回调的快捷方法
showSuccess('保存成功！', () => {
  // 跳转到其他页面
  router.push('/dashboard')
})
```

## 后端消息支持

系统自动监听后端发送的 `announcement` 事件：

```typescript
// 后端发送消息格式
window.electron.ipcRenderer.send('announcement', {
  title: '系统公告',
  content: '这是来自后端的消息',
  confirmButtonText: '我知道了'
})
```

## API 参考

### showModal 函数重载

```typescript
// 方式1：字符串参数
showModal(title: string, content: string, onConfirm?: () => void): void

// 方式2：对象参数
showModal(data: {
  title?: string
  content: string
  confirmButtonText?: string
  onConfirm?: () => void
}): void
```

### 快捷方法

```typescript
showSuccess(message: string, onConfirm?: () => void): void
showError(message: string, onConfirm?: () => void): void
showInfo(message: string, onConfirm?: () => void): void
```

### 事件监听方法

```typescript
initAnnouncementEventListeners(): void    // 初始化后端消息监听
cleanupAnnouncementEventListeners(): void // 清理后端消息监听
```

## 测试功能

在应用的顶部导航栏中，我们添加了"模态框测试"按钮，包含以下测试选项：

- **基础模态框** - 测试基本的标题和内容显示
- **成功提示** - 测试成功样式的模态框
- **错误提示** - 测试错误样式的模态框
- **信息提示** - 测试信息样式的模态框
- **回调测试** - 测试onConfirm回调函数功能

## 最佳实践

1. **替代 alert()** - 在所有需要用户确认的场景中使用全局模态框
2. **语义化调用** - 根据消息类型选择合适的快捷方法
3. **回调处理** - 在需要用户确认后执行操作时，使用onConfirm回调
4. **内容格式** - 支持换行符（\n）自动转换为HTML的<br>标签

## 迁移指南

### 从原生 alert() 迁移

```typescript
// 旧代码
alert('操作成功！')

// 新代码
const { showSuccess } = useGlobalModal()
showSuccess('操作成功！')
```

### 从原有公告系统迁移

```typescript
// 旧代码
const { showAnnouncement } = useAnnouncement()
showAnnouncement({ title: '标题', content: '内容' })

// 新代码
const { showModal } = useGlobalModal()
showModal({ title: '标题', content: '内容' })
```

## 技术细节

- 基于 Bootstrap 5 Modal 组件
- 使用 Vue 3 Composition API
- 支持 TypeScript 类型检查
- **单例模式设计** - 全局共享同一个模态框数据实例，确保数据正确显示
- 自动处理模态框的显示/隐藏状态
- 内置防止重复调用机制

## 故障排除

### 常见问题

1. **模态框不显示**

   - 确保在组件中正确导入 `useGlobalModal`
   - 检查 Bootstrap 是否正确加载

2. **模态框显示但内容为空**

   - 这个问题已通过单例模式修复
   - 现在所有组件共享同一个模态框数据实例

3. **回调函数不执行**

   - 确保传递的是函数引用，不是函数调用结果
   - 检查函数作用域是否正确

4. **样式问题**
   - 检查是否正确应用了暗色主题类名
   - 确保 CSS 变量已正确定义

## 更新日志

- v1.0.0 - 初始版本，支持基础模态框功能
- v1.1.0 - 添加快捷方法和多种调用方式
- v1.2.0 - 完善后端消息支持和测试功能
- v1.2.1 - **修复单例模式问题** - 解决模态框内容不显示的bug，确保全局共享同一个数据实例
