import { DataTypes, Model, Sequelize } from 'sequelize'
import { RoomCoreData as IRoomCoreData } from '../interfaces'

/**
 * 直播间核心数据模型类
 * 继承自 Sequelize Model，提供类型安全的数据库操作
 */
export class RoomCoreDataModel extends Model<IRoomCoreData> implements IRoomCoreData {
  declare live_id: string
  declare zbtj: number
  declare ffll: number
  declare live_room?: string
  declare start_time?: string
  declare start_time_ts?: number
  declare end_time_ts?: number
  declare live_duration?: string
  declare online_user_cnt?: number
  declare live_show_watch_cnt_ratio?: number
  declare watch_ucnt?: number
  declare live_show_cnt?: number
  declare avg_watch_duration?: number
  declare avg_min_comment_cnt?: number
  declare watch_interact_ucnt_ratio?: number
  declare follow_anchor_ucnt?: number
  declare watch_follow_ucnt_ratio?: number
  declare fans_club_join_ucnt?: number
  declare incr_ecf_club_ucnt_ratio?: number
  declare incr_ecf_club_ucnt?: number
  declare watch_fans_club_join_ucnt_ratio?: number
  declare pay_ucnt?: number
  declare gpm?: number
  declare watch_pay_ucnt_ratio?: number
  declare product_click_pay_ucnt_ratio?: number
  declare real_refund_amt?: number
  declare real_refund_amt_ratio?: number
  declare pay_deposit_pre_order_amt?: number
  declare pay_combo_cnt?: number
  declare old_fans_pay_ucnt_ratio?: number
  declare livetoind_pay_amt?: number
  declare stat_cost?: number
  declare live_show_ucnt?: number
  declare pay_amt?: number
  declare product_show_ucnt?: number
  declare product_click_ucnt?: number
  declare product_show_click_ucnt_ratio?: number
  declare entry_watch_ucnt?: number
  declare pcu?: number
  declare like_cnt?: number
  declare comment_cnt?: number
  declare old_fans_pay_amt_ratio?: number
  declare created_at?: string
  declare updated_at?: string
  declare status?: number
  declare is_live?: number;

  // 允许动态属性
  [key: string]: any
}

/**
 * 初始化 RoomCoreData 模型
 * @param sequelize Sequelize 实例
 * @returns 初始化后的模型类
 */
export function initRoomCoreDataModel(
  sequelize: Sequelize,
  tableName: string = 'room_core_data'
): typeof RoomCoreDataModel {
  RoomCoreDataModel.init(
    {
      live_id: {
        type: DataTypes.TEXT,
        primaryKey: true,
        allowNull: false,
        comment: '直播间ID，主键'
      },
      zbtj: {
        type: DataTypes.FLOAT,
        allowNull: false,
        defaultValue: 0,
        comment: '直播推荐'
      },
      ffll: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '付费流量'
      },
      live_room: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '直播间标题'
      },
      start_time: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '开播时间'
      },
      start_time_ts: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '直播开始时间时间戳'
      },
      end_time_ts: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '结束时间时间戳'
      },
      live_duration: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '直播时长'
      },
      online_user_cnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '在线用户数'
      },
      live_show_watch_cnt_ratio: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '直播观看比例'
      },
      watch_ucnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '观看用户数'
      },
      live_show_cnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '曝光次数'
      },
      avg_watch_duration: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '平均在线时长'
      },
      avg_min_comment_cnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '平均每分钟评论数'
      },
      watch_interact_ucnt_ratio: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '观看互动用户比例'
      },
      follow_anchor_ucnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '关注主播用户数'
      },
      watch_follow_ucnt_ratio: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '观看关注用户比例'
      },
      fans_club_join_ucnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '粉丝团加入用户数'
      },
      incr_ecf_club_ucnt_ratio: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '增量粉丝团用户比例'
      },
      incr_ecf_club_ucnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '增量粉丝团用户数'
      },
      watch_fans_club_join_ucnt_ratio: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '观看粉丝团加入用户比例'
      },
      pay_ucnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '支付用户数'
      },
      gpm: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '每千次观看GMV'
      },
      watch_pay_ucnt_ratio: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '观看支付用户比例'
      },
      product_click_pay_ucnt_ratio: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '产品点击支付用户比例'
      },
      real_refund_amt: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '实际退款金额'
      },
      real_refund_amt_ratio: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '实际退款金额比例'
      },
      pay_deposit_pre_order_amt: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '支付定金预购订单金额'
      },
      pay_combo_cnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '支付组合数'
      },
      old_fans_pay_ucnt_ratio: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '老粉支付用户比例'
      },
      livetoind_pay_amt: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '直播转化支付金额'
      },
      stat_cost: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '统计成本'
      },
      live_show_ucnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '曝光人数'
      },
      pay_amt: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '支付金额GMV'
      },
      product_show_ucnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '产品展示用户数'
      },
      product_click_ucnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '产品点击用户数'
      },
      product_show_click_ucnt_ratio: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '产品展示点击用户比例'
      },
      entry_watch_ucnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '入口观看用户数'
      },
      pcu: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '最高在线人数'
      },
      like_cnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '点赞数量'
      },
      comment_cnt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '评论数量'
      },
      old_fans_pay_amt_ratio: {
        type: DataTypes.FLOAT,
        allowNull: true,
        comment: '老粉支付金额比例'
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        allowNull: false,
        comment: '创建时间'
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        allowNull: false,
        comment: '更新时间'
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '状态'
      },
      is_live: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '是否直播中'
      }
    },
    {
      sequelize,
      tableName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      //  添加索引
      indexes: [
        {
          fields: ['start_time'],
          name: 'idx_room_start_time'
        },
        {
          fields: ['start_time_ts'],
          name: 'idx_room_start_time_ts'
        },
        {
          fields: ['created_at'],
          name: 'idx_room_created_at'
        }
      ],
      comment: '直播间核心数据表'
    }
  )

  return RoomCoreDataModel
}
