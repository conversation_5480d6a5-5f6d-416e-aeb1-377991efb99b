# 批量插入房间数据功能实现

## 任务概述

基于现有的 `insertRoomDataById` 函数，创建了新的批量处理函数，支持传入 ID 数组批量读取 `room_{id}` 文件并更新/新增数据库记录。

## 实现内容

### 1. 修复现有函数

- 修复了 `insertRoomDataById` 函数中的变量名错误
- 改用同步文件读取方式 `fs.readFileSync`
- 使用 SqliteDB V2 版本的 `upsertRoomCoreDataV2` 方法
- 添加了文件存在性检查和错误处理

### 2. 新增批量处理函数

- `batchInsertRoomDataByIds(ids: string[])`: 批量处理房间数据
- 支持分批处理，避免单个事务过大（批次大小：50）
- 使用 Promise.all 并行处理提高性能
- 返回详细的处理结果统计信息

### 3. IPC 处理器

- `lzy:insertRoomData`: 单个房间数据插入
- `lzy:batchInsertRoomData`: 批量房间数据插入

### 4. 测试方法

- `testInsertRoomData(id: string)`: 测试单个插入
- `testBatchInsertRoomData(ids: string[])`: 测试批量插入

## 使用方式

### 前端调用

```typescript
// 单个插入
const result = await window.electron.ipcRenderer.invoke('lzy:insertRoomData', 'room_id_123')

// 批量插入
const batchResult = await window.electron.ipcRenderer.invoke('lzy:batchInsertRoomData', [
  'id1',
  'id2',
  'id3'
])
```

### 后端直接调用

```typescript
// 单个插入
const success = await LZYService.testInsertRoomData('room_id_123')

// 批量插入
const result = await LZYService.testBatchInsertRoomData(['id1', 'id2', 'id3'])
console.log(
  `处理完成：成功 ${result.successCount}/${result.totalRooms}，失败 ${result.failedRooms.length} 个`
)
```

## 返回数据格式

### 单个插入返回

```typescript
boolean // true-成功，false-失败
```

### 批量插入返回

```typescript
{
  totalRooms: number;      // 总房间数
  successCount: number;    // 成功处理数
  failedRooms: string[];   // 失败的房间ID列表
}
```

## 技术特点

- 使用 SqliteDB V2 版本确保数据一致性
- 分批处理避免内存溢出
- 并行处理提高性能
- 完善的错误处理和日志记录
- 支持文件不存在的情况处理
