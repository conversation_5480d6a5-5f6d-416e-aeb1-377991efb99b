---
description:
globs:
alwaysApply: true
---
以下是所有可用工具及其完整参数：

1. `codebase_search` - 在代码库中查找与搜索查询最相关的代码片段
   - `explanation`：解释工具使用原因和贡献（可选）
   - `query`：搜索查询文本（必需）
   - `target_directories`：要搜索的目录的Glob模式数组（可选）

2. `read_file` - 读取文件内容
   - `target_file`：要读取的文件路径（必需）
   - `offset`：开始读取的行偏移量（可选）
   - `limit`：要读取的行数（可选）
   - `should_read_entire_file`：是否读取整个文件（可选，布尔值）

3. `run_terminal_cmd` - 代表用户运行终端命令
   - `command`：要执行的终端命令（必需）
   - `explanation`：解释命令需要运行的原因和贡献（可选）
   - `is_background`：命令是否应在后台运行（必需，布尔值）

4. `list_dir` - 列出目录的内容
   - `relative_workspace_path`：要列出内容的相对工作区路径（必需）
   - `explanation`：解释工具使用原因和贡献（可选）

5. `grep_search` - 使用ripgrep引擎在文本文件上运行快速、精确的正则表达式搜索
   - `query`：要搜索的正则表达式模式（必需）
   - `case_sensitive`：搜索是否区分大小写（可选，布尔值）
   - `exclude_pattern`：要排除的文件的Glob模式（可选）
   - `explanation`：解释工具使用原因和贡献（可选）
   - `include_pattern`：要包含的文件的Glob模式（可选）

6. `edit_file` - 提出对现有文件的编辑或创建新文件
   - `target_file`：要修改的目标文件（必需）
   - `instructions`：描述编辑内容的简短说明（必需）
   - `code_edit`：指定要编辑的代码行（必需）

7. `search_replace` - 对现有文件进行搜索和替换操作
   - `file_path`：要搜索和替换的文件路径（必需）
   - `old_string`：要替换的文本（必需）
   - `new_string`：替换为的编辑后文本（必需）

8. `file_search` - 基于文件路径的模糊匹配的快速文件搜索
   - `query`：要搜索的模糊文件名（必需）
   - `explanation`：解释工具使用原因和贡献（必需）

9. `delete_file` - 删除指定路径的文件
   - `target_file`：要删除的文件路径（必需）
   - `explanation`：解释工具使用原因和贡献（必需）

10. `reapply` - 调用更智能的模型来应用对指定文件的最后一次编辑
    - `target_file`：要重新应用最后一次编辑的文件的相对路径（必需）

11. `web_search` - 在网上搜索任何主题的实时信息
    - `search_term`：在网上查询的搜索词（必需）
    - `explanation`：解释工具使用原因和贡献（必需）

12. `mcp_API__read_project_oas_aehh55` - 读取"APP"的OpenAPI Spec文件内容
    - `_`：字符串类型参数（可选）

13. `mcp_API__read_project_oas_ref_resources_aehh55` - 读取"APP"的OpenAPI Spec文件内$ref的文件内容
    - `path`：OpenAPI Spec文件内$ref的值数组（必需）

14. `mcp_API__refresh_project_oas_aehh55` - 从服务器重新下载最新的"APP"的OpenAPI Spec文件内容
    - `_`：字符串类型参数（可选）

以上是所有可用工具及其完整参数列表。


正确的调用方式
<antml:function_calls>
<antml:invoke name="函数名称">
<antml:parameter name="参数名称">参数值</antml:parameter>
</antml:invoke>
</antml:function_calls>


<antml:function_calls>
<antml:invoke name="read_file">
<antml:parameter name="target_file">main.go</antml:parameter>
<antml:parameter name="should_read_entire_file">true</antml:parameter>
</antml:invoke>

</antml:function_calls>


