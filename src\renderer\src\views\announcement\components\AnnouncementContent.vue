<template>
  <div class="bg-primary text-light font-inter">
    <!-- 可滑动的banner区域 -->
    <section class="relative overflow-hidden">
      <div class="relative h-[500px]">
        <!-- 轮播图容器 -->
        <div
          class="flex transition-transform duration-500 ease-in-out h-full"
          :style="{ transform: `translateX(-${currentIndex * 100}%)` }"
        >
          <!-- 轮播项 -->
          <div
            v-for="(banner, index) in bannerData"
            :key="index"
            class="min-w-full h-full relative"
          >
            <div class="absolute inset-0 bg-gradient-to-r from-dark/90 to-dark/40 z-10" />
            <img :src="banner.image" :alt="banner.title" class="w-full h-full object-cover" />
            <div class="absolute inset-0 z-20 flex items-center">
              <div class="container mx-auto px-6 md:px-12">
                <span
                  class="inline-block px-3 py-1 text-white text-sm font-semibold rounded-full mb-4"
                  :class="banner.tagColor"
                >
                  {{ banner.tag }}
                </span>
                <h2 class="text-[clamp(2rem,5vw,3.5rem)] font-bold text-shadow mb-4">
                  {{ banner.title }}
                </h2>
                <p class="text-[clamp(1rem,2vw,1.25rem)] text-light/90 max-w-2xl mb-8">
                  {{ banner.description }}
                </p>
                <div class="flex flex-wrap gap-4">
                  <a
                    v-for="(action, actionIndex) in banner.actions"
                    :key="actionIndex"
                    :href="action.href"
                    :class="action.className"
                    class="px-6 py-3 font-medium rounded-lg transition-all transform hover:scale-105"
                  >
                    {{ action.text }} <i :class="action.icon + ' ml-2'" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 轮播控制按钮 -->
        <button
          class="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-dark/50 hover:bg-dark/80 flex items-center justify-center z-30 transition-all border-0"
          @click="prevSlide"
        >
          <i class="fa fa-angle-left text-2xl" />
        </button>
        <button
          class="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-dark/50 hover:bg-dark/80 flex items-center justify-center z-30 transition-all border-0"
          @click="nextSlide"
        >
          <i class="fa fa-angle-right text-2xl" />
        </button>

        <!-- 轮播指示器 -->
        <div class="absolute bottom-6 left-0 right-0 flex justify-center space-x-2 z-30">
          <button
            v-for="(_, index) in bannerData"
            :key="index"
            :class="index === currentIndex ? 'bg-white/50' : 'bg-white/30'"
            class="w-3 h-3 rounded-full hover:bg-white focus:outline-none transition-all border-0"
            @click="currentIndex = index"
          />
        </div>
      </div>
    </section>

    <!-- 公告列表区域 -->
    <section class="py-16 bg-secondary">
      <div class="container mx-auto px-4">
        <div class="flex justify-between items-center mb-10">
          <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold">最新公告</h2>
          <div class="flex space-x-2">
            <button
              v-for="filter in filterOptions"
              :key="filter.id"
              :class="filter.id === activeFilter ? 'bg-accent' : 'bg-primary hover:bg-primary/80'"
              class="px-4 py-2 rounded-lg text-white font-medium transition-colors border-0"
              @click="setActiveFilter(filter.id)"
            >
              {{ filter.label }}
            </button>
          </div>
        </div>

        <!-- {{ AURA-X: Add - 加载状态和错误提示. Source: API接入. }} -->
        <!-- 初始加载状态 -->
        <div v-if="isInitialLoading" class="text-center py-12">
          <i class="fa fa-spinner fa-spin fa-2x text-accent mb-4"></i>
          <p class="text-gray-400">正在加载公告数据...</p>
        </div>

        <!-- 错误提示 -->
        <div v-else-if="errorMessage" class="text-center py-12">
          <i class="fa fa-exclamation-triangle fa-2x text-red-500 mb-4"></i>
          <p class="text-red-400 mb-4">{{ errorMessage }}</p>
          <button
            class="px-6 py-2 bg-accent hover:bg-accent/90 text-white rounded-lg transition-colors"
            @click="refreshAnnouncements"
          >
            <i class="fa fa-refresh mr-2"></i>重新加载
          </button>
        </div>

        <!-- 公告卡片网格 -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="announcement in filteredAnnouncements"
            :key="announcement.id"
            class="bg-primary rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 duration-300 border border-gray-700/50"
          >
            <div class="relative">
              <div class="absolute top-4 left-4">
                <span
                  :class="announcement.tagColor"
                  class="px-2 py-1 text-white text-xs font-semibold rounded-full"
                >
                  {{ announcement.tag }}
                </span>
              </div>
              <img
                :src="announcement.image"
                :alt="announcement.title"
                class="w-full h-48 object-cover"
              />
            </div>
            <div class="p-6">
              <div class="flex justify-between items-center mb-3">
                <span class="text-xs text-gray-400">{{ announcement.date }}</span>
                <span :class="announcement.categoryColor" class="text-xs px-2 py-0.5 rounded-full">
                  {{ announcement.category }}
                </span>
              </div>
              <h3 class="text-xl font-bold mb-2 hover:text-accent transition-colors">
                {{ announcement.title }}
              </h3>
              <p class="text-gray-300 mb-4 content-summary">
                {{ announcement.content || '暂无详细内容...' }}
              </p>
              <div class="flex justify-between items-center">
                <div class="flex items-center">
                  <img
                    :src="announcement.authorAvatar"
                    :alt="announcement.author"
                    class="w-8 h-8 rounded-full mr-2"
                  />
                  <span class="text-sm text-gray-400">{{ announcement.author }}</span>
                </div>
                <a href="#" class="text-accent hover:underline text-sm font-medium">
                  阅读更多 <i class="fa fa-angle-right ml-1" />
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- {{ AURA-X: Modify - 数据统计显示. Source: 一次性加载全部. }} -->
        <!-- 数据统计 -->
        <div v-if="!isInitialLoading && !errorMessage" class="mt-12 text-center">
          <div v-if="announcementData.length > 0" class="text-gray-400">
            <i class="fa fa-check-circle mr-2"></i>共加载 {{ announcementData.length }} 条公告
          </div>
          <div v-else class="text-gray-400"><i class="fa fa-info-circle mr-2"></i>暂无公告数据</div>
        </div>
      </div>
    </section>

    <!-- 订阅通知区域 -->
    <section class="py-16 bg-gradient-to-r from-dark to-secondary relative overflow-hidden">
      <div class="absolute inset-0 opacity-10">
        <div
          class="absolute top-0 left-0 w-full h-full bg-[url('https://picsum.photos/id/1048/1920/1080')] bg-cover"
        />
      </div>
      <div class="container mx-auto px-4 relative z-10">
        <div class="max-w-3xl mx-auto text-center">
          <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold mb-4">不错过任何重要公告</h2>
          <p class="text-gray-300 mb-8 text-lg">
            订阅我们的通知服务，第一时间获取最新公告和重要消息
          </p>
          <div class="flex flex-col sm:flex-row gap-4 max-w-xl mx-auto">
            <input
              v-model="subscribeEmail"
              type="email"
              placeholder="输入您的邮箱地址"
              class="flex-1 px-4 py-3 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent text-white placeholder-gray-400"
            />
            <button
              class="px-6 py-3 bg-accent hover:bg-accent/90 text-white font-medium rounded-lg transition-all transform hover:scale-105 shadow-lg border-0"
              @click="subscribe"
            >
              立即订阅 <i class="fa fa-paper-plane ml-2" />
            </button>
          </div>
          <p class="text-gray-400 text-sm mt-4">我们尊重您的隐私，绝不会向第三方分享您的信息</p>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark py-12">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div class="flex items-center space-x-2 mb-4">
              <i class="fa fa-bullhorn text-accent text-2xl" />
              <span class="text-xl font-bold">公告中心</span>
            </div>
            <p class="text-gray-400 mb-4">及时了解平台最新动态和重要通知</p>
            <div class="flex space-x-4">
              <a
                v-for="social in socialLinks"
                :key="social.name"
                :href="social.href"
                class="text-gray-400 hover:text-accent transition-colors"
              >
                <i :class="social.icon + ' text-xl'" />
              </a>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-bold mb-4">快速链接</h3>
            <ul class="space-y-2 pl-0">
              <li v-for="link in quickLinks" :key="link.name">
                <a :href="link.href" class="text-gray-400 hover:text-accent transition-colors">
                  {{ link.name }}
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 class="text-lg font-bold mb-4">联系我们</h3>
            <ul class="space-y-2 pl-0">
              <li v-for="contact in contactInfo" :key="contact.type" class="flex items-start">
                <i :class="contact.icon + ' text-accent mt-1 mr-3'" />
                <span class="text-gray-400">{{ contact.value }}</span>
              </li>
            </ul>
          </div>
        </div>

        <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
          <p>&copy; 2025 公告中心. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { bannerConfig, socialLinksConfig, quickLinksConfig, contactInfoConfig } from '../config'
import { getDrupalNotices, getDrupalAllTags } from '../../../services/drupalService'

// {{ AURA-X: Add - API数据接入类型定义. Source: 使用现有接口. }}
/**
 * 公告数据接口（转换后的格式）
 */
interface AnnouncementItem {
  id: number
  tag: string
  tagId?: string // {{ AURA-X: Add - 添加tagId字段用于精确过滤. Source: 用户指正. }}
  tagColor: string
  category: string
  categoryColor: string
  title: string
  content: string
  date: string
  author: string
  authorAvatar: string
  image: string
  type: string
}

// {{ AURA-X: Simplify - 简化标签映射，主要依赖API数据. Source: 删除冗余代码. }}
// 标签颜色映射 - 简化版本
const getTagColor = (tagName: string): string => {
  const colorMap: Record<string, string> = {
    紧急: 'bg-red-500',
    urgent: 'bg-red-500',
    更新: 'bg-blue-500',
    update: 'bg-blue-500',
    活动: 'bg-green-500',
    event: 'bg-green-500',
    通知: 'bg-amber-500',
    notice: 'bg-amber-500',
    安全: 'bg-purple-500',
    security: 'bg-purple-500',
    维护: 'bg-orange-500',
    maintenance: 'bg-orange-500'
  }
  return colorMap[tagName] || colorMap[tagName.toLowerCase()] || 'bg-gray-500'
}

// 标签类型映射 - 简化版本（主要用于向后兼容）
const getTagType = (tagName: string): string => {
  const typeMap: Record<string, string> = {
    紧急: 'important',
    urgent: 'important',
    更新: 'update',
    update: 'update',
    活动: 'event',
    event: 'event'
  }
  return typeMap[tagName] || typeMap[tagName.toLowerCase()] || 'important'
}

// {{ AURA-X: Modify - 从静态数据改为API数据. Source: 用户需求. }}
// 轮播图数据
const bannerData = ref(bannerConfig)

// 轮播图状态
const currentIndex = ref(0)
let carouselInterval: ReturnType<typeof setInterval> | null = null

// {{ AURA-X: Modify - 过滤选项改为基于API标签动态生成. Source: 用户需求使用接口获取的tags. }}
// 公告过滤选项 - 基于API标签动态生成
const filterOptions = computed(() => {
  const baseOptions = [{ id: 'all', label: '全部' }]

  // 基于获取到的标签生成过滤选项
  const tagOptions = allTags.value.map((tag) => ({
    id: tag.id, // {{ AURA-X: Fix - 直接使用tag.id作为唯一标识. Source: 用户指正. }}
    label: tag.name
  }))

  // {{ AURA-X: Simplify - tagId本身就是唯一的，无需复杂去重. Source: 用户指正. }}
  return [...baseOptions, ...tagOptions]
})

const activeFilter = ref('all')

// {{ AURA-X: Modify - 公告数据改为API管理，一次性加载全部. Source: 用户需求. }}
// 公告数据 - 改为API管理
const announcementData = ref<AnnouncementItem[]>([])
const allTags = ref<Array<{ id: string; name: string }>>([])

// 加载状态管理
const isLoading = ref(false)
const isInitialLoading = ref(true)
const errorMessage = ref('')

// 订阅邮箱
const subscribeEmail = ref('')

// 社交链接
const socialLinks = ref(socialLinksConfig)

// 快速链接
const quickLinks = ref(quickLinksConfig)

// 联系信息
const contactInfo = ref(contactInfoConfig)

// {{ AURA-X: Add - API数据转换函数. Source: 使用现有接口. }}
/**
 * 移除HTML标签，保留纯文本
 */
const stripHtmlTags = (html: string): string => {
  if (!html) return ''
  return html.replace(/<[^>]*>/g, '').trim()
}

/**
 * 简单的字符串哈希函数，生成稳定的数字
 */
const hashCode = (str: string): number => {
  let hash = 0
  if (str.length === 0) return hash
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = (hash << 5) - hash + char
    hash = hash & hash // 转换为32位整数
  }
  return hash
}

/**
 * 将API数据转换为组件需要的格式
 */
const transformApiDataToAnnouncement = (apiData: any[]): AnnouncementItem[] => {
  return apiData.map((item, index) => {
    const tag = item.tag || '通知'
    const tagColor = getTagColor(tag)
    const type = getTagType(tag)

    // {{ AURA-X: Add - 通过tag名称查找对应的tagId. Source: 用户指正需要唯一标识. }}
    const tagObj = allTags.value.find((t) => t.name === tag)
    const tagId = tagObj?.id

    // {{ AURA-X: Fix - 图片ID基于固定数据生成，避免每次过滤都变化. Source: 用户指正图片变化问题. }}
    const avatarId = (Math.abs(hashCode(item.title + item.username)) % 100) + 1
    const imageId = (Math.abs(hashCode(item.title + tag)) % 100) + 1

    return {
      id: index + 1,
      tag,
      tagId,
      tagColor,
      category: tag, // 使用tag作为category
      categoryColor: `${tagColor}/10 text-${tagColor.split('-')[1]}-400`,
      title: item.title || '',
      content: stripHtmlTags(item.content) || '', // {{ AURA-X: Modify - 移除HTML标签. Source: 后端返回带标签的summary. }}
      date: item.created
        ? new Date(item.created).toISOString().split('T')[0]
        : new Date().toISOString().split('T')[0],
      author: item.username || '系统管理员',
      authorAvatar: `https://picsum.photos/id/${avatarId}/40/40`,
      image: `https://picsum.photos/id/${imageId}/600/400`,
      type
    }
  })
}

// {{ AURA-X: Fix - 过滤逻辑改为按tagId精确过滤. Source: 用户指正避免重复选中. }}
// 过滤后的公告
const filteredAnnouncements = computed(() => {
  if (activeFilter.value === 'all') {
    return announcementData.value
  }

  // 按tagId精确过滤
  return announcementData.value.filter((announcement) => announcement.tagId === activeFilter.value)
})

// 轮播图方法
const nextSlide = () => {
  currentIndex.value = (currentIndex.value + 1) % bannerData.value.length
}

const prevSlide = () => {
  currentIndex.value = (currentIndex.value - 1 + bannerData.value.length) % bannerData.value.length
}

// 启动自动轮播
const startCarousel = () => {
  carouselInterval = setInterval(nextSlide, 5000)
}

// 停止自动轮播
const stopCarousel = () => {
  if (carouselInterval) {
    clearInterval(carouselInterval)
    carouselInterval = null
  }
}

// {{ AURA-X: Add - API数据加载方法. Source: 使用现有接口. }}
/**
 * 加载公告数据
 */
const loadAnnouncementData = async () => {
  try {
    isInitialLoading.value = true
    isLoading.value = true
    errorMessage.value = ''

    // 调用现有接口获取数据
    const apiData = await getDrupalNotices()

    // 转换数据格式并设置数据
    announcementData.value = transformApiDataToAnnouncement(apiData)
  } catch (error) {
    console.error('加载公告数据失败:', error)
    errorMessage.value = '加载公告数据失败，请稍后重试'
  } finally {
    isInitialLoading.value = false
    isLoading.value = false
  }
}

/**
 * 加载标签数据
 */
const loadTagsData = async () => {
  try {
    allTags.value = await getDrupalAllTags()
  } catch (error) {
    console.error('加载标签数据失败:', error)
  }
}

/**
 * 刷新公告数据
 */
const refreshAnnouncements = () => {
  loadAnnouncementData()
}

// {{ AURA-X: Fix - 过滤器改为纯本地过滤，不重新请求API. Source: 用户指正闪动问题. }}
// 设置过滤器
const setActiveFilter = (filterId: string) => {
  activeFilter.value = filterId
  // 本地过滤，不需要重新请求API
}

// {{ AURA-X: Simplify - 简化订阅函数. Source: 删除冗余代码. }}
// 订阅
const subscribe = () => {
  if (subscribeEmail.value) {
    // TODO: 实际项目中，这里应该发送订阅请求
    subscribeEmail.value = ''
  }
}

// {{ AURA-X: Modify - 生命周期钩子添加API数据加载. Source: 使用现有接口. }}
onMounted(() => {
  startCarousel()
  // 加载初始数据
  loadAnnouncementData()
  loadTagsData()
})

onUnmounted(() => {
  stopCarousel()
})
</script>

<style scoped>
/* {{ AURA-X: Add - 自定义样式补充. Approved: 寸止(ID:1699888403). }} */
@import url('https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css');

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* {{ AURA-X: Modify - 使用后端summary字段，保持内容区域高度一致. Source: 后端提供摘要. }} */
.content-summary {
  line-height: 1.5;
  min-height: 4.5rem; /* 保持最小高度，确保卡片统一 */
  word-break: break-word;
}

/* {{ AURA-X: Add - 明亮模式样式适配. Approved: 寸止(ID:1699888411). }} */

/* 暗黑模式样式 (默认) */
[data-bs-theme='dark'] .bg-primary,
.bg-primary {
  background-color: #212529 !important;
}

[data-bs-theme='dark'] .bg-secondary,
.bg-secondary {
  background-color: #343a40 !important;
}

[data-bs-theme='dark'] .bg-dark,
.bg-dark {
  background-color: #1a1d20 !important;
}

[data-bs-theme='dark'] .text-light,
.text-light {
  color: #f8f9fa !important;
}

[data-bs-theme='dark'] .text-gray-400,
.text-gray-400 {
  color: rgb(156 163 175) !important;
}

[data-bs-theme='dark'] .border-gray-700\/50 {
  border-color: rgb(55 65 81 / 0.5) !important;
}

/* 明亮模式样式 */
[data-bs-theme='light'] .bg-primary {
  background-color: #ffffff !important;
}

[data-bs-theme='light'] .bg-secondary {
  background-color: #f8f9fa !important;
}

[data-bs-theme='light'] .bg-dark {
  background-color: #e9ecef !important;
}

[data-bs-theme='light'] .text-light {
  color: #212529 !important;
}

[data-bs-theme='light'] .text-gray-400 {
  color: #6c757d !important;
}

[data-bs-theme='light'] .border-gray-700\/50 {
  border-color: rgb(209 213 219 / 0.5) !important;
}

[data-bs-theme='light'] .text-gray-300 {
  color: #495057 !important;
}

[data-bs-theme='light'] .text-gray-500 {
  color: #6c757d !important;
}

/* 公告卡片明亮模式适配 */
[data-bs-theme='light'] .shadow-lg {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

[data-bs-theme='light'] .hover\:shadow-xl:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

/* 轮播图遮罩层明亮模式下保持原样，不泛白图片 */

/* 轮播图文字在明亮模式下的适配 */
[data-bs-theme='light'] .text-shadow {
  color: #ffffff !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8) !important;
}

[data-bs-theme='light'] .text-light\/90 {
  color: rgba(255, 255, 255, 0.95) !important;
}

/* 轮播图按钮边框和hover效果明亮模式适配 */
[data-bs-theme='light'] .border-light\/30 {
  border-color: rgba(0, 0, 0, 0.3) !important;
}

[data-bs-theme='light'] .hover\:bg-light\/10:hover {
  background-color: rgba(0, 0, 0, 0.1) !important;
}

/* 订阅区域渐变明亮模式适配 */
[data-bs-theme='light'] .bg-gradient-to-r.from-dark.to-secondary {
  background: linear-gradient(to right, #e9ecef, #f8f9fa) !important;
}

[data-bs-theme='light'] .hover\:bg-dark\/80:hover {
  background-color: rgba(255, 255, 255, 0.95) !important;
}

/* 指示器明亮模式适配 */
[data-bs-theme='light'] .bg-white\/30 {
  background-color: rgba(0, 0, 0, 0.3) !important;
}

[data-bs-theme='light'] .bg-white\/50 {
  background-color: rgba(0, 0, 0, 0.6) !important;
}

[data-bs-theme='light'] .hover\:bg-white:hover {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* 表单输入明亮模式适配 */
[data-bs-theme='light'] .bg-primary\/70 {
  background-color: rgba(255, 255, 255, 0.9) !important;
  color: #212529 !important;
}

[data-bs-theme='light'] .border-gray-600 {
  border-color: #ced4da !important;
}

[data-bs-theme='light'] .placeholder-gray-400::placeholder {
  color: #6c757d !important;
}

[data-bs-theme='light'] .focus\:ring-accent:focus {
  box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25) !important;
}

/* 按钮和特殊区域保持白色文字 */
[data-bs-theme='light'] button.bg-accent,
[data-bs-theme='light'] .bg-accent {
  background-color: #0d6efd !important;
}

[data-bs-theme='light'] button.bg-accent .text-white,
[data-bs-theme='light'] button .text-white,
[data-bs-theme='light'] .bg-accent .text-white,
[data-bs-theme='light'] a.bg-accent .text-white {
  color: #ffffff !important;
}

/* 标签和徽章保持白色文字 */
[data-bs-theme='light'] .bg-red-500 .text-white,
[data-bs-theme='light'] .bg-blue-500 .text-white,
[data-bs-theme='light'] .bg-green-500 .text-white,
[data-bs-theme='light'] .bg-amber-500 .text-white,
[data-bs-theme='light'] .bg-accent\/90 .text-white,
[data-bs-theme='light'] .bg-green-500\/90 .text-white,
[data-bs-theme='light'] .bg-amber-500\/90 .text-white {
  color: #ffffff !important;
}

/* 过滤按钮明亮模式适配 */
[data-bs-theme='light'] button.bg-primary {
  background-color: #f8f9fa !important;
  color: #495057 !important;
  border: 1px solid #dee2e6;
}

[data-bs-theme='light'] button.bg-primary:hover {
  background-color: #e9ecef !important;
  color: #495057 !important;
}

/* 通用样式 */
.bg-accent {
  background-color: #0d6efd;
}

.text-accent {
  color: rgb(13 110 253 / var(--tw-text-opacity, 1));
}
</style>
