import { computed } from 'vue'
import { useStore } from 'vuex'
import { chartNameColorMap, chartColorPalette } from '../utils/chartUtils'

/**
 * 全局颜色映射管理 Composable
 * 提供统一的颜色映射管理，确保所有组件使用一致的颜色
 */
export function useGlobalColorMapping() {
  const store = useStore()

  /**
   * 获取全局颜色映射
   */
  const globalColorMapping = computed(() => store.state.colorMapping)

  /**
   * 更新全局颜色映射
   * @param seriesNames 系列名称数组
   */
  const updateGlobalColorMapping = (seriesNames: string[]) => {
    const mapping: Record<string, string> = {}

    // 为每个系列分配颜色，优先使用chartNameColorMap，找不到再使用chartColorPalette
    seriesNames.forEach((name, index) => {
      if (chartNameColorMap[name]) {
        mapping[name] = chartNameColorMap[name]
      } else {
        mapping[name] = chartColorPalette[index % chartColorPalette.length]
      }
    })

    store.dispatch('updateColorMapping', mapping)
  }

  /**
   * 根据系列名称获取颜色
   * @param seriesName 系列名称
   * @returns 颜色值，如果找不到则返回默认颜色
   */
  const getColorBySeriesName = (seriesName: string): string => {
    return globalColorMapping.value[seriesName] || '#ccc'
  }

  /**
   * 检查是否已有颜色映射
   */
  const hasColorMapping = computed(() => {
    return Object.keys(globalColorMapping.value).length > 0
  })

  /**
   * 重置颜色映射
   */
  const resetColorMapping = () => {
    store.dispatch('updateColorMapping', {})
  }

  return {
    globalColorMapping,
    updateGlobalColorMapping,
    getColorBySeriesName,
    hasColorMapping,
    resetColorMapping
  }
}
