# 页面缩放功能实现

## 需求描述

在 Menu.vue 组件右侧添加缩放按钮，点击后展示滑块控制页面缩放，支持100%-200%缩放范围，需要持久化存储，返回首页时还原大小。

## 实现方案

使用 CSS zoom 属性实现页面缩放，通过 composable 管理状态和持久化存储。

## 技术实现

### 1. 创建 useZoom composable

- 文件路径：`src/renderer/src/composables/useZoom.ts`
- 功能：
  - 缩放级别状态管理 (100-200%)
  - localStorage 持久化存储
  - 路由监听，首页时重置缩放
  - 滑块显示状态控制
  - 图表自适应：缩放时触发resize事件和ApexCharts的windowResize方法

### 2. 修改 Menu.vue 组件

- 在导航栏右侧添加缩放按钮
- 集成 Element Plus 弹出层和滑块组件
- 显示当前缩放百分比
- 提供重置按钮

### 3. 全局状态管理

- 在 App.vue 中初始化缩放功能
- 使用全局状态避免组件卸载导致的状态丢失
- 路由监听在应用级别统一管理

### 4. 核心功能

- **缩放范围**：100% - 200%
- **步长**：25%（100%, 125%, 150%, 175%, 200%）
- **持久化**：localStorage存储用户设置
- **智能缩放管理**：
  - 在首页时自动重置为100%
  - 离开首页前保存当前缩放级别
  - 从首页进入其他页面时恢复之前的缩放级别
- **实时显示**：按钮上显示当前缩放百分比
- **图表适配**：缩放时自动触发图表resize，支持ECharts和ApexCharts

## 文件变更

1. 新增：`src/renderer/src/composables/useZoom.ts`
2. 修改：`src/renderer/src/components/Menu.vue`
3. 修改：`src/renderer/src/App.vue`（添加全局缩放初始化）

## 使用说明

1. 点击导航栏右侧的缩放按钮（放大镜图标+百分比）
2. 在弹出的滑块中调整缩放级别
3. 点击"重置"按钮可快速回到100%
4. 缩放设置会自动保存，下次打开应用时保持
5. 在首页时显示100%，进入其他页面时恢复之前的缩放级别

## 实现状态

✅ 已完成实现
