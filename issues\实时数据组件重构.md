# 实时数据组件重构任务

## 任务目标

1. 修复RealTime 的数据流向
2. 优化样式，将配置指标的按钮移动到实时数据右侧
3. 将实时数据外层包裹移动到组件内部去

## 已完成工作

### 1. 修复数据流向问题 ✅

- 修复了 `useRealTimeMetrics.ts` 中的时间同步发送者标识配置
- 将 `TimeSyncSender.LINKAGE` 改为 `TimeSyncSender.REALTIME_METRICS`
- 添加了完整的函数返回类型注解

### 2. 重构 RealTimeMetricGrid 组件为自包含卡片 ✅

- 将完整的 card 包装移入组件内部
- 添加了 `beganTime` prop 支持
- 更新了标题显示逻辑，配置按钮位于标题右侧
- 调整了样式确保与原布局一致

### 3. 简化 LinkAge 组件中的实时数据区域 ✅

- 移除了外层 card 包装代码
- 简化为单行组件调用 `<RealTimeMetricGrid :began-time="beganTime" />`
- 保持了原有的 Bootstrap 栅格布局

### 4. 修复代码质量问题 ✅

- 添加了函数返回类型注解
- 清理了未使用的变量和导入
- 修复了类型错误和空catch块
- 处理了Bootstrap组件初始化问题

### 5. 修复 LinkAge 双向联动问题 ✅

- 在 `linkagefn` 函数中添加消息发送逻辑
- 点击字幕/公屏评论时发送消息给 TrendAnalysis 组件
- 简化数据流，统一由 TrendAnalysis 转发实时数据
- 确保完整的双向数据流

### 6. 实现 RealTimeMetricGrid 完整指标支持 ✅

- 创建了包含所有可能指标的完整配置 (ALL_POSSIBLE_REALTIME_METRICS)
- 包含原有的 METRICS_CONFIG + 实际数据中发现的所有额外指标
- 支持所有实际存在的指标：在线人数、离开人数、人均观看时长、互动率、关注率、负反馈率等
- 修复了类型定义，使用正确的 MetricType 和 DataFormat 枚举
- 移除了"动态"标签，统一显示所有可配置指标
- 在初始化时就加载所有可能的指标，不依赖实时数据

## 数据流向

现在的数据流为：

1. **LinkAge → TrendAnalysis**：
   - 视频播放器事件发送时间戳 (TimeSyncSender.LINKAGE)
   - 点击字幕/公屏评论发送时间戳 (TimeSyncSender.LINKAGE)
2. **TrendAnalysis → LinkAge**：
   - 图表点击时发送时间戳 (TimeSyncSender.TREND_ANALYSIS)
3. **TrendAnalysis → RealTimeMetrics**：
   - 接收到时间戳后发送实时数据 (TimeSyncSender.REALTIME_METRICS)
4. **TrendAnalysis → RealTimeMetrics**：
   - 监听 LinkAge 消息并转发实时数据 (TimeSyncSender.REALTIME_METRICS)
5. **RealTimeMetricGrid 组件监听并显示实时数据**

## 剩余问题

- 可能还有少量 linter 警告需要处理
- 需要测试数据流是否正常工作
- 需要验证样式和布局是否符合预期

## 测试检查清单

- [ ] 视频播放时实时数据是否正确更新
- [ ] 点击字幕时 TrendAnalysis 是否高亮对应时间点
- [ ] 点击公屏评论时 TrendAnalysis 是否高亮对应时间点
- [ ] 点击字幕/公屏时实时数据是否同步更新
- [ ] TrendAnalysis 点击图表时 LinkAge 是否跳转视频
- [ ] 配置按钮是否在正确位置
- [ ] 组件样式是否与原设计一致
- [ ] 响应式布局是否正常
- [ ] 无 TypeScript 错误
