import { DataTypes, Model, Sequelize } from 'sequelize'
import { Enter as IEnter } from '../interfaces'

/**
 * 进入记录数据模型类
 * 继承自 Sequelize Model，提供类型安全的数据库操作
 */
export class EnterModel extends Model<IEnter> implements IEnter {
  declare id?: number
  declare user_id: string
  declare room_id?: string
  declare nick_name?: string
  declare douyin_level?: number
  declare badge_level?: number
  declare enter_count: number
  declare has_profile_video?: boolean
  declare has_showcase?: boolean
  declare follower_count?: number
  declare created_at?: string
  declare updated_at?: string
}

/**
 * 初始化 Enter 模型
 * 定义表结构、字段类型、索引等
 *
 * @param sequelize Sequelize 实例
 * @param tableName 表名，默认为 'enter'
 * @returns 初始化后的 EnterModel 类
 */
export function initEnterModel(
  sequelize: Sequelize,
  tableName: string = 'enter'
): typeof EnterModel {
  EnterModel.init(
    {
      // ===== 基础标识字段 =====
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '自增主键'
      },
      user_id: {
        type: DataTypes.TEXT,
        allowNull: false,
        unique: true,
        comment: '用户唯一标识，唯一索引'
      },
      room_id: {
        type: DataTypes.TEXT,
        comment: '房间ID'
      },
      nick_name: {
        type: DataTypes.TEXT,
        comment: '用户昵称'
      },

      // ===== 等级信息 =====
      douyin_level: {
        type: DataTypes.INTEGER,
        comment: '抖音等级'
      },
      badge_level: {
        type: DataTypes.INTEGER,
        comment: '灯牌等级'
      },

      // ===== 统计信息 =====
      enter_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '进入次数，默认为0'
      },

      // ===== 用户属性 =====
      has_profile_video: {
        type: DataTypes.BOOLEAN,
        comment: '是否有主页视频'
      },
      has_showcase: {
        type: DataTypes.BOOLEAN,
        comment: '是否有橱窗'
      },
      follower_count: {
        type: DataTypes.INTEGER,
        comment: '关注人数'
      },

      // ===== 系统字段 =====
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '创建时间'
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '更新时间'
      }
    },
    {
      sequelize,
      tableName,
      timestamps: true, // 启用自动时间戳
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      comment: '用户进入记录表',

      // 索引配置
      indexes: [
        {
          unique: true,
          fields: ['user_id'],
          name: 'idx_enter_user_id_unique'
        },
        {
          fields: ['room_id'],
          name: 'idx_enter_room_id'
        },
        {
          fields: ['enter_count'],
          name: 'idx_enter_count'
        },
        {
          fields: ['created_at'],
          name: 'idx_enter_created_at'
        },
        {
          fields: ['room_id', 'user_id'],
          name: 'idx_enter_room_user'
        }
      ]
    }
  )

  console.log(`✓ Enter 模型初始化完成，表名: ${tableName}`)
  return EnterModel
}
