# 页面布局重构 - 按场次按时间对比

## 任务概述

重构RoomListPanel.vue页面布局，将原有的双时间段选择器改为按钮切换模式，提供按场次对比和按时间对比两种模式。

## 具体需求

1. 顶部两个按钮：按场次对比、按时间对比（默认按场次）
2. 按场次模式：表格工具栏左侧快捷选择（本场、上一场），右侧场次下拉选择器
3. 按时间模式：表格工具栏左侧快捷选择（7天、15天、30天、90天），右侧两个时间段选择器
4. 按时间模式下，选择时间后自动加载数据，无需手动点击分析按钮

## 已完成功能

### 1. 模式切换组件

- ✅ 添加按场次对比/按时间对比切换按钮
- ✅ 管理模式状态（comparisonMode: 'session' | 'time'）
- ✅ 默认为按场次对比模式

### 2. 表格工具栏重构

- ✅ 左侧快捷选择区域
  - 按场次模式：本场、上一场按钮
  - 按时间模式：7天、15天、30天、90天按钮
- ✅ 右侧选择器区域
  - 按场次模式：场次信息下拉表格选择器（待实现）
  - 按时间模式：时间段A和时间段B双选择器

### 3. 模板结构重构

- ✅ 移除原有的复杂时间选择器区域
- ✅ 重新组织页面布局
- ✅ 添加条件渲染逻辑

### 4. 交互逻辑优化

- ✅ 按时间模式下选择时间自动加载数据
- ✅ 按场次模式保留对比分析按钮
- ✅ 加载状态在不同模式下的不同显示

### 5. 样式美化

- ✅ 模式选择区域样式
- ✅ 表格工具栏样式
- ✅ 响应式布局支持

### 6. 技术改进

- ✅ 修复TypeScript类型错误
- ✅ 清理不再使用的代码和变量
- ✅ 添加适当的函数返回类型

### 7. 性能优化

- ✅ 重构数据加载逻辑，使用前端筛选模式
- ✅ 默认加载所有房间数据到缓存
- ✅ 时间段A/B筛选改为前端计算，避免重复API调用
- ✅ 提升用户体验和响应速度

## 待开发功能

### 场次选择器组件

- ✅ 创建场次选择下拉组件
- ✅ 表格形式的下拉选择器
- ✅ 显示场次信息（直播ID、标题、开播时间、关键指标）
- ✅ 支持单选模式
- ✅ 搜索和过滤功能
- ⏳ 与后端API集成获取场次数据

## 主要代码变更

### 性能优化逻辑

```typescript
// 新增：缓存所有房间数据
const allRoomsData = ref<RoomCoreData[]>([])

// 前端筛选：时间段A数据
const roomListA = computed(() => {
  if (isLatestTwoMode.value || !allRoomsData.value.length) {
    return allRoomsData.value
  }

  const startMoment = moment(timeRangeA.startTime).startOf('day')
  const endMoment = moment(timeRangeA.endTime).endOf('day')

  const filtered = allRoomsData.value.filter((room) => {
    if (!room.start_time) return false
    const roomMoment = moment(room.start_time)
    return roomMoment.isBetween(startMoment, endMoment, null, '[]')
  })

  const { validRooms, filteredCount } = filterValidRooms(filtered)
  filteredCountA.value = filteredCount
  return validRooms
})

// 加载所有数据方法
const loadAllRoomsData = async (): Promise<void> => {
  // 不传递参数，获取所有数据
  window.electron.ipcRenderer.send('/room/list')
}
```

### 模式切换逻辑

```typescript
// 定义对比模式类型
type ComparisonMode = 'session' | 'time'

// 对比模式状态
const comparisonMode = ref<ComparisonMode>('session')

// 模式切换方法
const switchMode = (mode: ComparisonMode): void => {
  comparisonMode.value = mode
  if (mode === 'session') {
    loadLatestTwoRooms(id)
  } else {
    quickSelectTimeRange(7)
    loadRoomData()
  }
}
```

### 自动加载逻辑

```typescript
// 时间选择处理方法
const handleTimeSelect = async (days: number): Promise<void> => {
  quickSelectTimeRange(days)
  if (comparisonMode.value === 'time') {
    await loadRoomData()
  }
}

// 日期选择器变化处理
const onDateRangeAChange = createDateRangeChangeHandler(
  timeRangeA,
  () => {
    isLatestTwoMode.value = false
    if (comparisonMode.value === 'time' && dateRangeB.value && dateRangeB.value.length === 2) {
      loadRoomData()
    }
  },
  quickSelectDays
)
```

## 用户体验改进

1. 界面更加简洁直观，减少了复杂的双时间选择器
2. 模式切换清晰，用户可以快速理解当前操作模式
3. 按时间模式下的自动加载提升了操作效率
4. 工具栏布局合理，左侧快捷操作，右侧详细配置
5. 响应式设计，适配不同屏幕尺寸
6. **性能大幅提升**：前端筛选模式避免频繁API调用，响应更快
7. **数据一致性**：所有操作基于同一数据源，确保对比结果准确
8. **离线体验**：数据缓存后，切换时间段无需等待网络请求

## 技术栈

- Vue 3 Composition API
- TypeScript
- Element Plus
- VXE Table
- Bootstrap CSS

## 文件路径

- 主文件：`src/renderer/src/views/report/RoomListPanel.vue`
- 相关Hooks：`src/renderer/src/views/report/hooks/`
- 工具函数：`src/renderer/src/views/report/utils/`
