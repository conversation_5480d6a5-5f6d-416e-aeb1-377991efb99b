import { ipcMain } from 'electron'
import * as fs from 'fs'
import logger from './utils/Logger'
import JsonDB from '../module/JsonDB'

/**
 * 缓存服务，用于管理AI分析结果的缓存
 */
class CacheService {
  private cachePath: string

  constructor() {
    // 直接使用项目根目录下的 cache/dict.json
    this.cachePath = 'cache/dict.json'

    // 确保缓存目录存在
    this.ensureCacheDirectoryExists()

    // 注册IPC处理器
    this.registerIpcHandlers()

    logger.info(`缓存服务初始化完成，缓存路径: ${this.cachePath}`)
    console.log(`缓存服务初始化完成，缓存路径: ${this.cachePath}`)
  }

  /**
   * 确保缓存目录存在
   */
  private ensureCacheDirectoryExists(): void {
    try {
      const cacheDir = 'cache'
      if (!fs.existsSync(cacheDir)) {
        fs.mkdirSync(cacheDir, { recursive: true })
        logger.info(`创建缓存目录: ${cacheDir}`)
        console.log(`创建缓存目录: ${cacheDir}`)
      }
    } catch (error: any) {
      logger.error(`确保缓存目录存在时出错: ${error.message}`)
      console.error(`确保缓存目录存在时出错: ${error.message}`)
    }
  }

  /**
   * 注册IPC处理器
   */
  private registerIpcHandlers(): void {
    // 获取分析缓存
    ipcMain.handle('cache:getAnalysisCache', async () => {
      try {
        logger.info(`尝试读取缓存文件: ${this.cachePath}`)
        console.log(`尝试读取缓存文件: ${this.cachePath}`)

        // 检查文件是否存在
        if (!fs.existsSync(this.cachePath)) {
          logger.info(`缓存文件不存在，创建空缓存`)
          console.log(`缓存文件不存在，创建空缓存`)
          return {
            success: true,
            data: {
              results: [],
              lastUpdated: new Date().toISOString()
            }
          }
        }

        // 读取缓存文件
        try {
          const cacheData = JsonDB.getRecord(this.cachePath)
          const results = cacheData.results || []
          const lastUpdated = cacheData.lastUpdated || new Date().toISOString()

          logger.info(`成功读取缓存文件，包含 ${results.length} 条结果`)
          console.log(`成功读取缓存文件，包含 ${results.length} 条结果`)

          return {
            success: true,
            data: {
              results,
              lastUpdated
            }
          }
        } catch (readError: any) {
          logger.error(`解析缓存文件失败: ${readError.message}`)
          console.error(`解析缓存文件失败: ${readError.message}`)

          // 返回空数据
          return {
            success: true,
            data: {
              results: [],
              lastUpdated: new Date().toISOString()
            },
            message: '解析缓存失败，返回空数据'
          }
        }
      } catch (error: any) {
        logger.error(`获取分析缓存时出错: ${error.message}`)
        console.error(`获取分析缓存时出错: ${error.message}`)

        // 返回空数据
        return {
          success: false,
          data: {
            results: [],
            lastUpdated: new Date().toISOString()
          },
          message: `获取缓存失败: ${error.message}`
        }
      }
    })

    // 保存分析缓存
    ipcMain.handle('cache:saveAnalysisCache', async (_, cacheData) => {
      try {
        // 确保目录存在
        this.ensureCacheDirectoryExists()

        logger.info(`准备保存缓存数据，路径: ${this.cachePath}`)
        console.log(`准备保存缓存数据，路径: ${this.cachePath}`)

        // 验证缓存数据格式
        if (!cacheData || typeof cacheData !== 'object') {
          logger.error(`无效的缓存数据格式: ${typeof cacheData}`)
          console.error(`无效的缓存数据格式: ${typeof cacheData}`)
          return { success: false, message: '无效的缓存数据格式' }
        }

        // 确保缓存数据有正确的结构
        if (!cacheData.results || !Array.isArray(cacheData.results)) {
          logger.error('缓存数据缺少必要的 results 数组字段')
          console.error('缓存数据缺少必要的 results 数组字段')
          return { success: false, message: '缓存数据格式错误' }
        }

        // 确保有 lastUpdated 字段
        if (!cacheData.lastUpdated) {
          cacheData.lastUpdated = new Date().toISOString()
        }

        // 读取现有缓存文件
        let existingData = { results: [], lastUpdated: new Date().toISOString() }
        if (fs.existsSync(this.cachePath)) {
          try {
            existingData = JsonDB.getRecord(this.cachePath)
            console.log(`读取到现有缓存，包含 ${existingData.results?.length || 0} 条结果`)
          } catch (readError) {
            console.error(`读取现有缓存失败，将使用空缓存: ${readError}`)
          }
        }

        // 确保 results 字段存在且为数组
        if (!existingData.results || !Array.isArray(existingData.results)) {
          existingData.results = []
        }

        // 合并数据并去重
        const newResults = cacheData.results
        const existingResults = existingData.results
        const mergedResults: any[] = [...existingResults]

        // 添加新结果，避免重复
        let newItemCount = 0
        for (const newItem of newResults) {
          // 检查是否已存在相同ID的结果
          const existingIndex = mergedResults.findIndex((item: any) => item.id === newItem.id)

          if (existingIndex >= 0) {
            // 更新现有项
            mergedResults[existingIndex] = newItem
            console.log(`更新现有分析结果: ${newItem.id}`)
          } else {
            // 添加新项
            mergedResults.push(newItem)
            newItemCount++
            console.log(`添加新分析结果: ${newItem.id}`)
          }
        }

        // 构建最终要保存的数据
        const finalData = {
          results: mergedResults,
          lastUpdated: new Date().toISOString()
        }

        // 将合并后的数据写入文件
        JsonDB.setRecord(this.cachePath, finalData)

        logger.info(
          `缓存数据已保存到: ${this.cachePath}，共 ${mergedResults.length} 条结果，新增 ${newItemCount} 条`
        )
        console.log(
          `缓存数据已保存到: ${this.cachePath}，共 ${mergedResults.length} 条结果，新增 ${newItemCount} 条`
        )

        // 验证文件是否存在
        if (fs.existsSync(this.cachePath)) {
          const stats = fs.statSync(this.cachePath)
          console.log(`文件已创建，大小: ${stats.size} 字节`)
        } else {
          console.error(`警告：文件写入后不存在: ${this.cachePath}`)
        }

        return {
          success: true,
          count: mergedResults.length,
          newCount: newItemCount,
          message: `成功保存 ${mergedResults.length} 条结果，新增 ${newItemCount} 条`
        }
      } catch (error: any) {
        logger.error(`保存分析缓存时出错: ${error.message}`)
        console.error(`保存分析缓存时出错: ${error.message}`)
        console.error(error.stack)
        return { success: false, message: `保存缓存失败: ${error.message}` }
      }
    })

    // 清除分析缓存
    ipcMain.handle('cache:clearAnalysisCache', async () => {
      try {
        logger.info(`准备清除缓存文件: ${this.cachePath}`)
        console.log(`准备清除缓存文件: ${this.cachePath}`)

        // 创建空缓存
        const emptyCache = {
          results: [],
          lastUpdated: new Date().toISOString()
        }

        // 直接覆盖文件
        JsonDB.setRecord(this.cachePath, emptyCache)

        logger.info(`缓存文件已清除: ${this.cachePath}`)
        console.log(`缓存文件已清除: ${this.cachePath}`)

        return { success: true, message: '缓存已清除' }
      } catch (error: any) {
        logger.error(`清除分析缓存时出错: ${error.message}`)
        console.error(`清除分析缓存时出错: ${error.message}`)
        return { success: false, message: `清除缓存失败: ${error.message}` }
      }
    })

    // 更新单个分类项
    ipcMain.handle('cache:updateSingleCategory', async (_, updateData) => {
      try {
        logger.info(`准备更新单个分类项: ${updateData.itemId}`)
        console.log(`准备更新单个分类项: ${updateData.itemId}`)

        // 验证更新数据格式
        if (!updateData || !updateData.itemId || !updateData.newCategory) {
          logger.error(`无效的更新数据格式: ${JSON.stringify(updateData)}`)
          console.error(`无效的更新数据格式: ${JSON.stringify(updateData)}`)
          return { success: false, message: '无效的更新数据格式' }
        }

        // 读取现有缓存文件
        let existingData = { results: [], lastUpdated: new Date().toISOString() }
        if (fs.existsSync(this.cachePath)) {
          try {
            existingData = JsonDB.getRecord(this.cachePath)
            console.log(`读取到现有缓存，包含 ${existingData.results?.length || 0} 条结果`)
          } catch (readError) {
            console.error(`读取现有缓存失败: ${readError}`)
            return { success: false, message: '读取现有缓存失败' }
          }
        }

        // 确保 results 字段存在且为数组
        if (!existingData.results || !Array.isArray(existingData.results)) {
          existingData.results = []
        }

        // 查找要更新的项目
        const itemIndex = existingData.results.findIndex(
          (item: any) => item.id === updateData.itemId
        )

        if (itemIndex === -1) {
          logger.error(`未找到ID为 ${updateData.itemId} 的项目`)
          console.error(`未找到ID为 ${updateData.itemId} 的项目`)
          return { success: false, message: `未找到ID为 ${updateData.itemId} 的项目` }
        }

        // 更新分类
        const oldCategory = (existingData.results[itemIndex] as any).category
        ;(existingData.results[itemIndex] as any).category = updateData.newCategory

        // 更新最后修改时间
        existingData.lastUpdated = new Date().toISOString()

        // 保存更新后的数据
        JsonDB.setRecord(this.cachePath, existingData)

        logger.info(
          `成功更新项目 ${updateData.itemId} 的分类从 ${oldCategory} 改为 ${updateData.newCategory}`
        )
        console.log(
          `成功更新项目 ${updateData.itemId} 的分类从 ${oldCategory} 改为 ${updateData.newCategory}`
        )

        return {
          success: true,
          message: `成功更新分类: ${oldCategory} → ${updateData.newCategory}`,
          oldCategory,
          newCategory: updateData.newCategory
        }
      } catch (error: any) {
        logger.error(`更新单个分类时出错: ${error.message}`)
        console.error(`更新单个分类时出错: ${error.message}`)
        return { success: false, message: `更新分类失败: ${error.message}` }
      }
    })
  }
}

export default new CacheService()
