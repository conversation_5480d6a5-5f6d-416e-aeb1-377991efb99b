import { DataTypes, Model, Sequelize } from 'sequelize'
import { Order as IOrder } from '../interfaces'

/**
 * 订单数据模型类
 * 继承自 Sequelize Model，提供类型安全的数据库操作
 */
export class OrderModel extends Model<IOrder> implements IOrder {
  declare order_id: string
  declare product_id: string
  declare product_name: string
  declare product_detail: string
  declare user_name: string
  declare total_pay_amount: number
  declare commission_rate: number
  declare estimated_comission: number
  declare real_comission: number
  declare order_status: string
  declare pay_time: number
  declare confirm_time: number
  declare settle_time: number
  declare promotion_source: number
  declare user_profit_ratio: number
  declare platform_service_fee: number
  declare account_type: string
  declare total_commission: number
  declare estimated_user_comission: number
  declare estimated_inst_comission: number
  declare settle_user_comission: number
  declare settle_inst_comission: number
  declare is_haitao: boolean
  declare stage_order: boolean
  declare stage_one_pay_time: string
  declare stage_two_pay_time: string
  declare stage_one_pay_money: number
  declare buy_at_ease: boolean
  declare shop_id: string
  declare shop_name: string
  declare commission_invoice: boolean
  declare is_stepped_plan: boolean
  declare threshold_order_count: number | null
  declare stepped_ratio: number | null
  declare estimated_user_stepped_commission: number
  declare estimated_inst_stepped_commission: number
  declare settle_user_stepped_commission: number
  declare settle_inst_stepped_commission: number
  declare estimated_settle_time: string
  declare multi_settled: boolean
  declare unsettled_event: string
  declare settle_tech_service_fee: number
  declare buyer_app_id: string
  declare frozen_rate: number
  declare unsettled_code: string
  declare traffic_source: string
  declare content_type: string
  declare media_type_group_name: string
  declare cos_ratio_type: number
  declare has_bonus_ratio: boolean
  declare instant_retail_biz_type: number
  declare created_at?: string
  declare updated_at?: string;
  // 允许动态属性
  [key: string]: any
}

/**
 * 初始化订单模型
 * @param sequelize Sequelize 实例
 * @returns 初始化后的模型类
 */
export function initOrderModel(
  sequelize: Sequelize,
  tableName: string = 'orders'
): typeof OrderModel {
  OrderModel.init(
    {
      // ===== 基础信息 =====
      order_id: { type: DataTypes.STRING, primaryKey: true, comment: '订单ID，主键' },
      product_id: { type: DataTypes.STRING, comment: '商品ID' },
      product_name: { type: DataTypes.STRING, comment: '商品名称' },
      product_detail: { type: DataTypes.TEXT, comment: '商品详情页链接' },
      user_name: { type: DataTypes.STRING, comment: '达人信息' },

      // ===== 金额与佣金 =====
      total_pay_amount: { type: DataTypes.INTEGER, comment: '计佣金额（单位：分）' },
      commission_rate: { type: DataTypes.INTEGER, comment: '佣金比例（如 2100 表示 21.00%）' },
      estimated_comission: { type: DataTypes.INTEGER, comment: '预估佣金收入（单位：分）' },
      real_comission: { type: DataTypes.INTEGER, comment: '结算佣金收入（单位：分）' },
      total_commission: { type: DataTypes.INTEGER, comment: '总佣金（含平台服务费前）' },

      // ===== 状态与时间 =====
      order_status: { type: DataTypes.STRING, comment: '订单状态，如 PAY_SUCC（支付成功）' },
      pay_time: { type: DataTypes.INTEGER, comment: '支付时间（Unix 时间戳）' },
      confirm_time: { type: DataTypes.INTEGER, comment: '成交时间（Unix 时间戳）' },
      settle_time: { type: DataTypes.INTEGER, comment: '结算时间（Unix 时间戳）' },

      // ===== 推广分佣相关 =====
      promotion_source: { type: DataTypes.INTEGER, comment: '推广来源，数值枚举（如 4 表示直播）' },
      user_profit_ratio: { type: DataTypes.INTEGER, comment: '分成比例（10000 = 100%）' },
      platform_service_fee: { type: DataTypes.INTEGER, comment: '预估技术服务费（单位：分）' },
      account_type: { type: DataTypes.STRING, comment: '账户类型，如"正式账户"' },

      // ===== 用户与机构分佣 =====
      estimated_user_comission: { type: DataTypes.INTEGER, comment: '预估用户佣金' },
      estimated_inst_comission: { type: DataTypes.INTEGER, comment: '预估机构佣金' },
      settle_user_comission: { type: DataTypes.INTEGER, comment: '结算用户佣金' },
      settle_inst_comission: { type: DataTypes.INTEGER, comment: '结算机构佣金' },

      // ===== 特殊属性 =====
      is_haitao: { type: DataTypes.BOOLEAN, comment: '是否为海淘商品' },
      stage_order: { type: DataTypes.BOOLEAN, comment: '是否为分期订单' },
      stage_one_pay_time: { type: DataTypes.STRING, comment: '分期第一期支付时间' },
      stage_two_pay_time: { type: DataTypes.STRING, comment: '分期第二期支付时间' },
      stage_one_pay_money: { type: DataTypes.INTEGER, comment: '第一期支付金额（单位：分）' },
      buy_at_ease: { type: DataTypes.BOOLEAN, comment: '是否为"放心购"商品' },

      // ===== 店铺信息 =====
      shop_id: { type: DataTypes.STRING, comment: '店铺ID' },
      shop_name: { type: DataTypes.STRING, comment: '店铺名称' },

      // ===== 发票与分佣计划 =====
      commission_invoice: { type: DataTypes.BOOLEAN, comment: '是否开具佣金发票' },
      is_stepped_plan: { type: DataTypes.BOOLEAN, comment: '是否为阶梯返佣计划' },
      threshold_order_count: { type: DataTypes.INTEGER, comment: '阶梯门槛订单数' },
      stepped_ratio: { type: DataTypes.INTEGER, comment: '阶梯分佣比例' },
      estimated_user_stepped_commission: { type: DataTypes.INTEGER, comment: '用户奖励预估佣金' },
      estimated_inst_stepped_commission: { type: DataTypes.INTEGER, comment: '机构奖励预估佣金' },
      settle_user_stepped_commission: { type: DataTypes.INTEGER, comment: '用户奖励结算佣金' },
      settle_inst_stepped_commission: { type: DataTypes.INTEGER, comment: '机构奖励结算佣金' },

      // ===== 其他系统字段 =====
      estimated_settle_time: { type: DataTypes.STRING, comment: '预估结算时间' },
      multi_settled: { type: DataTypes.BOOLEAN, comment: '是否多次结算' },
      unsettled_event: { type: DataTypes.STRING, comment: '超时未结算原因' },
      settle_tech_service_fee: { type: DataTypes.INTEGER, comment: '技术服务费（单位：分）' },
      buyer_app_id: { type: DataTypes.STRING, comment: '下单来源' },
      frozen_rate: { type: DataTypes.INTEGER, comment: '冻结比例' },
      unsettled_code: { type: DataTypes.STRING, comment: '未结算编码' },

      // ===== 内容渠道与类型 =====
      traffic_source: { type: DataTypes.STRING, comment: '流量细分渠道' },
      content_type: { type: DataTypes.STRING, comment: '订单类型' },
      media_type_group_name: { type: DataTypes.STRING, comment: '流量渠道' },
      cos_ratio_type: { type: DataTypes.INTEGER, comment: '佣金类型，数值枚举' },
      has_bonus_ratio: { type: DataTypes.BOOLEAN, comment: '是否含奖金分佣' },
      instant_retail_biz_type: { type: DataTypes.INTEGER, comment: '零售业务类型（如 0 默认）' },

      // ===== 时间戳 =====
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间'
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间'
      }
    },
    {
      sequelize,
      tableName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      indexes: [
        { fields: ['order_status'], name: 'idx_orders_order_status' },
        { fields: ['user_name'], name: 'idx_orders_user_name' },
        { fields: ['product_id'], name: 'idx_orders_product_id' },
        { fields: ['pay_time'], name: 'idx_orders_pay_time' }
      ],
      comment: '订单数据表'
    }
  )

  return OrderModel
}
