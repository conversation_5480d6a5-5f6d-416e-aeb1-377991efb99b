import fs from 'fs'
import path from 'path'
import { app } from 'electron'

const rootDir = app.getAppPath() // 返回 electron-app 根目录（即 package.json 所在目录）

interface EntryUser {
  user_id: string
  room_id: string
  nick_name: string
  douyin_level: number
  badge_level: number
  enter_count: number
  follower_count: number
}

export async function ParseEntryUsers(roomId: string): Promise<EntryUser[]> {
  console.log(rootDir)
  const entryDir = path.join(rootDir, `cache/entry_${roomId}`)

  let files: string[]
  try {
    files = fs.readdirSync(entryDir)
    console.log(`✅ 找到 ${files.length} 个 entry 文件`)
  } catch (err) {
    console.error(`❌ 无法读取目录: ${entryDir}`, err)
    return []
  }

  const userMap = new Map<string, EntryUser>()

  for (const file of files) {
    const filePath = path.join(entryDir, file)
    let json: any
    try {
      const content = fs.readFileSync(filePath, 'utf-8')
      json = JSON.parse(content)
    } catch (err) {
      console.warn(`⚠️ 无效文件，跳过: ${filePath}`, err)
      continue
    }

    const user = json.user
    const common = json.common

    if (!user?.id || !common?.roomId) continue

    const userId = user.id
    const nickName = user.nickName || ''
    const roomId = common.roomId
    const dyLevel = user.PayGrade?.level || 0
    const lightLevel = user.FansClub?.data?.level || 0
    const followingCount = user.FollowInfo?.followingCountStr || 0

    if (userMap.has(userId)) {
      userMap.get(userId)!.enter_count += 1
    } else {
      userMap.set(userId, {
        user_id: userId,
        room_id: roomId,
        nick_name: nickName,
        douyin_level: Number(dyLevel),
        badge_level: Number(lightLevel),
        enter_count: 1,
        follower_count: followingCount
      })
    }
  }

  const result = Array.from(userMap.values())
  console.log(`🎯 总共统计到 ${result.length} 个唯一用户`)
  return result
}

// 示例调用
if (require.main === module) {
  ParseEntryUsers('7525785145926322959').then((res) => {
    console.log('🔢 用户总数:', res.length)
    console.log(res.slice(0, 3)) // 打印前3条预览
  })
}
