# 风险分析统计接口优化总结

## 问题背景

在 EnterView 风险分析页面中，发现统计卡片与数据表格在筛选时存在不一致的问题：

- **现象**：用户筛选"高风险用户"时，表格只显示高风险用户，但统计卡片仍显示全部用户的统计信息
- **问题根源**：`getRoomEnterStatistics` 接口不支持筛选条件，只能获取全局统计
- **用户体验**：筛选后的统计卡片与表格数据不匹配，造成困惑

## 优化方案

### 1. 后端接口改造

**文件**：`src/main/Service/business/EnterService.ts`

#### 1.1 扩展 `getRoomEnterStatistics` 方法

```typescript
// 优化前
export async function getRoomEnterStatistics(roomId: string): Promise<{...}>

// 优化后
export async function getRoomEnterStatistics(
  roomId: string,
  filters?: {
    douyin_level?: { min?: number; max?: number; exact?: number };
    badge_level?: { min?: number; max?: number; exact?: number };
    enter_count?: { min?: number; max?: number; exact?: number };
    has_profile_video?: boolean;
    has_showcase?: boolean;
    follower_count?: { min?: number; max?: number; exact?: number };
  }
): Promise<{...}>
```

#### 1.2 筛选条件逻辑复用

将 `getEnterRecordsByRoom` 中的筛选条件构建逻辑复用到统计方法中：

- **抖音等级筛选**：支持精确值、最小值、最大值
- **粉丝团等级筛选**：支持精确值、最小值、最大值
- **进入次数筛选**：支持精确值、最小值、最大值
- **头像视频筛选**：布尔值筛选
- **展示柜筛选**：布尔值筛选
- **粉丝数筛选**：支持精确值、最小值、最大值

#### 1.3 数据库查询优化

使用 `baseWhere` 条件对象，统一应用到所有统计查询：

```typescript
// 构建统一的 where 条件
const baseWhere: any = { room_id: roomId }

// 应用筛选条件...

// 并发执行所有统计查询
const [totalUsers, totalEnters, avgEnterCount, usersWithProfileVideo, usersWithShowcase] =
  await Promise.all([
    EnterModel.count({ where: baseWhere }),
    EnterModel.sum('enter_count', { where: baseWhere }),
    EnterModel.findOne({ where: baseWhere, attributes: [['AVG(enter_count)', 'avg']] }),
    EnterModel.count({ where: { ...baseWhere, has_profile_video: true } }),
    EnterModel.count({ where: { ...baseWhere, has_showcase: true } })
  ])
```

### 2. IPC 接口更新

**文件**：`src/main/Service/EnterService.ts`

#### 2.1 IPC 处理器更新

```typescript
// 获取房间进入统计信息（支持筛选条件）
ipcMain.handle('enter:getRoomStatistics', async (_, roomId, filters) => {
  return await getRoomEnterStatistics(roomId, filters)
})
```

#### 2.2 便捷方法更新

```typescript
public static async getRoomStatistics(roomId: string, filters?: any): Promise<any> {
  return await getRoomEnterStatistics(roomId, filters);
}
```

### 3. 前端调用优化

**文件**：`src/renderer/src/views/enter/EnterView.vue`

#### 3.1 统计数据获取优化

```typescript
// 获取统计信息（应用当前筛选条件）
const filterConditions = getFilterConditions()
const stats = await window.electron.ipcRenderer.invoke(
  'enter:getRoomStatistics',
  roomId.value,
  filterConditions
)
```

#### 3.2 数据流优化

```
用户筛选操作 →
同时更新：
├── 表格数据（getEnterRecordsByRoom + 筛选条件）
└── 统计数据（getRoomEnterStatistics + 相同筛选条件）
```

## 技术特性

### 1. 筛选条件一致性

- **接口统一**：统计接口与数据查询接口使用相同的筛选参数结构
- **逻辑复用**：筛选条件构建逻辑完全复用，避免不一致
- **类型安全**：TypeScript 类型定义确保参数一致性

### 2. 性能优化

- **并发查询**：使用 `Promise.all` 并发执行所有统计查询
- **单次调用**：前端通过一次筛选条件构建，同时用于统计和数据查询
- **数据库优化**：基于索引的 WHERE 条件，高效查询

### 3. 向后兼容性

- **可选参数**：`filters` 参数为可选，不传入时保持原有全局统计行为
- **接口稳定**：返回数据结构完全不变
- **现有功能**：其他调用该接口的地方无需修改

## 实现效果

### 1. 用户体验提升

- **数据一致性**：筛选后，统计卡片与表格数据完全同步
- **实时反馈**：筛选条件变更时，统计数据立即更新
- **清晰展示**：用户能准确了解当前筛选结果的统计信息

### 2. 功能场景

#### 2.1 全局统计（无筛选）

```
总用户数: 1000
总进入次数: 5000
平均进入次数: 5.0
高风险用户: 120
```

#### 2.2 高风险用户筛选

```
总用户数: 120       (仅高风险用户)
总进入次数: 2400    (高风险用户总进入次数)
平均进入次数: 20.0  (高风险用户平均进入次数)
高风险用户: 120     (与总用户数一致)
```

#### 2.3 抖音等级筛选

```
总用户数: 200       (指定等级范围用户)
总进入次数: 800     (该等级用户总进入次数)
平均进入次数: 4.0   (该等级用户平均进入次数)
高风险用户: 15      (该等级中的高风险用户)
```

## 开发规范遵循

- **代码复用**：复用现有筛选逻辑，保持代码一致性
- **类型安全**：完整的 TypeScript 类型定义
- **错误处理**：完善的异常捕获和默认值处理
- **性能优先**：并发查询优化，减少数据库压力
- **文档完整**：代码注释和接口文档完善

## 总结

通过本次优化，风险分析页面实现了统计数据与表格数据的完全同步，用户在使用筛选功能时能获得一致、准确的数据展示。同时保持了良好的性能和向后兼容性，为后续功能扩展奠定了坚实基础。

优化核心价值：

- **用户体验**：数据一致性显著提升
- **代码质量**：逻辑复用，减少重复代码
- **可维护性**：统一的筛选逻辑便于维护
- **可扩展性**：为后续新增筛选条件预留接口
