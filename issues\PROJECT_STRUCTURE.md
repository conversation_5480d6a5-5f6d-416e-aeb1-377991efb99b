# 项目结构重构说明

本文档记录了renderer端的目录重构过程和最终结构。

## 重构目标

1. 将业务组件按功能模块组织，避免组件散落
2. 建立清晰的文件层次结构
3. 组件与相关hooks就近放置
4. 删除未使用的冗余文件

## 最终目录结构

```
src/renderer/src/
├── layouts/                    # 布局组件
│   ├── MainLayout.vue         # 主布局（左侧菜单+右侧内容）
│   └── components/            # 布局相关组件
│       ├── Sidebar.vue        # 左侧菜单栏
│       ├── Header.vue         # 顶部导航
│       └── Footer.vue         # 底部状态栏
│
├── views/                     # 页面视图组件
│   ├── dashboard/             # 仪表板页面 ✨
│   │   ├── DashboardView.vue  # 主页面组件
│   │   ├── components/        # dashboard专用组件
│   │   │   ├── DashboardMetrics.vue  # 数据概览指标
│   │   │   └── DashboardTable.vue    # 直播间列表表格
│   │   └── composables/       # dashboard专用hooks（如需要）
│   │
│   ├── detail/                # 详情分析页面
│   │   └── DetailView.vue     # 直播详情分析
│   │
│   ├── data/                  # 数据页面
│   │   └── DataView.vue       # 数据总览
│   │
│   ├── report/                # 报告页面
│   │   └── ReportView.vue     # 报告分析
│   │
│   ├── suggestion/            # 建议页面
│   │   └── SuggestionView.vue # 整改建议
│   │
│   └── account/               # 账户页面
│       └── AccountDataView.vue # 账户数据
│
├── components/                # 共享业务组件
│   ├── common/               # 通用组件
│   ├── layout/               # 布局相关组件
│   ├── traffic/              # 流量相关组件
│   ├── crowd/                # 人群分析组件
│   ├── trends/               # 趋势分析组件
│   ├── live/                 # 直播数据组件
│   ├── linkAge/              # 时间轴组件
│   ├── flowrate/             # 流量等级组件
│   ├── canvasFunnel/         # 漏斗图组件
│   └── reportpomponents/     # 报告组件
│
├── composables/              # 共享hooks
│   ├── useTrafficData.ts     # 流量数据处理
│   ├── useRoomDataProcessor.ts # 房间数据处理
│   ├── useChartProcessor.ts  # 图表处理
│   ├── useTrendAnalysis.ts   # 趋势分析
│   ├── useLiveData.ts        # 直播数据
│   ├── useRealTimeMetrics.ts # 实时指标
│   ├── useCustomMetrics.ts   # 自定义指标
│   ├── useCoreDataRenderer.ts # 核心数据渲染
│   └── usePagination.ts      # 分页
│
└── utils/                    # 工具函数
    ├── formatUtils.ts        # 格式化工具
    └── ...
```

## 重构亮点 ✨

### 1. 页面级组件管理

- **dashboard**: 将原来的Home.vue拆分重构，创建专门的dashboard模块
- **components**: dashboard专用组件DashboardMetrics、DashboardTable
- **功能分离**: 登录、监控功能简化，专注于数据展示

### 2. 布局架构优化

- **MainLayout**: 统一的左右分栏布局
- **响应式设计**: 支持侧边栏收缩，适配不同屏幕
- **路由嵌套**: 首页使用MainLayout，详情页独立布局

### 3. 组件就近原则

- 业务组件放在使用它们的页面目录下
- 共享组件保留在全局components目录
- hooks按功能模块组织

### 4. 文件清理

- ✅ 删除未使用的Home.vue (22KB)
- ✅ 删除components/home目录
- ✅ 删除重复的Detail.vue、Data.vue
- ✅ 删除components/AccountMetrics.vue

## 布局模式

### MainLayout模式（有左侧菜单）

```
┌─────────────────────────────────┐
│ ┌─────┐ ┌─────────────────────┐ │
│ │     │ │     Header          │ │
│ │Side │ ├─────────────────────┤ │
│ │bar  │ │                     │ │
│ │     │ │     Content         │ │
│ │     │ │                     │ │
│ │     │ ├─────────────────────┤ │
│ │     │ │     Footer          │ │
│ └─────┘ └─────────────────────┘ │
└─────────────────────────────────┘
```

**适用页面**: 首页 (/)、账户数据 (/account-data)

### 独立布局模式（全屏）

```
┌─────────────────────────────────┐
│ ┌─────────────────────────────┐ │
│ │         Menu Bar            │ │
│ ├─────────────────────────────┤ │
│ │                             │ │
│ │         Content             │ │
│ │                             │ │
│ └─────────────────────────────┘ │
└─────────────────────────────────┘
```

**适用页面**: detail、data、report、suggestion

## 技术改进

1. **TypeScript支持**: 所有新组件都有完整的类型定义
2. **组合式API**: 使用setup语法，代码更简洁
3. **样式隔离**: 使用scoped样式，避免污染
4. **响应式设计**: 支持移动端适配
5. **暗色主题**: 支持Bootstrap的dark模式

## 下一步优化

1. 继续迁移Report、Suggestion、AccountData组件内容
2. 优化共享组件的props接口
3. 建立统一的样式变量系统
4. 添加组件测试
