# 商品分析页面添加

## 任务描述

创建独立的商品分析主页，作为与直播分析并列的第二个主要模块，显示全局商品数据和分析。

## 项目架构理解

项目有两个主要模块：

1. **直播分析** - 包含Dashboard、AccountData、data/:id、detail/:id、report/:id等页面
2. **商品分析** - 新创建的独立主页模块

## 实施步骤

### 1. 创建商品分析主页

- ✅ 创建 `src/renderer/src/views/product/ProductAnalysisView.vue`
- ✅ 创建 `src/renderer/src/views/product/components/ProductMetrics.vue` (数据概览)
- ✅ 创建 `src/renderer/src/views/product/components/ProductTable.vue` (商品列表)
- ✅ 使用MainLayout布局（有侧边栏），类似DashboardView结构

### 2. 更新路由配置

- ✅ 删除错误的 `/product-analysis/:id` 路由
- ✅ 添加 `/product-analysis` 路由到MainLayout的children中
- ✅ 路由名称：ProductAnalysis

### 3. 更新侧边栏导航

- ✅ 将Sidebar中的"即将上线"改为可点击链接
- ✅ 设置路由链接：`/product-analysis`
- ✅ 移除"即将上线"标签

### 4. 清理错误配置

- ✅ 从Menu.vue中移除商品分析导航项（因为那是房间内部导航）

## 技术实现

### 路由配置

```javascript
{
  path: '/',
  component: MainLayout,
  children: [
    { path: '', name: 'Dashboard', component: DashboardView },
    { path: 'product-analysis', name: 'ProductAnalysis', component: ProductAnalysisView }
  ]
}
```

### 侧边栏配置

```javascript
<router-link to="/product-analysis" class="nav-link">
  <i class="bi bi-bag"></i>
  <span>商品分析</span>
</router-link>
```

## 页面功能

### 商品数据概览 (ProductMetrics)

- 8个核心商品指标卡片
- 时间周期选择器（15天/30天）
- 当前周期与上周期对比
- 模拟数据展示

### 商品列表 (ProductTable)

- 完整的商品数据表格
- 搜索和筛选功能（按分类、状态）
- 导出Excel功能
- 分页和排序
- 模拟商品数据

## 后续扩展计划

1. 接入真实商品数据API
2. 添加商品趋势图表
3. 商品详情页面
4. 商品分析报告
5. 商品推荐功能

## 测试验证

- ✅ 侧边栏商品分析链接可正常点击
- ✅ 页面使用MainLayout布局
- ✅ 商品概览数据正常显示
- ✅ 商品列表表格功能正常
- ✅ 搜索、筛选、导出功能正常

## 完成状态

✅ 已完成 - 商品分析主页已创建，功能完整，架构正确
