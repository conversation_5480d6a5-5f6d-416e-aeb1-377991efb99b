"use strict";(()=>{var L=Object.create;var v=Object.defineProperty;var R=Object.getOwnPropertyDescriptor;var I=Object.getOwnPropertyNames;var M=Object.getPrototypeOf,P=Object.prototype.hasOwnProperty;var T=(n,e)=>()=>(n&&(e=n(n=0)),e);var D=(n,e)=>()=>(e||n((e={exports:{}}).exports,e),e.exports);var U=(n,e,s,a)=>{if(e&&typeof e=="object"||typeof e=="function")for(let u of I(e))!P.call(n,u)&&u!==s&&v(n,u,{get:()=>e[u],enumerable:!(a=R(e,u))||a.enumerable});return n};var k=(n,e,s)=>(s=n!=null?L(M(n)):{},U(e||!n||!n.__esModule?v(s,"default",{value:n,enumerable:!0}):s,n));var y,d=T(()=>{y={TERM_PROGRAM:"iTerm.app",NODE:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.17.0/installation/bin/node",INIT_CWD:"/Users/<USER>/g/devtools-next/packages/chrome-extension",TERM:"xterm-256color",SHELL:"/bin/zsh",npm_config_shamefully_hoist:"true",npm_config_registry:"https://registry.npmjs.org/",USER:"arlo",PNPM_SCRIPT_SRC_DIR:"/Users/<USER>/g/devtools-next/packages/chrome-extension",npm_config_strict_peer_dependencies:"",__CF_USER_TEXT_ENCODING:"0x1F5:0x19:0x34",npm_execpath:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/bin/pnpm.cjs",npm_config_frozen_lockfile:"",npm_config_verify_deps_before_run:"false",npm_config_catalog:'{"@iconify/json":"^2.2.321","@types/node":"^22.13.14","@unocss/reset":"^66.0.0","@vitejs/plugin-vue":"^5.2.3","@vueuse/core":"^12.8.2","@vueuse/integrations":"^12.8.2","colord":"^2.9.3","execa":"^9.5.2","floating-vue":"5.2.2","mitt":"^3.0.1","pathe":"^2.0.3","perfect-debounce":"^1.0.0","pinia":"^3.0.1","sass-embedded":"^1.86.0","serve":"^14.2.4","shiki":"^3.2.1","splitpanes":"^4.0.3","typescript":"^5.8.2","unocss":"^66.0.0","unplugin-auto-import":"^19.1.2","vite":"^6.2.1","vite-hot-client":"^2.0.4","vite-plugin-dts":"^4.5.3","vite-plugin-inspect":"0.8.9","vue":"^3.5.13","vue-router":"^4.5.0","vue-virtual-scroller":"2.0.0-beta.8"}',PATH:"/Users/<USER>/g/devtools-next/packages/chrome-extension/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/.cache/node/corepack/v1/pnpm/10.7.0/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/.local/bin:/Users/<USER>/Library/pnpm:/Users/<USER>/.local/state/fnm_multishells/24509_1745808020460/bin:/opt/homebrew/bin:/Users/<USER>/.cargo/bin:/libexec/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Applications/WebStorm.app/Contents/MacOS:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin",PWD:"/Users/<USER>/g/devtools-next/packages/chrome-extension",npm_command:"run-script",npm_lifecycle_event:"build",LANG:"zh_CN.UTF-8",npm_package_name:"@vue/devtools-chrome-extension",NODE_PATH:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/tsup@8.4.0_@microsoft+api-extractor@7.51.1_@types+node@22.13.14__jiti@2.4.2_postcss@8.5_96eb05a9d65343021e53791dd83f3773/node_modules/tsup/dist/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/tsup@8.4.0_@microsoft+api-extractor@7.51.1_@types+node@22.13.14__jiti@2.4.2_postcss@8.5_96eb05a9d65343021e53791dd83f3773/node_modules/tsup/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/tsup@8.4.0_@microsoft+api-extractor@7.51.1_@types+node@22.13.14__jiti@2.4.2_postcss@8.5_96eb05a9d65343021e53791dd83f3773/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/src/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/src/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/cross-env@7.0.3/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules",TURBO_HASH:"e065a3f9c2286634",NODE_ENV:"production",npm_config_side_effects_cache:"",npm_config_node_gyp:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node_modules/node-gyp/bin/node-gyp.js",npm_package_version:"7.7.3",SHLVL:"0",HOME:"/Users/<USER>",npm_lifecycle_script:"cross-env NODE_ENV=production tsup",npm_config_user_agent:"pnpm/10.7.0 npm/? node/v20.17.0 darwin arm64",COLORTERM:"truecolor",npm_config_shell_emulator:"true",npm_node_execpath:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.17.0/installation/bin/node"}});var O=D((Q,w)=>{"use strict";d();w.exports=V;function h(n){return n instanceof Buffer?Buffer.from(n):new n.constructor(n.buffer.slice(),n.byteOffset,n.length)}function V(n){if(n=n||{},n.circles)return B(n);let e=new Map;if(e.set(Date,o=>new Date(o)),e.set(Map,(o,c)=>new Map(a(Array.from(o),c))),e.set(Set,(o,c)=>new Set(a(Array.from(o),c))),n.constructorHandlers)for(let o of n.constructorHandlers)e.set(o[0],o[1]);let s=null;return n.proto?p:u;function a(o,c){let t=Object.keys(o),r=new Array(t.length);for(let f=0;f<t.length;f++){let i=t[f],l=o[i];typeof l!="object"||l===null?r[i]=l:l.constructor!==Object&&(s=e.get(l.constructor))?r[i]=s(l,c):ArrayBuffer.isView(l)?r[i]=h(l):r[i]=c(l)}return r}function u(o){if(typeof o!="object"||o===null)return o;if(Array.isArray(o))return a(o,u);if(o.constructor!==Object&&(s=e.get(o.constructor)))return s(o,u);let c={};for(let t in o){if(Object.hasOwnProperty.call(o,t)===!1)continue;let r=o[t];typeof r!="object"||r===null?c[t]=r:r.constructor!==Object&&(s=e.get(r.constructor))?c[t]=s(r,u):ArrayBuffer.isView(r)?c[t]=h(r):c[t]=u(r)}return c}function p(o){if(typeof o!="object"||o===null)return o;if(Array.isArray(o))return a(o,p);if(o.constructor!==Object&&(s=e.get(o.constructor)))return s(o,p);let c={};for(let t in o){let r=o[t];typeof r!="object"||r===null?c[t]=r:r.constructor!==Object&&(s=e.get(r.constructor))?c[t]=s(r,p):ArrayBuffer.isView(r)?c[t]=h(r):c[t]=p(r)}return c}}function B(n){let e=[],s=[],a=new Map;if(a.set(Date,t=>new Date(t)),a.set(Map,(t,r)=>new Map(p(Array.from(t),r))),a.set(Set,(t,r)=>new Set(p(Array.from(t),r))),n.constructorHandlers)for(let t of n.constructorHandlers)a.set(t[0],t[1]);let u=null;return n.proto?c:o;function p(t,r){let f=Object.keys(t),i=new Array(f.length);for(let l=0;l<f.length;l++){let b=f[l],g=t[b];if(typeof g!="object"||g===null)i[b]=g;else if(g.constructor!==Object&&(u=a.get(g.constructor)))i[b]=u(g,r);else if(ArrayBuffer.isView(g))i[b]=h(g);else{let x=e.indexOf(g);x!==-1?i[b]=s[x]:i[b]=r(g)}}return i}function o(t){if(typeof t!="object"||t===null)return t;if(Array.isArray(t))return p(t,o);if(t.constructor!==Object&&(u=a.get(t.constructor)))return u(t,o);let r={};e.push(t),s.push(r);for(let f in t){if(Object.hasOwnProperty.call(t,f)===!1)continue;let i=t[f];if(typeof i!="object"||i===null)r[f]=i;else if(i.constructor!==Object&&(u=a.get(i.constructor)))r[f]=u(i,o);else if(ArrayBuffer.isView(i))r[f]=h(i);else{let l=e.indexOf(i);l!==-1?r[f]=s[l]:r[f]=o(i)}}return e.pop(),s.pop(),r}function c(t){if(typeof t!="object"||t===null)return t;if(Array.isArray(t))return p(t,c);if(t.constructor!==Object&&(u=a.get(t.constructor)))return u(t,c);let r={};e.push(t),s.push(r);for(let f in t){let i=t[f];if(typeof i!="object"||i===null)r[f]=i;else if(i.constructor!==Object&&(u=a.get(i.constructor)))r[f]=u(i,c);else if(ArrayBuffer.isView(i))r[f]=h(i);else{let l=e.indexOf(i);l!==-1?r[f]=s[l]:r[f]=c(i)}}return e.pop(),s.pop(),r}}});d();d();d();d();var N=typeof navigator!="undefined",A=typeof window!="undefined"?window:typeof globalThis!="undefined"?globalThis:typeof global!="undefined"?global:{},W=typeof A.chrome!="undefined"&&!!A.chrome.devtools,Y=N&&A.self!==A.top,_,F=typeof navigator!="undefined"&&((_=navigator.userAgent)==null?void 0:_.toLowerCase().includes("electron")),X=typeof window!="undefined"&&!!window.__NUXT__;d();var j=k(O(),1);var E=n=>`${+n}`===n;var ee=(0,j.default)({circles:!0});var m={};function C(n){var u;let{tab:e,name:s,port:a}=n;return(u=m[e])!=null||(m[e]={devtools:null,userApp:null}),m[e][s]=a,m[e]}function $(n){let{devtools:e,userApp:s}=m[n],a=!1;function u(c){a||(y.NODE_ENV==="development"&&console.log("%cdevtools -> userApp","color:#888;",c),s.postMessage(c))}e.onMessage.addListener(u);function p(c){a||(y.NODE_ENV==="development"&&console.log("%cuserApp -> devtools","color:#888;",c),e.postMessage(c))}s.onMessage.addListener(p);function o(){if(a=!0,!m[n])return;let{devtools:c,userApp:t}=m[n];c.onMessage.removeListener(u),t.onMessage.removeListener(p),c==null||c.disconnect(),t==null||t.disconnect(),delete m[n]}e.onDisconnect.addListener(o),s.onDisconnect.addListener(o)}chrome.runtime.onConnect.addListener(n=>{let e={tab:"",name:"",port:n};E(n.name)?(e.tab=n.name,e.name="devtools",chrome.scripting.executeScript({target:{tabId:+n.name},files:["/dist/proxy.js"]},a=>{})):(e.tab=n.sender.tab.id,e.name="userApp");let s=C(e);s.devtools&&s.userApp&&$(e.tab)});chrome.runtime.onMessage.addListener((n,e)=>{if(e.tab&&n.vue2Detected&&(chrome.action.setPopup({tabId:e.tab.id,popup:chrome.runtime.getURL("popups/vue2-migration-guide.html")}),chrome.action.setIcon({tabId:e.tab.id,path:{16:chrome.runtime.getURL("icons/16.png"),48:chrome.runtime.getURL("icons/48.png"),128:chrome.runtime.getURL("icons/128.png")}})),e.tab&&n.vueDetected){let s="";n.nuxtDetected?s=".nuxt":n.vitePressDetected&&(s=".vitepress"),chrome.action.setIcon({tabId:e.tab.id,path:{16:chrome.runtime.getURL(`icons/16${s}.png`),48:chrome.runtime.getURL(`icons/48${s}.png`),128:chrome.runtime.getURL(`icons/128${s}.png`)}}),chrome.action.setPopup({tabId:e.tab.id,popup:n.devtoolsEnabled?chrome.runtime.getURL(`popups/enabled${s}.html`):chrome.runtime.getURL(`popups/disabled${s}.html`)})}});})();
