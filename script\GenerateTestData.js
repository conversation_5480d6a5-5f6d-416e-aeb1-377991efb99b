/* eslint-disable @typescript-eslint/explicit-function-return-type */
import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const roomId = '1234'

const commentsDir = path.join(__dirname, `../cache/comments_${roomId}`)
const wcommentsDir = path.join(__dirname, `../cache/wcomments_${roomId}`)
console.time('总耗时')
// 生成随机时间戳字符串，保证文件名唯一且格式类似真实数据
function randomTimestamp() {
  // 时间戳范围大致在 1750000000000 ~ 1755000000000 (未来某段时间，13位毫秒级)
  // 转成字符串直接用作文件名
  const base = 1750000000000
  return (base + Math.floor(Math.random() * 5000000000)).toString()
}

// 固定昵称和内容模板，生成唯一且对应的 key
function generateNickName(index) {
  return `陈琼助理晴晴_${index}`
}

function generateContent(index) {
  return `姐妹们，跟练的时候沉肩，立颈，坐直，脖子不要前倾哦[爱心] #${index}`
}

function generateCommentFileData(nickName, content) {
  return {
    list: {
      comments: [
        {
          nick_name: nickName,
          msg_id: '', // 这里留空
          content,
          comment_tag: null
        }
      ],
      cursor: `r-7524685245489092786_d-1_u-7524685236899152216_fh-1_t-${Date.now()}`,
      internal_ext: 'internal_src:dim|seq:0',
      fetch_interval: 1000
    }
  }
}

function generateWCommentFileData(nickName, content) {
  return {
    common: {
      method: 'WebcastChatMessage',
      msgId: '', // 可留空
      roomId,
      isShowMsg: true,
      priorityScore: '31058'
    },
    user: {
      id: '7440718725651350588',
      shortId: '39341534361',
      nickName,
      AvatarThumb: {
        urlListList: [
          'https://p3.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-i-0813_ocblgnmHAfaZAIffeAQG2Yu4PIIHg444gfAeYA.jpeg?from=**********'
        ]
      },
      BadgeImageList: [
        {
          urlListList: [
            'https://p3-webcast.douyinpic.com/img/webcast/webcast_admin_badge.png~tplv-obj.image'
          ],
          height: '16',
          width: '16',
          imageType: 3,
          content: { alternativeText: '房管勋章' }
        },
        {
          urlListList: [
            'https://p3-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_22.png~tplv-obj.image'
          ],
          height: '16',
          width: '32',
          imageType: 1,
          content: { level: '22', alternativeText: '荣誉等级22级勋章' }
        },
        {
          urlListList: [
            'https://p3-webcast.douyinpic.com/img/webcast/fansclub_level_v6_11.png~tplv-obj.image'
          ],
          imageType: 7,
          content: {
            fontColor: '#FFFFFF',
            level: '11',
            alternativeText: '粉丝团等级11级勋章'
          }
        }
      ],
      FollowInfo: {
        followingCount: '321',
        followerCount: '330',
        followStatus: '2',
        followerCountStr: '0',
        followingCountStr: '0'
      },
      PayGrade: {
        level: '22',
        newImIconWithLevel: {
          urlListList: [
            'https://p3-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_22.png~tplv-obj.image'
          ],
          height: '16',
          width: '32',
          imageType: 1
        },
        newLiveIcon: {
          urlListList: [
            'https://p11-webcast.douyinpic.com/img/webcast/aweme_pay_grade_2x_20_24.png~tplv-obj.image'
          ],
          height: '12',
          width: '12',
          imageType: 1
        }
      },
      FansClub: {
        data: {
          level: 11,
          userFansClubStatus: 1,
          badge: {
            icons: {
              2: {
                urlListList: [
                  'https://p3-webcast.douyinpic.com/img/webcast/fansclub_level_v6_11.png~tplv-obj.image',
                  'https://p11-webcast.douyinpic.com/img/webcast/fansclub_level_v6_11.png~tplv-obj.image'
                ],
                uri: 'webcast/fansclub_level_v6_11.png'
              },
              4: {
                urlListList: [
                  'https://p3-webcast.douyinpic.com/img/webcast/fansclub_new_advanced_badge_11_xmp.png~tplv-obj.image',
                  'https://p11-webcast.douyinpic.com/img/webcast/fansclub_new_advanced_badge_11_xmp.png~tplv-obj.image'
                ],
                uri: 'webcast/fansclub_new_advanced_badge_11_xmp.png'
              }
            }
          },
          anchorId: '1478215694024980'
        }
      },
      displayId: '39341534361',
      secUid: 'MS4wLjABAAAA7IC49u1ffGLj1jJaYb4LHf5HW--XY4Waqd7f2edUUJTRICtgm2HLiGFA0dyDbFJL'
    },
    content,
    publicAreaCommon: {
      userLabel: {
        urlListList: [
          'https://p3-webcast.douyinpic.com/img/webcast/userlabel_regular_chat.png~tplv-obj.image'
        ],
        avgColor: '#3D3D3D'
      },
      userConsumeInRoom: '23',
      userSendGiftCntInRoom: '1'
    },
    eventTime: `${Math.floor(Date.now() / 1000)}`
  }
}

async function generateLargeData() {
  await fs.mkdir(commentsDir, { recursive: true })
  await fs.mkdir(wcommentsDir, { recursive: true })

  const totalFiles = 100000

  console.log(`开始生成${totalFiles}个comments和wcomments文件，过程可能较慢...`)

  for (let i = 0; i < totalFiles; i++) {
    const nickName = generateNickName(i)
    const content = generateContent(i)

    const commentData = generateCommentFileData(nickName, content)
    const wcommentData = generateWCommentFileData(nickName, content)

    // 文件名随机时间戳（可能重复概率极低）
    const fileName = randomTimestamp()

    // 写入 comments 文件
    await fs.writeFile(
      path.join(commentsDir, fileName + '.json'),
      JSON.stringify(commentData, null, 2),
      'utf-8'
    )

    // 写入 wcomments 文件
    await fs.writeFile(
      path.join(wcommentsDir, fileName + '.json'),
      JSON.stringify(wcommentData, null, 2),
      'utf-8'
    )

    if ((i + 1) % 1000 === 0) {
      console.log(`已生成 ${i + 1} / ${totalFiles} 个文件`)
    }
  }

  console.log('✅ 3万个文件生成完成！')
  console.timeEnd('总耗时')
}

generateLargeData().catch((err) => {
  console.error('生成出错:', err)
})
