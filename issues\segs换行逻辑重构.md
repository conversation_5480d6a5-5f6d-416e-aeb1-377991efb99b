# segs换行逻辑重构

## 问题描述

LinkAge.vue中的segs换行逻辑过于复杂且存在逻辑错误：

1. 时间计算逻辑复杂，使用了错误的`setSeconds(59, 999)`
2. HTML生成复杂，难以维护
3. 没有正确处理单个seg内部跨分钟的情况
4. 直接修改props违反Vue规范
5. **新发现问题**：点击seg时会同时选中时间分割线

## 解决方案

### 1. 重构换行判断逻辑

- 简化`shouldAddLineBreak`函数，返回结构化数据
- 同时处理seg内部跨分钟和seg间跨分钟两种情况
- 使用简洁的分钟边界计算

### 2. 简化HTML生成

- 使用CSS类替代复杂内联样式
- 简化时间分割线HTML结构

### 3. 修复props修改问题

- 添加`updateSegs`事件发射器
- 通过emit通知父组件更新数据

### 4. 分离文本和分割线（新增）

- 重构`processedSegs`数据结构，返回包含type字段的数组
- seg文本和时间分割线分成独立的元素
- 只对seg文本绑定点击事件，分割线不可点击

## 新的数据结构

```javascript
// 重构后的processedSegs返回格式
;[
  { ...seg, processedText: '文本内容', type: 'seg' },
  { id: 'divider-xxx', timeDisplay: '19:31:00', type: 'divider', timestamp: xxx }
]
```

## 模板结构改进

```vue
<template v-for="item in processedSegs" :key="item.id || item.start">
  <!-- 字幕文本 - 可点击 -->
  <span v-if="item.type === 'seg'" @click="WordsGoToClick(item)">
    {{ item.processedText }}
  </span>

  <!-- 时间分割线 - 不可点击 -->
  <div v-else-if="item.type === 'divider'" class="time-divider">
    ────── {{ item.timeDisplay }} ──────
  </div>
</template>
```

## 需要父组件配合的修改

父组件需要监听`updateSegs`事件并更新roomData.segs：

```vue
<LinkAge
  :room-data="roomData"
  :began-time="beganTime"
  :roomid="roomid"
  @update-segs="(segs) => (roomData.segs = segs)"
/>
```

## 重构后的核心逻辑

```javascript
// 检查是否需要换行
function shouldAddLineBreak(currentSeg, nextSeg, liveStartTime) {
  // 1. 检查seg内部跨分钟
  // 2. 检查seg间跨分钟
  // 3. 返回结构化结果
}

// 新的processedSegs逻辑
const processedSegs = computed(() => {
  const result = []
  // 遍历segs，为每个seg和分割线创建独立项
  // seg类型：{ type: 'seg', ...segData }
  // 分割线类型：{ type: 'divider', timeDisplay, id }
  return result
})
```

## 样式改进

添加了`.time-divider`类，统一时间分割线样式。

## 解决的问题

✅ 时间计算错误
✅ HTML生成复杂
✅ seg内部跨分钟处理
✅ props直接修改
✅ 点击时选中分割线问题
