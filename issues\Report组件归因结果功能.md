# Report组件归因结果功能

## 任务描述

为Report.vue组件的归因结果行添加智能显示逻辑，当指标表现不佳时显示"查看归因结果"蓝色链接。

## 需求分析

- 归因结果行目前显示"暂无数据"
- 新逻辑：当 `本场值 < 上一场值` 或 `本场值 < 设置的基准值` 时，显示蓝色链接
- 链接文本："查看归因结果"
- 支持tooltip悬浮提示（先用TODO占位）

## 实现方案

### 1. 创建基准值获取函数

```javascript
const getBaselineConfig = (): Record<string, number> => {
  try {
    const stored = localStorage.getItem('baselineConfig')
    return stored ? JSON.parse(stored) : {}
  } catch {
    return {}
  }
}
```

### 2. 创建归因判断函数

```javascript
const shouldShowAttributionLink = (fieldName: string, currentValue: any, previousValue: any): boolean => {
  // 判断条件：本场值 < 上一场值 或者 本场值 < 基准值
  const isLowerThanPrevious = previousNum !== null && currentNum < previousNum
  const isLowerThanBaseline = baselineNum !== undefined && currentNum < baselineNum
  return isLowerThanPrevious || isLowerThanBaseline
}
```

### 3. 创建TODO版本的tooltip映射

```javascript
const attributionTooltipMap: Record<string, string> = {
  live_show_watch_ucnt_ratio: 'TODO: 曝光-观看率归因分析',
  watch_interact_ucnt_ratio: 'TODO: 观看互动率归因分析',
  // ... 其他字段
}
```

### 4. 修改数据生成逻辑

在 `extractionch` 函数中更新归因结果（empty1）的生成：

```javascript
// 归因结果：根据条件判断是否显示链接
if (shouldShowAttributionLink(key, current[key], previous[key])) {
  const tooltipText = attributionTooltipMap[key] || 'TODO: 归因分析'
  empty1[key] = {
    type: 'attribution-link',
    text: '查看归因结果',
    tooltip: tooltipText
  }
} else {
  empty1[key] = '暂无数据'
}
```

### 5. 创建专用formatter

```javascript
const attributionFormatter: VxeColumnPropTypes.Formatter = ({ cellValue }) => {
  if (cellValue && typeof cellValue === 'object' && cellValue.type === 'attribution-link') {
    return `<a href="javascript:void(0)" class="text-primary" title="${cellValue.tooltip}">${cellValue.text}</a>`
  }
  return `${render(cellValue)}`
}
```

## 支持的字段

- **直播内容板块**：

  - live_show_watch_ucnt_ratio (曝光-观看率)
  - watch_interact_ucnt_ratio (观看互动率)
  - watch_follow_ucnt_ratio (观看-关注率)
  - watch_fans_club_join_ucnt_ratio (观看-加直播团率)

- **直播带货板块**：
  - live_show_watch_ucnt_ratio (曝光-观看率)
  - watch_product_visibility (观看-商品曝光率)
  - product_show_click_ucnt_ratio (商品曝光-点击率)
  - product_click_pay_ucnt_ratio (商品点击-成交率)

## 功能特性

1. **智能判断**：自动比较本场与上场数据、基准值
2. **条件显示**：只在指标下降时显示归因链接
3. **样式美观**：蓝色链接，符合设计规范
4. **扩展性**：TODO结构便于后续添加具体归因内容
5. **基准值集成**：与已有的基准值配置功能完美结合

## 技术实现

- 百分比字符串解析为数值比较
- localStorage基准值读取
- VXE表格自定义formatter
- HTML链接渲染和tooltip支持

## 文件修改

- `src/renderer/src/Report.vue` - 添加归因结果判断逻辑和UI支持

## 问题修复

### 特殊字段归因判断问题

**问题**: 特殊处理的字段（`live_show_watch_ucnt_ratio`、`watch_product_visibility`）在 `extractionch` 函数中使用了 `continue` 语句，跳过了归因结果判断逻辑。

**修复**: 移除特殊字段处理后的 `continue` 语句，让所有字段都能使用归因判断逻辑。

**修改前**:

```javascript
// 特殊字段处理完后
empty1[key] = '暂无数据'
empty2[key] = '暂无数据'
continue // 这里跳过了后续的归因判断
```

**修改后**:

```javascript
// 特殊字段处理完后
// 不要直接 continue，让特殊字段也能使用归因判断逻辑
// 继续执行后面的归因判断逻辑
```

**结果**: 现在所有字段（包括计算字段）都能正确进行归因结果判断。

## 状态

✅ 已完成 - 归因结果功能已实现，支持智能链接显示和TODO版本tooltip
✅ 已修复 - 特殊字段归因判断问题已解决
