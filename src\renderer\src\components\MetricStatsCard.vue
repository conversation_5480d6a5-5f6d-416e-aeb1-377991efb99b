<script setup lang="ts">
/**
 * MetricStatsCard组件
 *
 * 功能描述:
 * 该组件是从AccountMetrics中抽离的指标卡片组件，用于展示指标的当前值和历史值的对比。
 *
 * 属性:
 * - title: 指标标题
 * - currentValue: 当前值
 * - previousValue: 历史值
 * - icon: 图标名称
 * - days: 时间跨度（天数）
 * - type: 指标类型（金额、数量等）
 */
import { computed } from 'vue'

const props = defineProps<{
  title: string
  currentValue: number
  previousValue: number
  days?: number
  type?: 'amount' | 'count' | 'percent'
}>()

/**
 * 金额格式化：分转元，保留两位小数
 */
const formatAmount = (amount: number): string => {
  return Math.floor(amount / 100).toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

/**
 * 格式化值根据类型
 */
const formatValue = (value: number): string => {
  if (props.type === 'amount') {
    return formatAmount(value)
  } else if (props.type === 'percent') {
    return value.toFixed(1) + '%'
  } else {
    return value.toString()
  }
}

/**
 * 增长率格式化：百分比+箭头
 */
const formatGrowthRate = (current: number, previous: number): string => {
  if (!previous || previous === 0) return '--'
  const rate = current / previous - 1
  const percent = Math.abs(rate * 100).toFixed(1) + '%'
  if (rate > 0) {
    return `同比增长${percent}`
  } else if (rate < 0) {
    return `同比下降${percent}`
  } else {
    return '持平'
  }
}

/**
 * 当前值
 */
const current = computed(() => formatValue(props.currentValue))

/**
 * 上一期值
 */
const previous = computed(() => formatValue(props.previousValue))

/**
 * 增长率
 */
const growthRate = computed(() => formatGrowthRate(props.currentValue, props.previousValue))
</script>

<template>
  <div class="relative p-4 dark:text-white">
    <p class="mb-2 font-bolod">近{{ days || 30 }}天{{ title }}</p>
    <div class="mb-1">
      <p class="current-card text-4xl line-height-[50px] mb-0 font-bold">{{ current }}</p>
      <p class="mt-2 mb-0">
        <span class="dark:text-[#9e88ff] text-gray-500">上一周期</span>
        {{ previous }}
      </p>
      <div
        class="dark:bg-[#6e4cff] bg-gray-500 p-2 absolute top-0 right-0 rounded-bl-4 rounded-tr-4 text-white text-[12px]"
      >
        <div v-if="previousValue > 0">
          {{ growthRate }}
        </div>
        <div v-else>无数据</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.first-card .current-card {
  font-size: 58px;
}
</style>
