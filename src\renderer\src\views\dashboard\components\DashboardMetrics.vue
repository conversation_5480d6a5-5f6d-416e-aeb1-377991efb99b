<script setup lang="ts">
/**
 * AccountMetrics组件
 *
 * 功能描述:
 * 该组件用于显示账户销售和观看相关的四个关键指标:
 * 1. 销售额 (totalPayAmt) - 累加所有直播间的pay_amt
 * 2. 场均观看 (avgWatchUcnt) - 累加所有直播间的watch_ucnt后除以直播场次
 * 3. 销量 (totalPayComboCnt) - 累加所有直播间的pay_combo_cnt
 * 4. 退款额 (totalRefundAmt) - 累加所有直播间的real_refund_amt
 *
 * 数据流:
 * 1. 组件内部通过IPC发送'/account/metrics'事件请求数据
 * 2. 后端LZYService处理请求，查询近30天和前30-60天数据
 * 3. 后端通过'/account/metrics/reply'事件回传数据
 * 4. 组件接收数据并渲染UI，显示当月和上月对比数据
 *
 * 组件是完全自包含的，不依赖外部传入props
 */
import { ref, onMounted, onUnmounted } from 'vue'
import MetricStatsCard from '@components/MetricStatsCard.vue'

// 当月数据 (近30天)
const currentMonthData = ref({
  totalPayAmt: 0, // 销售额（分）
  avgWatchUcnt: 0, // 场均观看
  totalPayComboCnt: 0, // 销量
  totalRefundAmt: 0 // 退款额（分）
})

// 上月数据 (30-60天前)
const previousMonthData = ref({
  totalPayAmt: 0, // 销售额（分）
  avgWatchUcnt: 0, // 场均观看
  totalPayComboCnt: 0, // 销量
  totalRefundAmt: 0 // 退款额（分）
})

// 当前选择的时间跨度（天数），默认30天
const currentSpan = ref(30)

// 指标定义
const metrics = [
  { id: 'totalPayAmt', title: '销售额', type: 'amount' as const },
  { id: 'avgWatchUcnt', title: '场均观看', type: 'count' as const },
  { id: 'totalPayComboCnt', title: '销量', type: 'count' as const },
  { id: 'totalRefundAmt', title: '退款额', type: 'amount' as const }
]

/**
 * 处理时间跨度变更
 */
const handleSpanChange = (days: number): void => {
  if (currentSpan.value === days) return
  currentSpan.value = days
  fetchAccountMetrics()
}

/**
 * 获取账户指标数据
 */
const fetchAccountMetrics = (): void => {
  window.electron.ipcRenderer
    .invoke('getAccountMetrics', { days: currentSpan.value })
    .then((data: any) => {
      currentMonthData.value = data.currentMonth
      previousMonthData.value = data.previousMonth
    })
}

// 页面加载时获取账户指标数据
onMounted(() => {
  fetchAccountMetrics()
})

// 卸载
onUnmounted(() => {})
</script>

<template>
  <!-- 销售指标卡片组 -->
  <div class="flex relative mb-4">
    <div class="card min-w-[20%] mr-4 dark:!bg-[#5d38ff]">
      <MetricStatsCard
        class="first-card"
        :title="metrics[0].title"
        :current-value="currentMonthData[metrics[0].id]"
        :previous-value="previousMonthData[metrics[0].id]"
        :type="metrics[0].type"
        :days="currentSpan"
      />
    </div>
    <div class="card flex-1 flex-row dark:!bg-[#5d38ff]">
      <MetricStatsCard
        style="width: 20%"
        :title="metrics[1].title"
        :current-value="currentMonthData[metrics[1].id]"
        :previous-value="previousMonthData[metrics[1].id]"
        :type="metrics[1].type"
        :days="currentSpan"
      />
      <el-divider
        direction="vertical"
        class="h-full m-l-0"
        style="border-left-color: rgba(255, 255, 255, 0.3)"
      />
      <MetricStatsCard
        style="width: 20%"
        :title="metrics[2].title"
        :current-value="currentMonthData[metrics[2].id]"
        :previous-value="previousMonthData[metrics[2].id]"
        :type="metrics[2].type"
        :days="currentSpan"
      />
      <el-divider
        direction="vertical"
        class="h-full m-l-0"
        style="border-left-color: rgba(255, 255, 255, 0.3)"
      />

      <MetricStatsCard
        style="width: 20%"
        :title="metrics[3].title"
        :current-value="currentMonthData[metrics[3].id]"
        :previous-value="previousMonthData[metrics[3].id]"
        :type="metrics[3].type"
        :days="currentSpan"
      />
      <el-divider
        direction="vertical"
        class="h-full m-l-0"
        style="border-left-color: rgba(255, 255, 255, 0.3)"
      />
    </div>
    <!-- 时间跨度选择器 -->
    <div class="absolute top-4 right-4">
      <div class="d-flex align-items-center btn-group btn-group-sm" role="group">
        <button
          type="button"
          class="btn dark:text-white text-gray-500"
          :class="currentSpan === 15 ? 'btn-primary text-white' : 'btn-outline-primary'"
          @click="handleSpanChange(15)"
        >
          15天周期
        </button>
        <button
          type="button"
          class="btn dark:text-white text-gray-500"
          style="
            border-top-right-radius: 4px !important;
            border-bottom-right-radius: 4px !important;
          "
          :class="currentSpan === 30 ? 'btn-primary text-white' : 'btn-outline-primary'"
          @click="handleSpanChange(30)"
        >
          30天周期
        </button>
        <RouterLink class="ms-2 dark:text-white text-gray-500" to="/account-data">
          查看更多
        </RouterLink>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
