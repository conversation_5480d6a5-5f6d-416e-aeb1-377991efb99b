# 公屏消息折叠功能实现

## 任务背景

在 LinkAge.vue 组件的公屏区域，需要对重复出现的消息进行折叠处理，显示 `+数量` 标识，并提供悬浮提示显示详细内容。

## 实现方案

使用 Vue 计算属性 + Bootstrap Popover 的方案：

### 1. 数据结构设计

```typescript
interface CollapsedComment {
  id: string | number // 用于渲染的唯一标识
  content: string // 消息内容
  event_ts: number // 第一条消息的时间戳（用于联动）
  isCollapsed: boolean // 是否是折叠项
  count: number // 重复次数
  details: Array<{
    // 详细信息（用于悬浮提示）
    event_ts: number
    content: string
    time: string
  }>
}
```

### 2. 关键修改点

#### 添加计算属性处理折叠逻辑

- 遍历 `roomData.comments` 检测连续相同内容的消息
- 将重复消息合并，保留第一条用于联动
- 记录重复次数和详细信息

#### 修改模板渲染

- 使用 `collapsedComments` 替代 `roomData.comments`
- 添加条件渲染显示 `+数量` 标识
- 集成 Bootstrap Popover 实现悬浮提示

#### 悬浮提示功能

- `generatePopoverContent` 函数生成 HTML 格式的提示内容
- 包含每条重复消息的时间和内容
- 延迟初始化确保 DOM 已渲染

### 3. 保持原有功能

- 视频联动功能：通过 `linkage` 函数保持不变
- 时间同步：继续使用第一条消息的时间戳
- 滚动定位：保持原有的滚动到中心逻辑

## 效果预期

- 连续重复的消息将被折叠为一条
- 折叠消息后显示蓝色 `+数量` 标识
- 鼠标悬浮显示所有重复消息的时间和内容
- 保持所有原有的联动和同步功能
