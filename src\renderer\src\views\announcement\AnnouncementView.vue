<template>
  <div class="announcement-container">
    <!-- {{ AURA-X: Modify - 替换iframe为Vue组件. Approval: 寸止(ID:1699888404). }} -->
    <AnnouncementContent />
  </div>
</template>

<script setup lang="ts">
// {{ AURA-X: Add - 导入公告内容组件. Approval: 寸止(ID:1699888405). }}
import AnnouncementContent from './components/AnnouncementContent.vue'

// 公告页面组件
</script>

<style scoped>
.announcement-container {
  height: 100%;
  width: 100%;
  /* {{ AURA-X: Remove - 移除padding，让组件全屏显示. Approval: 寸止(ID:1699888406). }} */
  box-sizing: border-box;
  overflow: auto;
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #a3a6ad;
    opacity: 0.3;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a3a6ad;
    opacity: 0.5;
  }
}
</style>
