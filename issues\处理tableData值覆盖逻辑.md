# 处理tableData值覆盖逻辑

## 任务描述

为CanvasFunnel.vue组件添加tableData props，并实现当tableData中存在相同指标时优先使用tableData值的覆盖逻辑。

## 实现步骤

### 1. 添加数据类型定义

- 定义`TableDataItem`接口，包含：
  - `index_display`: 指标显示名称
  - `index_name`: 指标名称
  - `this_field`: 当前字段值
  - `last_game`: 上次数据值

### 2. 更新Props定义

- 将`tableData?: any`更改为`tableData?: TableDataItem[]`
- 提供明确的类型约束

### 3. 实现值覆盖函数

- 创建`getOverriddenValue`函数
- 以`index_display`为匹配字段（flowAnalysisData的index_name对应tableData的index_display）
- 如果tableData中存在匹配项，返回`this_field.value`，否则返回原始值

### 4. 更新所有显示逻辑

- GMV漏斗的5个指标显示值
- 互动漏斗的3个指标显示值
- 所有转化率计算函数
- 总转化率计算

## 关键实现点

- 匹配逻辑：`flowAnalysisData.index_name === tableData.index_display`
- 优先级：tableData > flowAnalysisData
- 影响范围：所有数值显示和转化率计算

## 测试验证

- 当传入tableData时，对应指标应显示tableData的值
- 当tableData为空或不匹配时，应显示原始flowAnalysisData的值
- 转化率计算应基于覆盖后的值进行
