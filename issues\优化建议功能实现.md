# 优化建议功能实现

## 任务概述

在 ReportView.vue 页面中添加查看优化建议的功能，按照现有的归因分析逻辑，为表格第四行（优化建议）添加弹窗显示功能。

## 实现内容

### 1. 状态管理

- 添加 `optimizationModal` ref 对象，管理优化建议弹窗的显示状态
- 包含 visible、title、content 三个属性

### 2. 功能函数

- `shouldShowAsOptimizationLink`: 判断是否显示为优化建议链接
- `formatOptimizationCell`: 格式化优化建议单元格，生成可点击的链接
- `handleOptimizationClick`: 处理优化建议链接点击事件
- `closeOptimizationModal`: 关闭优化建议弹窗

### 3. 数据处理

- 修改 `extractionch` 函数，在第四行（`empty2`）中添加优化建议链接逻辑
- 使用与归因分析相同的条件判断（`shouldShowAttributionLink`）来决定是否显示链接

### 4. 事件监听

- 在 `onMounted` 中添加 `optimization-link` 类的点击事件监听
- 与归因分析链接共享同一个事件委托

### 5. 模板更新

- 更新所有表格列的模板，添加对优化建议链接的支持
- 在所有 `<template #default="{ row, column }">` 中添加：
  ```vue
  <span
    v-else-if="shouldShowAsOptimizationLink(row[column.field])"
    v-html="formatOptimizationCell(row[column.field])"
  ></span>
  ```
- 添加优化建议弹窗模板

### 6. 涉及表格

- 直播内容板块复盘子表格（4个字段）
- 直播带货板块复盘子表格（4个字段）

## 功能特性

- 与归因分析使用相同的判断条件
- 在满足条件时显示"查看优化建议"链接
- 点击链接打开独立的优化建议弹窗
- 弹窗标题格式：`[字段名]优化建议`
- 弹窗内容暂时为空，等待后续添加

## 技术实现

- 使用 Vue 3 Composition API
- Element Plus 弹窗组件
- VXE Table 表格组件
- 事件委托处理点击事件
- 条件渲染控制链接显示

## 状态

✅ 已完成

- 所有代码修改已完成
- 功能逻辑已实现
- 模板已更新
- 弹窗已添加

⏳ 待完成

- 优化建议文案内容（用户后续添加）
