# 基准值配置支持计算型指标

## 任务描述

为新添加的计算型指标 `live_show_watch_ucnt_ratio`（曝光-观看率（人数））添加基准值配置支持。

## 问题分析

原有的基准值配置只支持原始数据中的率指标，通过 `getRatioFields` 函数筛选 `unit === 'ratio'` 的字段。但是新添加的 `live_show_watch_ucnt_ratio` 是计算型指标，不在原始 `core_data` 中，因此不会出现在基准值配置列表中。

## 解决方案

修改 `ratioFields` 计算属性，在原有的标准率指标基础上，手动添加计算型率指标。

## 实现步骤

### 1. 修改 ratioFields 计算属性

**文件**: `src/renderer/src/components/live/LiveDataPanel.vue`

**修改前**:

```javascript
const ratioFields = computed(() => {
  return getRatioFields(props.roomData?.core_data)
})
```

**修改后**:

```javascript
const ratioFields = computed(() => {
  const standardRatioFields = getRatioFields(props.roomData?.core_data)

  // 添加计算型指标到率指标列表中
  const calculatedRatioFields: any[] = []

  // 添加 live_show_watch_ucnt_ratio 计算型指标
  const calculatedItem = createCalculatedCoreDataItem('live_show_watch_ucnt_ratio')
  if (calculatedItem) {
    calculatedRatioFields.push(calculatedItem)
  }

  return [...standardRatioFields, ...calculatedRatioFields]
})
```

## 功能验证

1. 打开直播数据对比页面
2. 点击"配置指标"按钮
3. 在基准配置部分应该能看到"曝光-观看率（人数）"指标
4. 可以为该指标设置基准值（0-100%）
5. 设置基准值后，数据显示时会根据基准值显示颜色提示（绿色⬇/红色⬆）

## 技术细节

- 使用 `createCalculatedCoreDataItem` 创建计算型指标的数据结构
- 保持与原有基准值配置逻辑的兼容性
- 计算型指标的基准值配置会保存到 `localStorage`，与其他指标一致

## 文件修改

- `src/renderer/src/components/live/LiveDataPanel.vue` - 修改 ratioFields 计算属性

## 状态

✅ 已完成 - 基准值配置现在支持计算型指标 `live_show_watch_ucnt_ratio`
