<template>
  <!-- 登录模态框 -->
  <LoginModal />

  <!-- 直播监控模态框 -->
  <LiveMonitorModal />

  <!-- 下载进度显示 -->
  <div v-if="need_install" class="container-fluid p-4">
    <DownloadProgress :files="need_download_rooms" />
  </div>

  <!-- 主要内容 -->
  <div v-else class="dashboard-content">
    <!-- 指标卡片 -->
    <AccountMetrics />

    <!-- 数据表格 -->
    <div class="hometable-style"><HomeTable /></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount } from 'vue'
import HomeTable from './components/DashboardTable.vue'
import AccountMetrics from './components/DashboardMetrics.vue'
import DownloadProgress from '@/components/reportpomponents/DownloadProgress.vue'
import LoginModal from '@/components/modals/LoginModal.vue'
import LiveMonitorModal from '@/components/modals/LiveMonitorModal.vue'
import { useAuth } from '@/composables/useAuth'
import { useLiveMonitor } from '@/composables/useLiveMonitor'
import { useAppState } from '@/composables/useAppState'

// 引入必要的CSS
import '@/assets/vendor/libs/spinkit/spinkit.css'

// 使用各种状态管理
const { initAuthEventListeners, cleanupAuthEventListeners } = useAuth()

const { initLiveMonitorEventListeners, cleanupLiveMonitorEventListeners } = useLiveMonitor()

const {
  need_download_rooms,
  need_install,
  initAppStateEventListeners,
  cleanupAppStateEventListeners
} = useAppState()

onMounted(() => {
  // {{ AURA-X: Add - 记录首页进入时的内存状态. Approval: 寸止 }}

  // 设置body的overflow为hidden（用于布局需要）
  document.body.style.overflow = 'hidden'

  // 监听登录模态框关闭事件
  const loginModal = document.getElementById('loginModal')
  if (loginModal) {
    loginModal.addEventListener('hidden.bs.modal', () => {
      window.electron.ipcRenderer.send('close_login_form')
    })
  }

  // 初始化各种事件监听
  initAuthEventListeners()
  initLiveMonitorEventListeners()
  initAppStateEventListeners()
})

onBeforeUnmount(() => {
  // {{ AURA-X: Add - 记录首页离开时的内存状态. Approval: 寸止 }}

  // 恢复body的overflow
  document.body.style.overflowY = 'scroll'
  document.body.style.overflow = 'auto'

  // 清理所有事件监听器
  cleanupAuthEventListeners()
  cleanupLiveMonitorEventListeners()
  cleanupAppStateEventListeners()
})
</script>

<style scoped>
.dashboard-content {
  padding: 16px 16px 55px 16px;
  height: 100%;
  /* overflow-y: auto; */
}

.hometable-style {
  height: calc(100% - 182px);
}

.hometable-style div:nth-child(1) {
  padding: 16px 24px;
}
/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-content {
    padding: 1rem;
  }
}
</style>
