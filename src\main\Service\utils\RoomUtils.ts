/**
 * 房间工具函数集合
 * 提供房间相关的所有工具函数和数据处理逻辑
 */
import * as path from 'path'
import * as fs from 'fs'
import { RoomCoreData } from '../../module/interfaces'
import { upsertRoomCoreData } from '../business/RoomService'

// ================================
// 格式化工具函数
// ================================

/**
 * 格式化时长（秒）为 "X小时Y分Z秒" 格式
 * @param seconds 秒数
 * @returns 格式化后的时长字符串
 */
export function formatDuration(seconds: number): string {
  if (isNaN(seconds) || seconds < 0) {
    return '0秒'
  }

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  const parts: string[] = []

  if (hours > 0) {
    parts.push(`${hours}小时`)
  }
  if (minutes > 0) {
    parts.push(`${minutes}分`)
  }
  if (remainingSeconds > 0 || parts.length === 0) {
    parts.push(`${remainingSeconds}秒`)
  }

  return parts.join('')
}

// ================================
// 指标计算工具函数
// ================================

/**
 * 计算账户指标数据
 * @param data 直播间数据数组
 * @returns 计算后的指标对象
 */
export function calculateAccountMetrics(data: RoomCoreData[]): {
  totalPayAmt: number // 销售额
  avgWatchUcnt: number // 场均观看
  totalPayComboCnt: number // 销量
  totalRefundAmt: number // 退款额

  // 新增指标
  totalLiveShowCnt: number // 曝光次数
  totalLiveShowUcnt: number // 直播间曝光人数
  totalEntryWatchUcnt: number // 进入直播间人数
  totalFollowAnchorUcnt: number // 新增粉丝数
  totalFansClubJoinUcnt: number // 新加直播团人数
  totalIncrEcfClubUcnt: number // 新加购物团人数
  totalLikeCnt: number // 点赞次数
  totalCommentCnt: number // 评论次数
  totalProductShowUcnt: number // 商品曝光人数
  totalProductClickUcnt: number // 商品点击人数
  totalPayUcnt: number // 成交人数
  avgGpm: number // 千次观看成交金额
  totalPayDepositPreOrderAmt: number // 预售全款金额
} {
  // 初始化指标值
  let totalPayAmt = 0 // 销售额
  let totalWatchUcnt = 0 // 观看用户数累计
  let totalPayComboCnt = 0 // 销量
  let totalRefundAmt = 0 // 退款额

  // 初始化新增指标值
  let totalLiveShowCnt = 0 // 曝光次数
  let totalLiveShowUcnt = 0 // 直播间曝光人数
  let totalEntryWatchUcnt = 0 // 进入直播间人数
  let totalFollowAnchorUcnt = 0 // 新增粉丝数
  let totalFansClubJoinUcnt = 0 // 新加直播团人数
  let totalIncrEcfClubUcnt = 0 // 新加购物团人数
  let totalLikeCnt = 0 // 点赞次数
  let totalCommentCnt = 0 // 评论次数
  let totalProductShowUcnt = 0 // 商品曝光人数
  let totalProductClickUcnt = 0 // 商品点击人数
  let totalPayUcnt = 0 // 成交人数
  let totalGpm = 0 // 千次观看成交金额总和
  let gpmCount = 0 // 有效GPM数据计数
  let totalPayDepositPreOrderAmt = 0 // 预售全款金额

  // 遍历数据计算指标
  for (const room of data) {
    // 累加销售额 (pay_amt)
    if (room.pay_amt !== undefined && room.pay_amt !== null) {
      totalPayAmt += parseFloat(room.pay_amt.toString())
    }

    // 累加观看用户数 (watch_ucnt)
    if (room.watch_ucnt !== undefined && room.watch_ucnt !== null) {
      // 处理可能包含"万"的情况
      const watchUcntStr = room.watch_ucnt.toString()
      if (watchUcntStr.includes('万')) {
        totalWatchUcnt += parseFloat(watchUcntStr.replace('万', '')) * 10000
      } else {
        totalWatchUcnt += parseFloat(watchUcntStr)
      }
    }

    // 累加销量 (pay_combo_cnt)
    if (room.pay_combo_cnt !== undefined && room.pay_combo_cnt !== null) {
      totalPayComboCnt += parseFloat(room.pay_combo_cnt.toString())
    }

    // 累加退款额 (real_refund_amt)
    if (room.real_refund_amt !== undefined && room.real_refund_amt !== null) {
      totalRefundAmt += parseFloat(room.real_refund_amt.toString())
    }

    // 累加曝光次数 (live_show_cnt)
    if (room.live_show_cnt !== undefined && room.live_show_cnt !== null) {
      totalLiveShowCnt += parseFloat(room.live_show_cnt.toString())
    }

    // 累加直播间曝光人数 (live_show_ucnt)
    if (room.live_show_ucnt !== undefined && room.live_show_ucnt !== null) {
      // 处理可能包含"万"的情况
      const liveShowUcntStr = room.live_show_ucnt.toString()
      if (liveShowUcntStr.includes('万')) {
        totalLiveShowUcnt += parseFloat(liveShowUcntStr.replace('万', '')) * 10000
      } else {
        totalLiveShowUcnt += parseFloat(liveShowUcntStr)
      }
    }

    // 累加进入直播间人数 (entry_watch_ucnt)
    if (room.entry_watch_ucnt !== undefined && room.entry_watch_ucnt !== null) {
      // 处理可能包含"万"的情况
      const entryWatchUcntStr = room.entry_watch_ucnt.toString()
      if (entryWatchUcntStr.includes('万')) {
        totalEntryWatchUcnt += parseFloat(entryWatchUcntStr.replace('万', '')) * 10000
      } else {
        totalEntryWatchUcnt += parseFloat(entryWatchUcntStr)
      }
    }

    // 累加新增粉丝数 (follow_anchor_ucnt)
    if (room.follow_anchor_ucnt !== undefined && room.follow_anchor_ucnt !== null) {
      totalFollowAnchorUcnt += parseFloat(room.follow_anchor_ucnt.toString())
    }

    // 累加新加直播团人数 (fans_club_join_ucnt)
    if (room.fans_club_join_ucnt !== undefined && room.fans_club_join_ucnt !== null) {
      totalFansClubJoinUcnt += parseFloat(room.fans_club_join_ucnt.toString())
    }

    // 累加新加购物团人数 (incr_ecf_club_ucnt)
    if (room.incr_ecf_club_ucnt !== undefined && room.incr_ecf_club_ucnt !== null) {
      totalIncrEcfClubUcnt += parseFloat(room.incr_ecf_club_ucnt.toString())
    }

    // 累加点赞次数 (like_cnt)
    if (room.like_cnt !== undefined && room.like_cnt !== null) {
      totalLikeCnt += parseFloat(room.like_cnt.toString())
    }

    // 累加评论次数 (comment_cnt)
    if (room.comment_cnt !== undefined && room.comment_cnt !== null) {
      totalCommentCnt += parseFloat(room.comment_cnt.toString())
    }

    // 累加商品曝光人数 (product_show_ucnt)
    if (room.product_show_ucnt !== undefined && room.product_show_ucnt !== null) {
      totalProductShowUcnt += parseFloat(room.product_show_ucnt.toString())
    }

    // 累加商品点击人数 (product_click_ucnt)
    if (room.product_click_ucnt !== undefined && room.product_click_ucnt !== null) {
      totalProductClickUcnt += parseFloat(room.product_click_ucnt.toString())
    }

    // 累加成交人数 (pay_ucnt)
    if (room.pay_ucnt !== undefined && room.pay_ucnt !== null) {
      totalPayUcnt += parseFloat(room.pay_ucnt.toString())
    }

    // 累加千次观看成交金额 (gpm)
    if (room.gpm !== undefined && room.gpm !== null) {
      const gpmValue = parseFloat(room.gpm.toString())
      if (!isNaN(gpmValue)) {
        totalGpm += gpmValue
        gpmCount++
      }
    }

    // 累加预售全款金额 (pay_deposit_pre_order_amt)
    if (room.pay_deposit_pre_order_amt !== undefined && room.pay_deposit_pre_order_amt !== null) {
      totalPayDepositPreOrderAmt += parseFloat(room.pay_deposit_pre_order_amt.toString())
    }
  }

  // 计算场均观看用户数
  const avgWatchUcnt = data.length > 0 ? Math.round(totalWatchUcnt / data.length) : 0

  // 计算平均千次观看成交金额
  const avgGpm = gpmCount > 0 ? totalGpm / gpmCount : 0

  // 格式化金额，保留两位小数
  totalPayAmt = parseFloat(totalPayAmt.toFixed(2))
  totalRefundAmt = parseFloat(totalRefundAmt.toFixed(2))
  totalPayDepositPreOrderAmt = parseFloat(totalPayDepositPreOrderAmt.toFixed(2))

  return {
    totalPayAmt,
    avgWatchUcnt,
    totalPayComboCnt,
    totalRefundAmt,

    // 新增指标
    totalLiveShowCnt,
    totalLiveShowUcnt,
    totalEntryWatchUcnt,
    totalFollowAnchorUcnt,
    totalFansClubJoinUcnt,
    totalIncrEcfClubUcnt,
    totalLikeCnt,
    totalCommentCnt,
    totalProductShowUcnt,
    totalProductClickUcnt,
    totalPayUcnt,
    avgGpm,
    totalPayDepositPreOrderAmt
  }
}

// ================================
// 房间数据转换工具函数
// ================================

/**
 * 通过ID读取room文件获取原始数据
 * @param id 房间ID
 * @returns 原始房间数据，如果文件不存在则返回null
 */
export async function readRoomDataById(id: string): Promise<any | null> {
  try {
    const roomFilePath = path.join(path.resolve('cache'), `room_${id}`)

    if (!fs.existsSync(roomFilePath)) {
      console.warn(`房间文件不存在: ${roomFilePath}`)
      return null
    }

    const roomContent = fs.readFileSync(roomFilePath, 'utf-8')
    const roomData = JSON.parse(roomContent)

    return roomData
  } catch (error) {
    console.error(`读取房间 ${id} 文件失败:`, error)
    return null
  }
}

/**
 * 从 flow_distribution.natural_data 中提取直播推荐数据
 * @param flowDistribution 流量分布数据
 * @returns 直播推荐的 watch_ratio.value
 */
export function extractZbtjFromFlowDistribution(flowDistribution: any): number {
  try {
    if (!flowDistribution?.natural_data) {
      return 0
    }

    const zhiBoTuiJian = flowDistribution.natural_data.find(
      (item: any) => item.channel_name === '直播推荐'
    )

    return zhiBoTuiJian?.watch_ratio?.value || 0
  } catch (error) {
    console.error('提取直播推荐数据失败:', error)
    return 0
  }
}

/**
 * 从 flow_distribution.pay_data 中计算付费流量
 * @param flowDistribution 流量分布数据
 * @returns 付费流量的 watch_ratio.value 总和
 */
export function extractFfllFromFlowDistribution(flowDistribution: any): number {
  try {
    if (!flowDistribution?.pay_data || !Array.isArray(flowDistribution.pay_data)) {
      return 0
    }

    return flowDistribution.pay_data.reduce((total: number, item: any) => {
      return total + (item?.watch_ratio?.value || 0)
    }, 0)
  } catch (error) {
    console.error('提取付费流量数据失败:', error)
    return 0
  }
}

/**
 * 工具函数：roomData原始数据转数据库格式数据
 * @param roomData 原始房间数据
 * @returns 转换后的RoomCoreData对象
 */
export async function roomData2RoomCoreData(roomData: any): Promise<RoomCoreData> {
  // 提取基础ID信息
  const liveId = roomData.room_id || roomData.live_id
  if (!liveId) {
    throw new Error('无效的room_id或live_id')
  }

  // 计算直播时长（live_end_ts - live_start_ts）
  const liveDuration =
    roomData.live_base_info?.live_end_ts && roomData.live_base_info?.live_start_ts
      ? roomData.live_base_info.live_end_ts - roomData.live_base_info.live_start_ts
      : 0

  // 从 flow_distribution 中提取数据
  const zbtj = extractZbtjFromFlowDistribution(roomData.flow_distribution)
  const ffll = extractFfllFromFlowDistribution(roomData.flow_distribution)

  // 构建基础的RoomCoreData对象
  const roomCoreData: RoomCoreData = {
    live_id: liveId,
    zbtj: zbtj,
    ffll: ffll,
    live_room: roomData.live_room,
    start_time: roomData.start_time || roomData.live_base_info?.live_start_time,
    start_time_ts: roomData.live_base_info?.live_start_ts,
    end_time_ts: roomData.live_base_info?.live_end_ts,
    live_duration: formatDuration(liveDuration)
  }

  // 处理core_data中的字段
  if (roomData.core_data && Array.isArray(roomData.core_data)) {
    for (const item of roomData.core_data) {
      if (item.index_name && item.value !== undefined) {
        const fieldName = item.index_name
        let fieldValue = item.value.value

        // 数据清理和类型转换
        if (fieldValue === null || fieldValue === undefined) {
          fieldValue = null
        } else if (typeof fieldValue === 'string' && fieldValue.trim() === '') {
          fieldValue = null
        } else if (typeof fieldValue === 'number' && isNaN(fieldValue)) {
          fieldValue = null
        }

        // 动态赋值到roomCoreData对象
        ;(roomCoreData as any)[fieldName] = fieldValue
      }
    }
  }

  return roomCoreData
}

/**
 * 通过ID获取转换后的房间核心数据（从文件读取并转换）
 * @param id 房间ID
 * @returns 转换后的RoomCoreData对象，如果失败则返回null
 */
export async function getRoomCoreDataByIdFromFile(id: string): Promise<RoomCoreData | null> {
  try {
    // 1. 读取原始房间数据
    const roomData = await readRoomDataById(id)
    if (!roomData) {
      return null
    }

    // 2. 转换为RoomCoreData格式
    const roomCoreData = await roomData2RoomCoreData(roomData)
    return roomCoreData
  } catch (error) {
    console.error(`获取房间 ${id} 核心数据失败:`, error)
    return null
  }
}

/**
 * 工具函数，传入roomlist对象 转换一些补充字段后返回
 * @param roomListItems 房间列表
 * @returns 转换后的房间列表
 */
function transformRoomList(roomListItems: RoomCoreData): RoomCoreData {
  // 转换start_time为时间戳
  let startTimeTs: number | null = null
  if (roomListItems.start_time) {
    try {
      // 将"2025/05/20 19:30"格式转换为时间戳
      const startTimeDate = new Date(roomListItems.start_time.replace(/\//g, '-'))
      startTimeTs = Math.floor(startTimeDate.getTime() / 1000)
    } catch (error) {
      console.error('转换开播时间失败:', error)
      startTimeTs = null
    }
  }

  // 计算end_time_ts (开播时间 + 直播时长)
  let endTimeTs: number | null = null
  if (startTimeTs && roomListItems.live_duration) {
    try {
      const durationSeconds = parseDurationToSeconds(roomListItems.live_duration)
      endTimeTs = startTimeTs + durationSeconds
    } catch (error) {
      console.error('计算结束时间失败:', error)
      endTimeTs = null
    }
  }

  // 转换pay_gmv(元)为pay_amt(分)
  let payAmt: number | null = null
  if (roomListItems.pay_gmv) {
    try {
      // 移除"¥"符号和","，然后转换为分
      const gmvStr = roomListItems.pay_gmv.toString().replace(/[¥,]/g, '')
      const gmvNumber = parseFloat(gmvStr)
      if (!isNaN(gmvNumber)) {
        payAmt = Math.round(gmvNumber * 100) // 元转分
      }
    } catch (error) {
      console.error('转换支付金额失败:', error)
      payAmt = null
    }
  }

  // 转换 avg_watch_duration 时间字符串为秒数
  let avgWatchDuration: number | null = null
  if (roomListItems.avg_watch_duration) {
    avgWatchDuration = parseDurationToSeconds(roomListItems.avg_watch_duration as unknown as string)
  }

  return {
    ...roomListItems,
    start_time_ts: startTimeTs || undefined,
    end_time_ts: endTimeTs || undefined,
    pay_amt: payAmt || undefined,
    avg_watch_duration: avgWatchDuration || undefined
  }
}

/**
 * 解析时长字符串为秒数
 * @param durationStr 时长字符串，如"2小时16分20秒"
 * @returns 总秒数
 */
function parseDurationToSeconds(durationStr: string): number {
  let totalSeconds = 0
  // 匹配小时
  const hoursMatch = durationStr.match(/(\d+)小时/)
  if (hoursMatch) {
    totalSeconds += parseInt(hoursMatch[1]) * 3600
  }

  // 匹配分钟
  const minutesMatch = durationStr.match(/(\d+)分/)
  if (minutesMatch) {
    totalSeconds += parseInt(minutesMatch[1]) * 60
  }

  // 匹配秒
  const secondsMatch = durationStr.match(/(\d+)秒/)
  if (secondsMatch) {
    totalSeconds += parseInt(secondsMatch[1])
  }

  return totalSeconds
}

export async function upsertRoomList(roomListItems: RoomCoreData): Promise<boolean> {
  const roomList = transformRoomList(roomListItems)
  return await upsertRoomCoreData(roomList)
}
