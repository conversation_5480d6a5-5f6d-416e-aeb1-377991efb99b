interface UserInfo {
  isPrivate: boolean
  likesCount: number | null
  worksCount: number | null
}

function parseUserInfo(htmlSource: string): UserInfo {
  const result: UserInfo = {
    isPrivate: false,
    likesCount: null,
    worksCount: null
  }

  // 检查是否为私密账号
  if (htmlSource.includes('私密账号')) {
    result.isPrivate = true
    return result
  }

  // 查找"作品"文本附近的数字
  const worksMatch = htmlSource.match(
    />作品<\/span>[\s\S]*?data-e2e="user-tab-count">(\d+)<\/span>/
  )

  if (worksMatch) {
    result.worksCount = parseInt(worksMatch[1])
  }

  // 查找"喜欢"文本附近的数字
  const likesMatch = htmlSource.match(
    />喜欢<\/span>[\s\S]*?data-e2e="user-tab-count">(\d+)<\/span>/
  )
  if (likesMatch) {
    result.likesCount = parseInt(likesMatch[1])
  }

  // 如果上面没匹配到，用备用方案
  if (result.worksCount === null && result.likesCount === null) {
    const allNumbers = htmlSource.match(/data-e2e="user-tab-count">(\d+)<\/span>/g)
    if (allNumbers && allNumbers.length > 0) {
      // 找"作品"后面的第一个数字
      const worksIndex = htmlSource.indexOf('作品')
      if (worksIndex !== -1) {
        const afterWorks = htmlSource.substring(worksIndex)
        const worksNum = afterWorks.match(/data-e2e="user-tab-count">(\d+)/)
        if (worksNum) result.worksCount = parseInt(worksNum[1])
      }

      // 找"喜欢"后面的第一个数字
      const likesIndex = htmlSource.indexOf('喜欢')
      if (likesIndex !== -1) {
        const afterLikes = htmlSource.substring(likesIndex)
        const likesNum = afterLikes.match(/data-e2e="user-tab-count">(\d+)/)
        if (likesNum) result.likesCount = parseInt(likesNum[1])
      }

      // 最后兜底：第一个数字当作品数
      if (result.worksCount === null) {
        const firstNum = allNumbers[0].match(/(\d+)/)

        if (firstNum) result.worksCount = parseInt(firstNum[1])
      }
    }
  }

  return result
}

export { parseUserInfo }
export type { UserInfo }
