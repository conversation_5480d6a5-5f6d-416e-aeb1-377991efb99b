/**
 * 数据库相关的接口定义
 */

/**
 * 直播间核心数据接口
 * 用于表示room_core_data表中的数据结构
 */
export interface RoomCoreData {
  // 基础标识字段
  live_id: string // 直播间ID，主键
  zbtj: number // 直播推荐
  ffll: number // 付费流量

  // 直播间基础信息
  live_room?: string // 直播间标题
  start_time?: string // 开播时间
  start_time_ts?: number // 直播开始时间时间戳
  end_time_ts?: number // 结束时间时间戳
  live_duration?: string // 直播时长

  // 核心观看指标
  online_user_cnt?: number // 在线用户数
  live_show_watch_cnt_ratio?: number // 直播观看比例
  watch_ucnt?: number // 观看用户数
  live_show_cnt?: number // 曝光次数
  avg_watch_duration?: number // 平均在线
  avg_min_comment_cnt?: number // 平均每分钟评论数

  // 互动相关指标
  watch_interact_ucnt_ratio?: number // 观看互动用户比例
  follow_anchor_ucnt?: number // 关注主播用户数
  watch_follow_ucnt_ratio?: number // 观看关注用户比例
  fans_club_join_ucnt?: number // 粉丝团加入用户数
  incr_ecf_club_ucnt_ratio?: number // 增量粉丝团用户比例
  incr_ecf_club_ucnt?: number // 增量粉丝团用户数
  watch_fans_club_join_ucnt_ratio?: number // 观看粉丝团加入用户比例

  // 商业交易指标
  pay_ucnt?: number // 支付用户数
  gpm?: number // 每千次观看GMV
  watch_pay_ucnt_ratio?: number // 观看支付用户比例
  product_click_pay_ucnt_ratio?: number // 产品点击支付用户比例
  real_refund_amt?: number // 实际退款金额
  real_refund_amt_ratio?: number // 实际退款金额比例
  pay_deposit_pre_order_amt?: number // 支付定金预购订单金额
  pay_combo_cnt?: number // 支付组合数
  old_fans_pay_ucnt_ratio?: number // 老粉支付用户比例
  livetoind_pay_amt?: number // 直播转化支付金额
  stat_cost?: number // 统计成本

  // 其他展示指标
  live_show_ucnt?: number // 曝光人数
  pay_amt?: number // 支付金额gmv
  product_show_ucnt?: number // 产品展示用户数
  product_click_ucnt?: number // 产品点击用户数
  product_show_click_ucnt_ratio?: number // 产品展示点击用户比例
  entry_watch_ucnt?: number // 入口观看用户数
  pcu?: number // 最高在线人数
  like_cnt?: number // 点赞数量
  comment_cnt?: number // 评论数量
  old_fans_pay_amt_ratio?: number // 老粉支付金额比例

  // 时间戳
  created_at?: string // 创建时间
  updated_at?: string // 更新时间

  // 动态属性，允许添加其他字段
  [key: string]: any
}

/**
 * 订单数据接口
 * 用于表示orders表中的数据结构
 */
export interface Order {
  // ===== 订单基础信息 =====
  order_id: string // 订单ID，唯一标识
  product_id: string // 商品ID
  product_name: string // 商品名称
  product_detail: string // 商品详情页链接
  user_name: string // 推广人昵称（达人、主播等）

  // ===== 金额与分佣信息 =====
  total_pay_amount: number // 用户支付金额（单位：分）
  commission_rate: number // 分佣比例（如 2100 表示 21.00%）
  estimated_comission: number // 预估总佣金（单位：分）
  real_comission: number // 实际结算佣金（单位：分）
  total_commission: number // 总佣金（含平台服务费之前）

  // ===== 用户与机构分佣 =====
  estimated_user_comission: number // 用户预估分佣（单位：分）
  estimated_inst_comission: number // 机构预估分佣（单位：分）
  settle_user_comission: number // 用户结算分佣（单位：分）
  settle_inst_comission: number // 机构结算分佣（单位：分）

  // ===== 状态相关字段 =====
  order_status: string // 订单状态，如 PAY_SUCC（支付成功）
  account_type: string // 账户类型，如 "正式账户"
  pay_time: number // 支付时间（Unix 时间戳）
  confirm_time: number // 成交时间（如有）
  settle_time: number // 结算时间（如有）

  // ===== 推广与利润字段 =====
  promotion_source: number // 推广来源（如 4 表示直播间）
  user_profit_ratio: number // 用户分润比例（如 10000 表示 100%）
  platform_service_fee: number // 平台服务费（单位：分）
  commission_invoice: boolean // 是否开具佣金发票

  // ===== 分级计划（阶梯返佣） =====
  is_stepped_plan: boolean // 是否为阶梯分佣计划
  threshold_order_count: number | null // 阶梯起始订单数
  stepped_ratio: number | null // 阶梯比例（百分比 * 100）
  estimated_user_stepped_commission: number // 用户阶梯预估佣金
  estimated_inst_stepped_commission: number // 机构阶梯预估佣金
  settle_user_stepped_commission: number // 用户阶梯结算佣金
  settle_inst_stepped_commission: number // 机构阶梯结算佣金

  // ===== 分期支付字段 =====
  stage_order: boolean // 是否为分期订单
  stage_one_pay_time: string // 第一期支付时间（如有）
  stage_two_pay_time: string // 第二期支付时间（如有）
  stage_one_pay_money: number // 第一期支付金额（单位：分）

  // ===== 其他标识字段 =====
  buy_at_ease: boolean // 是否为“放心购”商品
  is_haitao: boolean // 是否为海淘商品
  multi_settled: boolean // 是否多次结算
  unsettled_event: string // 未结算原因描述（如有）
  settle_tech_service_fee: number // 技术服务费（单位：分）
  buyer_app_id: string // 买家使用的App ID（如 1128 是抖音）
  frozen_rate: number // 冻结比例（如 0）
  unsettled_code: string // 未结算代码（如有）

  // ===== 流量与平台渠道 =====
  traffic_source: string // 流量来源，如“直播”
  content_type: string // 内容类型，如“非商品卡”
  media_type_group_name: string // 媒体类型分组，如“直播”
  cos_ratio_type: number // 分佣类型（如 4 表示 CPS）
  has_bonus_ratio: boolean // 是否有奖金比例
  instant_retail_biz_type: number // 零售业务类型（0 表示默认）

  // ===== 商家与店铺信息 =====
  shop_id: string // 店铺ID
  shop_name: string // 店铺名称

  // ===== 时间预估 =====
  estimated_settle_time: string // 预估结算时间（字符串）

  // ===== 系统字段 =====
  created_at?: string // 记录创建时间
  updated_at?: string // 记录更新时间

  // ===== 扩展字段 =====
  [key: string]: any // 动态扩展字段
}

/**
 * 商品数据接口
 * 用于表示products表中的数据结构
 */
export interface Product {
  // ===== 基础信息 =====
  id?: number // 自增主键
  product_id: string // 商品ID
  product_name: string // 商品名称

  // ===== 系统字段 =====
  created_at?: string // 创建时间
  updated_at?: string // 更新时间

  // ===== 扩展字段 =====
  [key: string]: any // 动态扩展字段
}

/**
 * 进入记录表接口
 * 用于表示enter表中的数据结构
 */
export interface Enter {
  // ===== 基础标识字段 =====
  id?: number // 自增主键
  user_id: string // 用户唯一标识，唯一索引
  room_id?: string // 房间ID
  nick_name?: string // 用户昵称

  // ===== 等级信息 =====
  douyin_level?: number // 抖音等级
  badge_level?: number // 灯牌等级

  // ===== 统计信息 =====
  enter_count: number // 进入次数，默认为0

  // ===== 用户属性 =====
  has_profile_video?: boolean // 是否有主页视频
  has_showcase?: boolean // 是否有橱窗
  follower_count?: number // 关注人数

  // ===== 系统字段 =====
  created_at?: string // 创建时间
  updated_at?: string // 更新时间

  // ===== 扩展字段 =====
  [key: string]: any // 动态扩展字段
}
