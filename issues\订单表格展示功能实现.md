# 订单表格展示功能实现

## 任务背景

将ProductTable.vue表格改造为展示订单数据，以Order模型为主，替换原有的商品指标数据。

## 执行计划

### 1. 修改LZYService.ts后端接口

- 导入OrderService中的getOrderList函数
- 添加handle方法'lzy:getOrderList'，支持订单查询参数
- 使用async/await处理异步调用

### 2. 修改ProductTable.vue前端表格

- 更新tableColumns配置，替换为订单相关字段：
  - order_id: 订单ID
  - product_name: 商品名称
  - user_name: 推广人昵称
  - total_pay_amount: 支付金额（分→元格式化）
  - order_status: 订单状态
  - commission_rate: 佣金比例（整数→百分比格式化）
  - estimated_comission: 预估佣金（分→元格式化）
  - real_comission: 实际佣金（分→元格式化）
  - pay_time: 支付时间（Unix时间戳→日期时间格式化）

### 3. 更新数据获取逻辑

- 修改fetchProductData方法为fetchOrderData
- 调用IPC接口'lzy:getOrderList'获取订单数据
- 移除模拟数据，使用真实订单数据

### 4. 适配搜索筛选功能

- 搜索：支持商品名称和推广人昵称搜索
- 筛选：按订单状态筛选（如PAY_SUCC、CANCEL等）
- 移除原有的分类和库存状态筛选

### 5. 更新导出功能

- 修改导出Excel的字段映射，对应订单模型字段
- 确保导出数据格式化正确

## 预期结果

- 表格完全展示订单数据
- 支持订单搜索和状态筛选
- 支持订单数据导出
- 金额和时间格式化正确显示
