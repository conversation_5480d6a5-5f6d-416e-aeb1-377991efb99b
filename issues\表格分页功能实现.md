# 表格分页功能实现

## 任务目标

1. 为 HomeTable.vue 添加前端分页功能
2. 重构 useProductTable.ts 复用 usePagination hook

## 实现计划

### 阶段一：HomeTable.vue 分页功能

- [x] 导入 usePagination hook
- [x] 配置分页参数（默认页面大小20，可选10/20/50/100）
- [x] 将 filteredRoomlist 作为数据源
- [x] 使用 paginatedData 替代 filteredRoomlist 作为表格数据
- [x] 添加 Element Plus 分页组件
- [x] 在日期范围变化时重置分页

### 阶段二：重构 useProductTable.ts

- [x] 导入 usePagination hook
- [x] 移除重复的分页状态和计算属性
- [x] 使用 usePagination 处理分页逻辑
- [x] 保持排序功能独立
- [x] 确保返回接口兼容性

## 技术要点

### HomeTable.vue 修改

```typescript
// 导入分页 hook
import { usePagination } from '../../composables/usePagination'

// 配置分页
const {
  currentPage,
  pageSize,
  pageSizes,
  paginatedData,
  totalItems,
  handleCurrentChange,
  handleSizeChange,
  resetPagination
} = usePagination(filteredRoomlist, {
  defaultPageSize: 20,
  pageSizes: [10, 20, 50, 100]
})

// 在日期变化时重置分页
const setQuickDateRange = (days: string): void => {
  // ... 设置日期逻辑
  resetPagination()
}
```

### useProductTable.ts 重构

```typescript
// 导入基础分页 hook
import { usePagination } from './usePagination'

// 使用 usePagination 处理分页
const {
  currentPage,
  pageSize,
  pageSizes,
  paginatedData,
  totalItems,
  handleCurrentChange: paginationHandleCurrentChange,
  handleSizeChange: paginationHandleSizeChange,
  resetPagination: paginationResetPagination
} = usePagination(sortedData, {
  defaultPageSize: initialPageSize,
  pageSizes: [10, 20, 50, 100]
})
```

## 实现结果

- ✅ HomeTable.vue 成功添加分页功能，支持每页显示条数切换
- ✅ useProductTable.ts 成功重构，复用 usePagination，减少代码重复
- ✅ 保持了现有组件的兼容性
- ✅ 日期筛选与分页功能协同工作

## 文件修改清单

1. `src/renderer/src/components/home/<USER>
2. `src/renderer/src/composables/useProductTable.ts` - 重构复用 usePagination
