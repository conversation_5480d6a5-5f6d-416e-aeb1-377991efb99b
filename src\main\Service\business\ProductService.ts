/**
 * 商品业务服务
 * 提供商品相关的所有业务处理逻辑
 */
import SqliteDB from '../../module/SqliteDB'
import { Product } from '../../module/interfaces'
import { Op } from 'sequelize'

// ================================
// 商品创建服务
// ================================

/**
 * 创建单个商品
 * @param productData 商品数据
 * @returns 操作结果：true-成功，false-失败
 */
export async function createProduct(productData: Product): Promise<boolean> {
  try {
    if (!productData.product_id || !productData.product_name) {
      console.error('商品ID和商品名称为必填字段')
      return false
    }

    // 通过SqliteDB获取模型实例进行操作
    const model = SqliteDB.getProductModel()
    await model.create(productData as any)

    console.log(`创建商品成功: ${productData.product_id}`)
    return true
  } catch (error: any) {
    console.error(`创建商品 ${productData.product_id} 失败:`, error.message)
    return false
  }
}

/**
 * 批量创建商品
 * @param products 商品数据数组
 * @returns 处理结果统计信息
 */
export async function createProducts(products: Product[]): Promise<{
  totalProducts: number
  successCount: number
  failedProducts: Array<{ index: number; error: string; data: Product }>
}> {
  const result = {
    totalProducts: products.length,
    successCount: 0,
    failedProducts: [] as Array<{ index: number; error: string; data: Product }>
  }

  console.log(`开始批量创建${products.length}个商品...`)

  // 分批处理，避免单个事务过大
  const BATCH_SIZE = 50
  const batches: Product[][] = []
  for (let i = 0; i < products.length; i += BATCH_SIZE) {
    batches.push(products.slice(i, i + BATCH_SIZE))
  }

  for (const batch of batches) {
    const transaction = await SqliteDB.getTransaction()

    try {
      const model = SqliteDB.getProductModel()

      for (let i = 0; i < batch.length; i++) {
        const productData = batch[i]
        try {
          if (!productData.product_id || !productData.product_name) {
            throw new Error('商品ID和商品名称不能为空')
          }

          console.log(`处理商品 ${productData.product_id}`)
          await model.create(productData as any, { transaction })

          result.successCount++
        } catch (error: any) {
          result.failedProducts.push({
            index: i,
            error: error?.message || '创建商品失败',
            data: productData
          })
          console.error(`商品创建失败 ${productData.product_id}:`, error.message)
        }
      }

      await transaction.commit()
      console.log('该批次成功提交')
    } catch (batchError: any) {
      await transaction.rollback()
      console.error('该批次提交失败，已回滚:', batchError.message)
    }
  }

  console.log(
    `批量创建完成：成功 ${result.successCount}/${result.totalProducts}，失败 ${result.failedProducts.length} 个`
  )
  if (result.failedProducts.length > 0) {
    console.log('失败的商品:', result.failedProducts.map((item) => item.data.product_id).join(', '))
  }

  return result
}

// ================================
// 商品查询服务
// ================================

/**
 * 获取商品列表（支持分页）
 * @param options 查询选项
 * @returns 商品数据列表和分页信息
 */
export async function getProducts(options?: {
  productName?: string
  page?: number
  pageSize?: number
}): Promise<{
  data: Product[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}> {
  try {
    const model = SqliteDB.getProductModel()

    // 构建查询条件
    const whereCondition: any = {}
    if (options?.productName && options.productName.trim()) {
      whereCondition.product_name = {
        [Op.like]: `%${options.productName.trim()}%`
      }
    }

    // 分页参数处理
    const page = Math.max(1, options?.page || 1)
    const pageSize = Math.min(100, Math.max(1, options?.pageSize || 10)) // 限制最大页面大小为100
    const offset = (page - 1) * pageSize

    // 获取总数
    const total = await model.count({
      where: Object.keys(whereCondition).length > 0 ? whereCondition : undefined
    })

    // 获取分页数据
    const results = await model.findAll({
      where: Object.keys(whereCondition).length > 0 ? whereCondition : undefined,
      limit: pageSize,
      offset: offset,
      order: [['created_at', 'DESC']], // 按创建时间倒序
      benchmark: true,
      logging: false
    })

    const totalPages = Math.ceil(total / pageSize)

    return {
      data: results.map((item) => item.toJSON() as Product),
      total,
      page,
      pageSize,
      totalPages
    }
  } catch (error) {
    const errorMsg = options?.productName
      ? `搜索商品名称 ${options.productName} 失败`
      : '获取商品列表失败'
    console.error(errorMsg + ':', error)
    return {
      data: [],
      total: 0,
      page: options?.page || 1,
      pageSize: options?.pageSize || 10,
      totalPages: 0
    }
  }
}

/**
 * 根据商品ID获取商品详情
 * @param productId 商品ID
 * @returns 商品详情
 */
export async function getProductById(productId: string): Promise<Product | null> {
  try {
    if (!productId) {
      console.error('商品ID不能为空')
      return null
    }

    const model = SqliteDB.getProductModel()
    const result = await model.findOne({
      where: { product_id: productId },
      benchmark: true,
      logging: false
    })

    return result ? (result.toJSON() as Product) : null
  } catch (error) {
    console.error(`获取商品 ${productId} 详情失败:`, error)
    return null
  }
}

// ================================
// 商品更新服务
// ================================

/**
 * 更新商品信息
 * @param productId 商品ID
 * @param updateData 要更新的数据
 * @returns 更新结果
 */
export async function updateProduct(
  productId: string,
  updateData: Partial<Product>
): Promise<boolean> {
  try {
    if (!productId) {
      console.error('商品ID不能为空')
      return false
    }

    const model = SqliteDB.getProductModel()
    const [affectedCount] = await model.update(updateData, {
      where: { product_id: productId }
    })

    if (affectedCount > 0) {
      console.log(`商品 ${productId} 信息已更新`)
      return true
    } else {
      console.log(`商品 ${productId} 不存在`)
      return false
    }
  } catch (error) {
    console.error(`更新商品 ${productId} 失败:`, error)
    return false
  }
}

/**
 * 删除商品
 * @param productId 商品ID
 * @returns 删除结果
 */
export async function deleteProduct(productId: string): Promise<boolean> {
  try {
    if (!productId) {
      console.error('商品ID不能为空')
      return false
    }

    const model = SqliteDB.getProductModel()
    const deletedRows = await model.destroy({
      where: { product_id: productId }
    })

    if (deletedRows > 0) {
      console.log(`商品 ${productId} 已删除`)
      return true
    } else {
      console.log(`商品 ${productId} 不存在`)
      return false
    }
  } catch (error) {
    console.error(`删除商品 ${productId} 失败:`, error)
    return false
  }
}

// ================================
// 商品统计服务
// ================================

/**
 * 商品统计指标接口
 */
export interface ProductMetricsData {
  totalProducts: number // 总商品数
  recentProducts: number // 最近7天新增商品数
}

/**
 * 获取商品统计指标
 * @param filters 筛选条件
 * @returns 商品统计数据
 */
export async function getProductMetrics(filters?: {
  productName?: string
}): Promise<ProductMetricsData> {
  try {
    const model = SqliteDB.getProductModel()

    // 构建查询条件
    const whereCondition: any = {}
    if (filters?.productName && filters.productName.trim()) {
      whereCondition.product_name = {
        [Op.like]: `%${filters.productName.trim()}%`
      }
    }

    // 获取总商品数
    const totalProducts = await model.count({
      where: Object.keys(whereCondition).length > 0 ? whereCondition : undefined
    })

    // 获取最近7天新增商品数
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    const recentProducts = await model.count({
      where: {
        ...whereCondition,
        created_at: {
          [Op.gte]: sevenDaysAgo.toISOString()
        }
      }
    })

    return {
      totalProducts,
      recentProducts
    }
  } catch (error) {
    console.error('获取商品统计指标失败:', error)
    return {
      totalProducts: 0,
      recentProducts: 0
    }
  }
}
