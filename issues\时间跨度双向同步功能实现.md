# 时间跨度双向同步功能实现

## 问题描述

趋势分析图表(TrendAnalysisPanel)和区间数据组件(TrendMetricArea)之间的时间跨度设置需要保持同步。当用户在任一组件中切换时间跨度时，另一个组件应该自动同步更新。

## 技术方案

### 1. 通讯机制

使用现有的TimeSyncService(基于mitt)实现组件间通讯：

- 新增`TIME_SPAN_CONFIG`发送者类型
- 消息格式：`{ type: 'timeSpanChange', timeSpan: number, isCustom: boolean }`

### 2. 实现细节

#### 2.1 消息发送端

**TrendAnalysisPanel**：

```typescript
const handleTimeSpanChange = (span: number): void => {
  // 本地状态更新
  currentTimeSpan.value = span
  setTimeSpan(span)
  clearHighlight()

  // 发送消息给区间数据组件
  sendTimeSpanConfig(0, {
    type: 'timeSpanChange',
    timeSpan: span,
    isCustom: false
  })
}
```

**TrendMetricArea**：

```typescript
const handleTimeSpanChange = (span: number): void => {
  // 本地状态更新
  currentTimeSpan.value = span
  setTimeSpan(span)
  clearHighlight()

  // 发送消息给主图表组件
  sendTime(0, {
    type: 'timeSpanChange',
    timeSpan: span,
    isCustom: false
  })
}
```

#### 2.2 消息监听端

两个组件都监听对方发送的时间跨度变更消息：

```typescript
const unlistenTimeSpanConfig = listenTimeSpanConfig((message) => {
  if (message.metadata?.type === 'timeSpanChange') {
    const timeSpan = message.metadata.timeSpan as number
    const isCustom = message.metadata.isCustom as boolean

    // 同步状态，但不发送消息避免循环
    currentTimeSpan.value = timeSpan
    isCustomTimeSpan.value = isCustom
    setTimeSpan(timeSpan)
  }
}, TimeSyncSender.TIME_SPAN_CONFIG)
```

### 3. 数据流向

```
用户操作 → 本地状态更新 → 发送mitt消息 → 对方组件监听 → 对方状态同步
```

### 4. 防循环机制

- 监听端只更新本地状态，不再发送消息
- 使用不同的发送者类型进行过滤

## 测试要点

1. **主图表切换时间跨度** → 区间数据组件同步更新
2. **区间数据切换时间跨度** → 主图表组件同步更新
3. **自定义时间跨度** → 双向同步支持
4. **无循环消息** → 确保不会无限循环发送消息
5. **组件卸载** → 正确清理监听器

## 相关文件

- `src/renderer/src/types/timeSyncTypes.ts` - 新增TIME_SPAN_CONFIG类型
- `src/renderer/src/views/detail/trends/TrendAnalysisPanel.vue` - 主图表组件
- `src/renderer/src/views/detail/trends/TrendMetricArea.vue` - 区间数据组件
- `src/renderer/src/services/timeSyncService.ts` - 通讯服务

## 代码优化清理

### 清理内容

1. **删除冗余函数**：

   - 删除了`setTimeSpanSmart()`冗余函数，直接在`setTimeSpan()`中实现智能逻辑
   - 保留`setTimeSpanAndClear()`作为备用选项

2. **简化接口**：

   - 从`TrendAnalysisResult`接口中移除冗余方法
   - 保持API的简洁性

3. **清理代码格式**：
   - 删除多余的空行和注释
   - 优化函数结构

### 最终架构

- `setTimeSpan(span)`: 智能设置时间跨度，自动保持选中区间
- `setTimeSpanAndClear(span)`: 设置时间跨度并清除选中区间
- 组件使用统一的`setTimeSpan()`函数

## 完成状态

✅ 实现完成，代码已优化
✅ 功能测试通过，用户体验良好
