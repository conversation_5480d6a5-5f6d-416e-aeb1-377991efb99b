/**
 * 进入记录主服务类
 * 负责 IPC 通信和进入记录相关的业务逻辑调用
 */
import { IpcMain } from 'electron'
import {
  upsertEnterRecords,
  getEnterRecordsByRoom,
  getEnterByUserId,
  getRoomEnterStatistics,
  clearRoomEnterRecords,
  checkRoomDataExists,
  processAndStoreRoomData
} from './business/EnterService'

export default class EnterService {
  /**
   * 注册 IPC 处理器
   * @param ipcMain Electron 的 IpcMain 实例
   */
  public static hook(ipcMain: IpcMain): void {
    // 页面进入时检查并初始化房间数据
    ipcMain.handle('enter:checkAndInitRoom', async (_, roomId) => {
      try {
        console.log(`🚪 页面进入房间 ${roomId}，检查数据...`)

        // 检查数据库中是否存在数据
        const dataExists = await checkRoomDataExists(roomId)

        if (dataExists) {
          console.log(`✓ 房间 ${roomId} 数据已存在，无需处理`)
          return { success: true, dataExists: true, processed: false }
        }

        // 数据不存在，自动处理并存储
        console.log(`📁 房间 ${roomId} 数据不存在，开始处理...`)
        const processResult = await processAndStoreRoomData(roomId)

        return {
          success: true,
          dataExists: false,
          processed: true,
          processedCount: processResult.processedCount,
          insertedCount: processResult.insertedCount,
          error: processResult.error
        }
      } catch (error: any) {
        console.error(`房间 ${roomId} 检查和初始化失败:`, error)
        return { success: false, error: error.message }
      }
    })

    // 按房间ID查询进入记录（支持多条件筛选和分页信息）
    ipcMain.handle('enter:getRecordsByRoom', async (_, roomId, filters) => {
      return await getEnterRecordsByRoom(roomId, filters)
    })

    // 根据用户ID获取进入记录
    ipcMain.handle('enter:getEnterByUserId', async (_, userId) => {
      return await getEnterByUserId(userId)
    })

    // 获取房间进入统计信息（支持筛选条件）
    ipcMain.handle('enter:getRoomStatistics', async (_, roomId, filters) => {
      return await getRoomEnterStatistics(roomId, filters)
    })

    // 清空房间的所有进入记录（危险操作）
    ipcMain.handle('enter:clearRoomRecords', async (_, roomId, confirm) => {
      return await clearRoomEnterRecords(roomId, confirm)
    })
  }

  /**
   * 便捷方法：批量插入/更新进入记录
   * @param enterDataList 进入记录数组
   * @returns 处理结果
   */
  public static async upsertRecords(enterDataList: any[]): Promise<any> {
    return await upsertEnterRecords(enterDataList)
  }

  /**
   * 便捷方法：按房间查询进入记录
   * @param roomId 房间ID
   * @param filters 筛选条件
   * @returns 记录列表
   */
  public static async getRecordsByRoom(roomId: string, filters?: any): Promise<any> {
    return await getEnterRecordsByRoom(roomId, filters)
  }

  /**
   * 便捷方法：获取房间统计信息（支持筛选条件）
   * @param roomId 房间ID
   * @param filters 筛选条件
   * @returns 统计信息
   */
  public static async getRoomStatistics(roomId: string, filters?: any): Promise<any> {
    return await getRoomEnterStatistics(roomId, filters)
  }

  /**
   * 便捷方法：检查房间数据是否存在
   * @param roomId 房间ID
   * @returns 是否存在数据
   */
  public static async checkRoomDataExists(roomId: string): Promise<boolean> {
    return await checkRoomDataExists(roomId)
  }

  /**
   * 便捷方法：处理并存储房间数据
   * @param roomId 房间ID
   * @returns 处理结果
   */
  public static async processAndStoreRoomData(roomId: string): Promise<any> {
    return await processAndStoreRoomData(roomId)
  }
}
