import {
  GetOptions,
  JsonApiClient as OfficialJsonApiClient
} from '@drupal-api-client/json-api-client'

/**
 * 简化的 JsonApiClient - 完全兼容用户调用方式
 * {{ AURA-X: Add - 简化实现. Source: 用户要求不手动构建URL. }}
 */
export class JsonApiClient {
  private officialClient: OfficialJsonApiClient

  constructor(baseUrl: string) {
    this.officialClient = new OfficialJsonApiClient(baseUrl.replace(/\/$/, ''))
  }

  /**
   * 获取资源集合 - 支持queryString参数
   * {{ AURA-X: Add - getCollection方法. Source: 用户提供的调用示例. }}
   */
  async getCollection(resource: string, options?: GetOptions): Promise<any> {
    try {
      console.log(`获取 ${resource} 集合...`)

      // 使用官方客户端
      const result: any = await this.officialClient.getCollection(resource, options)

      console.log('API响应:', result)

      return result
    } catch (error: any) {
      console.error('Drupal API 请求失败:', error)
      throw new Error(`获取资源集合失败: ${error.message}`)
    }
  }

  /**
   * 获取单个资源
   * {{ AURA-X: Add - getResource方法. }}
   */
  async getResource(resource: string, id: string): Promise<any> {
    try {
      console.log(`获取单个资源: ${resource}/${id}`)

      const result = await this.officialClient.getResource(resource, id)
      const apiResult = result as any

      return {
        data: apiResult.data
      }
    } catch (error: any) {
      console.error('获取单个资源失败:', error)
      throw new Error(`获取资源失败: ${error.message}`)
    }
  }
}

/**
 * 创建客户端实例
 */
export const createDrupalClient = (baseUrl: string = 'http://www.tieqiao1.com') => {
  return new JsonApiClient(baseUrl)
}

/**
 * 默认客户端实例
 */
export const drupalClient = createDrupalClient()

// 工具：通过 type+id 找 included 节点
function findIncluded(includedArr: any[], type: string, id: string) {
  return includedArr.find((item) => item.type === type && item.id === id) || {}
}

// 把原始数据映射为你要的格式
function mapNotices(json: any) {
  return json.data.map((n) => {
    const tagId = n.relationships.field_tags?.data?.id
    const userId = n.relationships.uid?.data?.id

    const tag = findIncluded(json.included, 'taxonomy_term--tags', tagId)
    const user = findIncluded(json.included, 'user--user', userId)

    return {
      title: n.attributes.title,
      content: n.attributes.field_body?.summary || n.attributes.field_body?.processed || '',
      tag: tag.attributes?.name || '',
      created: n.attributes.created,
      username: user.attributes?.display_name || ''
    }
  })
}

/**
 * 便捷方法：获取通知列表
 * {{ AURA-X: Add - 便捷方法. Source: 用户提供的字段要求. }}
 */

export const getDrupalNotices = async (options?: GetOptions) => {
  const defaultOptions: GetOptions = {
    queryString: 'include=uid,field_tags,notice_type',
    ...options
  }
  const response = await drupalClient.getCollection('notice--default', defaultOptions)

  return mapNotices(response)
}

// 获取所有tag
export const getDrupalAllTags = async (options?: GetOptions) => {
  const defaultOptions: GetOptions = {
    ...options
  }
  const response = await drupalClient.getCollection('taxonomy_term--tags', defaultOptions)
  return response.data.map((tag) => {
    return {
      id: tag.id,
      name: tag.attributes.name
    }
  })
}
