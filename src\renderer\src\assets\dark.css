/* ==================== CSS 变量定义 ==================== */
:root {
  --title-text-size: 14px;
  --card-title-size: 18px;
  --card-title-weight: 600;
  --btn-radius: 4px;
  --title-text-color: #ffffff;
  --card-title-color: #8a8a98;
  --card-title-color-o: rgb(138, 138, 152, 0.25);
  --card-title-family: 'PingFang SC', sans-serif;

  --floor-bg-color: #191a1c;
  --bg-color: #212228;
  --shadow-color: #34343e;
  --popover-bg-color: #2c2c35;
  --dark-text-color: #70707c;
  --text-color: #8a8a98;
  --middle-text-color: #d0d0da;
  --light-text-color: #fff;
  --title-color: #fff;
  --primary-color: #5d38ff;
  --primary-color-relative: #04d7f6;
  --secondary-color: #7b5dff;
  --button-dark-background-color: #2c2c35;
  --button-background-color: #34343e;
  --button-border-color: #40404a;
  --progress-background-color: #3d3d45;
  --legend-bg-color: #282830;
  --legend-text-color: #d0d0da;
  --button-primary-background: var(--primary-color);
  --button-primary-background-hover: #7552f7;
  --button-primary-background-active: #3e15b3;
  --button-primary-disabled-color: #8a8a98;
  --button-primary-disabled-background: #39117a;
  --button-primary-loading-background: #bb90f8;
  --button-default-color: var(--light-text-color);
  --button-default-color-hover: var(--light-text-color);
  --button-default-color-active: var(--light-text-color);
  --button-default-background: #34343e;
  --button-default-background-hover: var(--button-border-color);
  --button-default-background-active: #5b5b65;
  --button-default-disabled-color: var(--button-border-color);
  --button-default-disabled-background: #25252c;
  --button-default-loading-background: #5b5b65;
  --box-border-color: #4a4a59;
  --box-disabled-checked-background: #2f1c80;
  --box-disabled-checked-color: grey;
  --iuput-inner-color: #fff;
}

/* ==================== 基础组件样式 ==================== */
.dark {
  padding: 0;
}

.dark hr {
  background-color: #384869 !important;
  opacity: 30% !important;
  margin-left: 0px !important;
  margin-right: 0px !important;
}

.dark .font14,
.dark .font,
.dark .f14 {
  font-size: 14px;
  color: #fff;
  font-weight: 500;
  cursor: pointer;
}

.dark .font18 {
  font-size: 18px;
  color: #fff;
}

.dark .dark-1 {
  background: #191a1c;
  margin-top: 5px;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark .border-bottom {
  border-block-end: 1px solid #393742 !important;
}

.dark .border-end {
  border-inline-end: 0rem !important;
}

.dark .detail-font {
  color: var(--light-text-color);
}

.dark .disabled-row {
  background-color: #282830 !important;
  color: #999 !important;
  cursor: not-allowed !important;
}

/* ==================== 布局组件 (Header, Sidebar, 导航) ==================== */
.dark .sidebar-header .brand-text h6 {
  color: var(--title-text-color) !important;
}

.dark .sidebar-header {
  background-color: var(--bg-color) !important;
}

.dark .header-left h5 {
  color: var(--title-text-color);
  font-size: 18px;
}

.dark .header-container nav {
  background-color: var(--bg-color) !important;
  border: 0;
}

.dark .content-container .main-content {
  background-color: var(--floor-bg-color) !important;
}

.dark .sidebar-container .sidebar {
  background-color: var(--bg-color) !important;
}

.dark .header-container .loginstate button {
  color: var(--middle-text-color);
  background-color: var(--popover-bg-color);
  border: 0;
  border-radius: 4px;
}

.dark .header-container .themestate button {
  color: var(--middle-text-color);
}

.dark .sidebar-nav {
  background-color: var(--bg-color);
}

.dark .sidebar-nav .router-link-exact-active {
  color: var(--title-text-color);
  background-color: var(--popover-bg-color);
  box-shadow: 0 0 0;
  font-weight: normal;
}

.dark .sidebar-nav .router-link-exact-active:hover {
  color: var(--middle-text-color);
  background-color: var(--popover-bg-color) !important;
}

.dark .sidebar-nav .nav-link:hover {
  color: var(--middle-text-color);
  background-color: var(--popover-bg-color) !important;
}

.dark .nav-link {
  color: var(--title-text-color) !important;
}

.dark .router-link-exact-active {
  background-color: #424348 !important;
}

.dark .nav-link:hover {
  background-color: #424348 !important;
}

.dark .menu-dark {
  background-color: #282830;
  color: #fff;
}

.dark .menu-link a {
  color: #fff !important;
}

/* ==================== 卡片和容器组件 ==================== */
.dark .card {
  background-color: var(--bg-color) !important;
  border-radius: 7.6px;
}

.dark .video-card {
  background-color: var(--bg-color) !important;
}

.dark .tit-card {
  display: flex;
  justify-content: space-around;
}

.dark .card-header-padding {
  line-height: 54px;
}

.dark .video-card-body {
  margin: 24px -28px;
}

.dark .card-shadow-dark {
  border-radius: 60px;
}

.dark .card-header-title {
  color: var(--title-text-color);
  font-size: var(--card-title-size);
  font-family: var(--card-title-family);
  font-weight: var(--card-title-weight);
}

.dark .card-header .card-header-title {
  color: var(--title-text-color);
  font-size: var(---title-text-size);
}

.dark .card-title p {
  color: var(--card-title-color);
  font-size: var(--title-text-size);
  font-family: var(--card-title-family);
}

.dark .card-title h4 {
  color: var(--title-text-color);
  font-size: var(--card-title-size);
  font-family: var(--card-title-family);
  font-weight: var(--card-title-weight);
}

.dark .card-title .title-p {
  color: #fff;
  font-size: 20px;
}

.dark .card-title .title-f {
  color: #fff;
  font-size: 44px;
  font-weight: 700;
}

.dark .card-title-style {
  background-image: url('@/assets/img/bg.svg');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 4px;
  display: flex;
  justify-content: normal;
  align-items: center;
}

.dark .card-title-container p {
  color: #e0e6ff !important;
}

.dark .card-info .card-info-title {
  color: var(--card-title-color) !important;
  font-size: 14px !important;
}

.dark .card-body .card-info-title span {
  color: var(--title-text-color);
  font-size: var(--card-title-size) !important;
  font-family: var(--card-title-family) !important;
  font-weight: var(--card-title-weight) !important;
}

.dark .card-body .card-info-index_name {
  color: var(--card-title-color) !important;
  font-size: 14px !important;
}

.dark .card-body h5 {
  color: var(--title-text-color);
  font-size: var(--card-title-size);
  font-family: var(--card-title-family);
  font-weight: var(--card-title-weight);
}

/* ==================== 数据展示组件 (表格, 图表, 统计) ==================== */
.dark .--vxe-ui-table-header {
  background-color: #2c2c35;
}

.dark .el-table__header-wrapper th {
  background-color: #2c2c35 !important;
  padding: 5px 0 !important;
  text-align: center !important;
  font-size: 14px !important;
  color: var(--text-color) !important;
  font-weight: 500 !important;
  height: 51px;
}

.dark .el-table__row {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
}

.dark .el-table__row td {
  text-align: center !important;
  color: var(--light-text-color) !important;
  border-bottom: 1px solid var(--button-dark-background-color) !important;
}

.dark .el-table {
  --el-table-tr-bg-color: #282830 !important;
  --el-table-header-bg-color: #282830 !important;
}

.dark .el-table__body tr:nth-child(even) > td {
  background-color: #262631 !important;
}

.dark .room-table td {
  border-bottom: 1px solid var(--button-dark-background-color) !important;
}

.dark .room-table thead {
  background-color: var(--popover-bg-color);
}

.dark .room-table thead th {
  border-bottom: 1px solid var(--button-dark-background-color) !important;
  color: #fff;
  font-weight: normal;
}

.dark .vxe-header--column,
.dark .vxe-table--header-inner-wrapper {
  background-color: #2c2c35 !important;
}

.dark .vxe-table--body .vxe-body--row {
  background-color: #282830 !important;
  color: #fff;
}

.dark .vxe-table--body .row--stripe td {
  background-color: #262631 !important;
  color: #fff;
}

.dark .vxe-table--body-inner-wrapper,
.dark .vxe-table--header-inner-wrapper {
  background-color: #212228 !important;
}

.dark .card-body .channel-table .el-table__row {
  background-color: var(--bg-color);
}

/* 图表相关样式 */
.dark .chartContainerRef div {
  background-color: #282830 !important;
}

.dark .crowd-distribution-container {
  background-color: #282830 !important;
}

.dark .panlchar-container {
  background-color: #282830 !important;
}

.dark .panlchar-container div {
  border: 0px !important;
}

.dark .panelchar {
  color: #fff !important;
}

.dark #chartContainer1 .custom-legend-main {
  background-color: var(--bg-color) !important;
  box-shadow: 0 0 0 !important;
}

.dark #chartContainer2 .custom-legend-main {
  background-color: var(--bg-color) !important;
  box-shadow: 0 0 0 !important;
}

.dark .custom-legend-main-container {
  background-color: var(--bg-color) !important;
}

.dark .custom-legend-main {
  padding: 0 !important;
}

.dark .card-body .lineAreaChart .custom-legend-main-container .btn-chart:hover {
  background-color: var(--legend-bg-color) !important;
  color: var(--title-text-color) !important;
}

.dark .chart-box div:nth-child(2) {
  border: 0px !important;
}

.dark .card-body-lineAreaChart {
  padding: 0 !important;
}

.dark .card-body-lineAreaChart .traffic-layout {
  padding: 16px 24px !important;
}

/* 统计和指标卡片 */
.dark .metric-stats-card {
  border-radius: 20px;
  padding: 1rem;
  color: #fff;
  background: linear-gradient(135deg, rgba(64, 88, 173, 0.25), rgba(103, 178, 111, 0.25));
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
  z-index: 99;
}

.dark .metric-stats-card:hover {
  transform: translateY(-6px);
}

.dark .icon-base {
  font-size: 20px;
  color: #fff;
}

.dark .metric-stats-card p {
  font-size: 0.85rem;
  color: #d1d1d1;
}

.dark .metric-stats-card h4 {
  font-size: 1.2rem;
  font-weight: bold;
  color: #fff;
  margin: 0.5rem 0;
}

.dark .data-style {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-around;
}

.dark .paid-traffic-container {
  background: linear-gradient(to bottom right, #121212, #282830);
  border-radius: 16px;
  padding: 15px;
  position: relative;
  height: 160px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dark .summary-value {
  font-size: 22px;
  color: #fff;
  font-weight: 700;
  animation: pulseValue 1.5s ease-in-out infinite;
}

.dark .card-body .metric-grid-container .metric-item .metric-content div {
  color: #fff !important;
}

.dark .card-body .metric-grid-container .metric-item {
  background-color: var(--legend-bg-color) !important;
}

.dark .realtime-grid .realtime-metric-item div {
  background-color: #2c2c35 !important;
  color: #fff;
}

.dark .realtime-grid .realtime-metric-item .metric-header div {
  color: #8a8a98 !important;
  font-size: 12px;
}

.dark .first-metric-card .card-title-previous,
.dark .first-metric-card .card-title-current {
  color: #fff !important;
}

.dark .first-metric-card .card-title-current {
  font-size: 28px;
}

.dark .describe-style {
  font-size: 11px !important;
  color: #fff !important;
}

.dark .first-card-title .card-title-describe {
  color: #fff !important;
}

.dark .first-card-title .card-title-current {
  color: #fff !important;
}

.dark .line--NNUEF {
  background: linear-gradient(
    180deg,
    rgba(172, 187, 255, 0),
    #acbbff 13.81%,
    #acbbff 78.73%,
    rgba(172, 187, 255, 0) 92.63%
  );
  height: 156px;
  margin-left: 32px;
  width: 1px;
}

/* ==================== 网格和布局容器 ==================== */
.dark .card-body .grid-container-dark {
  background-color: var(--popover-bg-color) !important;
}

.dark .card-body .grid-container-dark span {
  color: var(--text-color);
}

.dark .card-body .grid-item-container {
  border-bottom: 1px solid var(--popover-bg-color);
}

.dark .card-body .organic-traffic h3,
.dark .card-body .Paid-traffic h3 {
  color: var(--title-text-color);
  font-size: 14px;
}

.dark .dashboard-content .section-metrics {
  margin-bottom: 16px !important;
}

.dark .card-body .text-center-title {
  display: flex;
  flex-flow: column;
  align-items: start;
  justify-content: center;
  border-radius: 7.6px;
}

.dark .aggregated-data-section .data-header {
  border-bottom: 1px solid rgb(56, 72, 105, 0.3);
}

.dark .trend-divider {
  background-color: rgba(56, 72, 105, 0.3);
}

/* ==================== 表单和输入组件 ==================== */
.dark .el-range-editor--small.el-input__wrapper {
  height: 32px;
  width: 266px;
}

.dark .el-range-editor--small.el-input__wrapper input {
  color: var(--title-color);
}

.dark .el-date-editor .el-range__icon {
  color: var(--title-color);
}

.dark .custom-date-picker .el-date-editor {
  box-shadow: none !important;
  background-color: var(--popover-bg-color);
}

.dark .card-body .card-body .form-label {
  color: var(--title-text-color);
}

/* ==================== 按钮组件 ==================== */
.dark .routerlink-btn {
  background-color: var(--primary-color);
  color: var(--title-color);
  border-radius: var(--btn-radius);
  height: 25px;
  border: 0;
}

.dark .action-buttons button {
  background-color: var(--shadow-color);
  color: var(--title-color);
  border-radius: var(--btn-radius);
  height: 25px;
}

.dark .toolbox-content button {
  background-color: var(--shadow-color) !important;
  border: 0px;
  color: var(--title-color);
  font-size: var(--title-text-size);
  font-weight: normal;
}

.dark .btn-dark-primary {
  border-radius: 4px;
  border: none;
  color: #fff;
  background: var(--button-primary-background);
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
}

.dark .btn-default-dark {
  border-radius: 4px;
  border: none;
  color: var(--button-default-color);
  background: var(--button-default-background);
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
}

.dark .time-span-selector div button {
  color: var(--title-color);
  border-radius: var(--btn-radius);
  border: 0;
  height: 33px;
  box-shadow: 0 0 0;
}

.dark .time-span-selector .btn-outline-primary {
  background: hsla(0, 0%, 100%, 0.2);
  color: var(--title-color);
  border-radius: var(--btn-radius);
  height: 33px;
  border: 0;
}

.dark .time-span-selector a {
  color: var(--title-color);
  font-size: 12px;
}

.dark .dark-button-border {
  border-radius: 0 12px !important;
  background: hsla(0, 0%, 100%, 0.2) !important;
}

/* ==================== 特殊功能组件 ==================== */
.dark .live-broadcast-font {
  color: #6b7280;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .live-broadcast-font-red {
  color: #6b7280;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .about-us-center span {
  color: #fff;
}

.dark .subtitlepanel-title,
.dark .videoplayer-title,
.dark .grid-title {
  color: #fff;
}

.dark .detail-report-content {
  color: #fff;
}

/* ==================== 时间选择和控制组件 ==================== */
.dark .card-body .time-selector-area {
  background-color: var(--popover-bg-color) !important;
}

.dark .card-body .quick-select-buttons {
  background-color: var(--bg-color) !important;
  border: 0;
}

.dark .card-body .time-selector-area .card {
  border: 0;
}

.dark .card-body .time-selector-area .card-header {
  border-bottom: 1px solid rgb(230, 230, 232, 0.1);
}

.dark .aggregated-data-section .control-buttons .time-span-selector {
  position: static;
}

/* ==================== 模态框和弹窗组件 ==================== */
.dark .modal-content {
  background-color: #fff;
}

.dark .modal .btn-close {
  background-color: #fff;
}

.dark .modal-content .modal-title,
.dark .modal-content .modal-body,
.dark .modal-content .modal-header {
  color: #000;
}

/* ==================== 特殊页面和区域 ==================== */
.dark .producttable .dropdown {
  position: relative;
}

.dark .card-body .room-list-panel .card .card-header {
  background-color: var(--bg-color) !important;
}

.dark .card-body .dragable-table-container .header-actions {
  position: relative;
}

.dark .card-header .demo-page-wrapper div {
  border: 0 !important;
}

.dark .review-report .card-title {
  color: var(--title-text-color);
  font-size: var(---title-text-size);
}
