# Service目录重构任务

## 背景

src/main/Service/utils 目录下文件命名混乱，既有Service又有Util后缀，需要重新整理。

## 目标

1. 合并3个Service文件为 `business/RoomService.ts`
2. 合并3个Util文件为 `utils/RoomUtils.ts`
3. 添加清晰的功能分区注释
4. 更新所有导入引用

## 计划步骤

1. ✅ 创建目录结构
2. ✅ 合并Service文件为RoomService.ts
3. ✅ 合并Util文件为RoomUtils.ts
4. ✅ 更新导入引用
5. ✅ 删除旧文件
6. ✅ 验证功能

## 重构结果

### 最终目录结构

```
src/main/Service/
├── business/
│   ├── RoomService.ts    # 合并了3个Service文件的功能
│   └── index.ts          # 统一导出
├── utils/
│   ├── RoomUtils.ts      # 合并了3个Util文件的功能
│   └── index.ts          # 统一导出
└── [其他Service文件保持不变]
```

### 验证结果

- ✅ TypeScript编译通过（main进程）
- ✅ 所有导入引用已更新
- ✅ 功能分区清晰，注释完善
- ✅ 代码整洁，符合DRY原则

## 收益

1. 减少文件数量：从6个文件合并为2个文件
2. 目录结构清晰：business层和utils层分离
3. 功能分区明确：通过注释分区组织代码
4. 维护效率提升：相关功能集中管理

## 文件合并映射

### business/RoomService.ts

- RoomDataService.ts (房间数据插入、批量处理)
- RoomListService.ts (房间列表查询)
- AccountMetricsService.ts (账户指标计算)

### utils/RoomUtils.ts

- FormatUtil.ts (格式化工具)
- MetricsUtil.ts (指标计算工具)
- RoomCoreDataUtil.ts (数据转换工具)
