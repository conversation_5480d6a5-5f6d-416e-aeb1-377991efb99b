<template>
  <el-dialog
    v-model="visible"
    title="自动导入Excel数据"
    width="900px"
    top="10vh"
    destroy-on-close
    class="auto-import-dialog"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 步骤指示器 -->
    <div class="steps-container">
      <el-steps :active="currentStep" align-center finish-status="success">
        <el-step title="输入URL" description="输入Excel文件下载链接" />
        <el-step title="下载并处理" description="自动下载、验证并导入数据" />
        <el-step title="完成" description="查看导入结果" />
      </el-steps>
    </div>

    <!-- 步骤 1: URL输入 -->
    <div v-if="currentStep === 0" class="step-content">
      <div class="url-input-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="bi bi-link-45deg"></i>
            输入Excel文件链接
          </h3>
          <p class="section-subtitle">请提供Excel文件的下载链接，系统将自动下载并导入数据</p>
        </div>

        <el-form ref="urlFormRef" :model="urlForm" :rules="urlRules" label-width="0">
          <el-form-item prop="url">
            <el-input
              v-model="urlForm.url"
              placeholder="https://example.com/data.xlsx"
              type="textarea"
              :rows="4"
              clearable
              class="url-input"
            >
              <template #prepend>
                <i class="bi bi-globe"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-form>

        <div class="tips-section">
          <el-alert type="info" :closable="false" show-icon>
            <template #title>
              <span class="tips-title">使用说明</span>
            </template>
            <template #default>
              <div class="tips-grid">
                <div class="tip-item">
                  <i class="bi bi-file-earmark-excel text-success"></i>
                  <span>支持 .xlsx 和 .xls 格式</span>
                </div>
                <div class="tip-item">
                  <i class="bi bi-hdd text-warning"></i>
                  <span>文件大小不超过 10MB</span>
                </div>
                <div class="tip-item">
                  <i class="bi bi-shield-check text-primary"></i>
                  <span>支持 HTTP 和 HTTPS 协议</span>
                </div>
                <div class="tip-item">
                  <i class="bi bi-wifi text-info"></i>
                  <span>确保网络连接稳定</span>
                </div>
                <div class="tip-item">
                  <i class="bi bi-arrow-clockwise text-success"></i>
                  <span>自动验证和导入数据</span>
                </div>
                <div class="tip-item">
                  <i class="bi bi-trash text-secondary"></i>
                  <span>临时文件自动清理</span>
                </div>
              </div>
            </template>
          </el-alert>
        </div>
      </div>
    </div>

    <!-- 步骤 2: 下载并处理 -->
    <div v-if="currentStep === 1" class="step-content">
      <div class="unified-progress-section">
        <!-- 当前阶段指示 -->
        <div class="stage-indicator">
          <div class="stage-item" :class="{ active: currentStage === 'downloading' }">
            <div class="stage-icon">
              <i class="bi bi-cloud-download-fill"></i>
            </div>
            <span class="stage-text">下载文件</span>
          </div>
          <div class="stage-connector"></div>
          <div class="stage-item" :class="{ active: currentStage === 'processing' }">
            <div class="stage-icon">
              <i class="bi bi-gear-fill"></i>
            </div>
            <span class="stage-text">处理数据</span>
          </div>
        </div>

        <!-- 主要进度显示 -->
        <div class="main-progress-area">
          <h3 class="progress-title">{{ currentStageTitle }}</h3>
          <p class="progress-subtitle">{{ currentStageMessage }}</p>

          <div class="progress-content">
            <!-- 主进度条 -->
            <el-progress
              :percentage="mainProgressPercentage"
              :status="mainProgressPercentage === 100 ? 'success' : undefined"
              :stroke-width="16"
              text-inside
              class="main-progress"
            />

            <!-- 详细信息 -->
            <div class="progress-details">
              <!-- 下载阶段信息 -->
              <div v-if="currentStage === 'downloading'" class="stage-details">
                <div class="detail-grid">
                  <div class="detail-item">
                    <span class="detail-label">状态:</span>
                    <span class="detail-value">{{
                      downloadProgress.stage === 'downloading' ? '下载中' : '验证中'
                    }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">进度:</span>
                    <span class="detail-value">{{ downloadProgress.percentage }}%</span>
                  </div>
                </div>
              </div>

              <!-- 处理阶段信息 -->
              <div v-if="currentStage === 'processing'" class="stage-details">
                <div class="detail-grid">
                  <div class="detail-item">
                    <span class="detail-label">已处理:</span>
                    <span class="detail-value"
                      >{{ processProgress.processedRows }}/{{ processProgress.totalRows }}</span
                    >
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">有效记录:</span>
                    <span class="detail-value text-success">{{ processProgress.validRows }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">错误记录:</span>
                    <span class="detail-value text-danger">{{ processProgress.errorRows }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">已导入:</span>
                    <span class="detail-value text-primary">{{
                      processProgress.importedRows
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 步骤 3: 完成结果 -->
    <div v-if="currentStep === 2">
      <div class="result-area">
        <el-result
          :icon="processResult?.successCount > 0 ? 'success' : 'warning'"
          :title="processResult?.successCount > 0 ? '导入完成' : '导入失败'"
          :sub-title="`成功导入 ${processResult?.successCount || 0} 条记录，失败 ${processResult?.failedCount || 0} 条记录`"
        >
          <template #extra>
            <!-- 统计信息 -->
            <div class="result-stats">
              <div class="stat-card info">
                <div class="stat-number">{{ processResult?.totalRows || 0 }}</div>
                <div class="stat-label">总记录数</div>
              </div>
              <div class="stat-card success">
                <div class="stat-number">{{ processResult?.successCount || 0 }}</div>
                <div class="stat-label">成功导入</div>
              </div>
              <div class="stat-card danger">
                <div class="stat-number">{{ processResult?.failedCount || 0 }}</div>
                <div class="stat-label">导入失败</div>
              </div>
            </div>

            <!-- 错误详情 -->
            <div v-if="processResult?.sampleErrors && processResult.sampleErrors.length > 0">
              <el-button type="text" @click="showErrorDetails = !showErrorDetails">
                {{ showErrorDetails ? '隐藏' : '查看' }}错误详情
              </el-button>
              <el-collapse v-if="showErrorDetails" style="margin-top: 15px; text-align: left">
                <el-collapse-item title="错误记录详情" name="errors">
                  <el-table :data="processResult.sampleErrors" size="small" max-height="200" border>
                    <el-table-column prop="rowIndex" label="行号" width="80" />
                    <el-table-column prop="errors" label="错误信息">
                      <template #default="{ row }">
                        <el-tag
                          v-for="error in row.errors"
                          :key="error"
                          type="danger"
                          size="small"
                          style="margin-right: 5px"
                        >
                          {{ error }}
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-collapse-item>
              </el-collapse>
            </div>

            <div style="margin-top: 20px">
              <el-button type="primary" @click="handleClose">完成</el-button>
            </div>
          </template>
        </el-result>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="currentStep !== 2" @click="handleClose">取消</el-button>
        <el-button
          v-if="currentStep === 0"
          type="primary"
          :disabled="!urlForm.url.trim()"
          :loading="downloading"
          @click="handleStartDownload"
        >
          {{ downloading ? '下载中...' : '开始下载' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const currentStep = ref(0)
const downloading = ref(false)
const processing = ref(false)
const showErrorDetails = ref(false)

// 当前阶段状态
const currentStage = ref<'downloading' | 'processing'>('downloading')

// URL表单
const urlForm = ref({
  url: ''
})

const urlFormRef = ref()

// URL验证规则
const urlRules = {
  url: [
    { required: true, message: '请输入Excel文件URL', trigger: 'blur' },
    {
      validator: (_rule: any, value: string, callback: any) => {
        if (!value) {
          callback()
          return
        }
        try {
          const url = new URL(value)
          if (!['http:', 'https:'].includes(url.protocol)) {
            callback(new Error('仅支持HTTP和HTTPS协议'))
            return
          }
          callback()
        } catch {
          callback(new Error('请输入有效的URL'))
        }
      },
      trigger: 'blur'
    }
  ]
}

// 下载进度
const downloadProgress = ref({
  stage: 'waiting',
  message: '准备下载...',
  percentage: 0
})

// 处理进度
const processProgress = ref({
  processedRows: 0,
  totalRows: 0,
  percentage: 0,
  stage: 'parsing',
  currentBatch: 0,
  totalBatches: 0,
  message: '准备处理...',
  validRows: 0,
  errorRows: 0,
  importedRows: 0
})

// 处理结果
const processResult = ref<any>(null)

// 计算属性
const currentStageTitle = computed(() => {
  return currentStage.value === 'downloading' ? '正在下载Excel文件' : '正在处理Excel数据'
})

const currentStageMessage = computed(() => {
  return currentStage.value === 'downloading'
    ? downloadProgress.value.message
    : processProgress.value.message
})

const mainProgressPercentage = computed(() => {
  return currentStage.value === 'downloading'
    ? downloadProgress.value.percentage
    : processProgress.value.percentage
})

// 方法
const handleClose = (): void => {
  // 如果正在下载或处理，取消任务
  if (downloading.value || processing.value) {
    window.electron.ipcRenderer.invoke('excel:cancelProcess')
  }

  visible.value = false
  // 重置状态
  nextTick(() => {
    currentStep.value = 0
    currentStage.value = 'downloading'
    downloading.value = false
    processing.value = false
    showErrorDetails.value = false
    urlForm.value.url = ''
    downloadProgress.value = {
      stage: 'waiting',
      message: '准备下载...',
      percentage: 0
    }
    processProgress.value = {
      processedRows: 0,
      totalRows: 0,
      percentage: 0,
      stage: 'parsing',
      currentBatch: 0,
      totalBatches: 0,
      message: '准备处理...',
      validRows: 0,
      errorRows: 0,
      importedRows: 0
    }
    processResult.value = null
  })
}

const handleStartDownload = async (): Promise<void> => {
  // 验证表单
  if (!urlFormRef.value) return

  const valid = await urlFormRef.value.validate().catch(() => false)
  if (!valid) return

  downloading.value = true
  currentStep.value = 1

  try {
    console.log('发送下载请求到主进程:', urlForm.value.url)

    // 发送开始下载请求
    window.electron.ipcRenderer.send('excel:downloadFromUrl', urlForm.value.url)
  } catch (error: any) {
    console.error('发送下载请求失败:', error)
    ElMessage.error(`发送下载请求失败：${error.message}`)
    downloading.value = false
    currentStep.value = 0
  }
}

// 事件处理函数
const handleDownloadStarted = (_event: any, data: any): void => {
  console.log('下载开始:', data)
  downloadProgress.value.message = data.message || '开始下载...'
}

const handleDownloadProgress = (_event: any, progress: any): void => {
  console.log('下载进度:', progress)
  downloadProgress.value = { ...downloadProgress.value, ...progress }
}

const handleDownloadComplete = (_event: any, data: any): void => {
  console.log('下载完成:', data)
  console.log('接收到的文件路径:', data.filePath)

  if (!data.filePath) {
    console.error('下载完成但文件路径为空')
    ElMessage.error('下载完成但文件路径为空')
    downloading.value = false
    currentStep.value = 0
    return
  }

  downloading.value = false
  // 切换到处理阶段，但保持在步骤1
  currentStage.value = 'processing'

  // 开始处理Excel文件（使用自动导入专用事件）
  processing.value = true
  console.log('发送自动导入处理请求，文件路径:', data.filePath)
  window.electron.ipcRenderer.send('excel:autoImportProcess', data.filePath)
}

const handleDownloadError = (_event: any, error: any): void => {
  console.error('下载失败:', error)
  downloading.value = false
  currentStep.value = 0
  ElMessage.error(`下载失败：${error.message}`)
}

const handleAutoImportStarted = (_event: any, data: any): void => {
  console.log('自动导入处理开始:', data)
  processProgress.value.message = data.message || '开始处理...'
}

const handleAutoImportProgress = (_event: any, progress: any): void => {
  console.log('自动导入处理进度:', progress)
  processProgress.value = { ...processProgress.value, ...progress }
}

const handleAutoImportComplete = (_event: any, result: any): void => {
  console.log('自动导入处理完成:', result)
  processing.value = false
  currentStep.value = 2 // 跳转到完成步骤
  processResult.value = result

  // 通知父组件刷新数据
  emit('success')

  ElMessage.success(`自动导入完成！成功导入 ${result.successCount} 条记录`)
}

const handleAutoImportError = (_event: any, error: any): void => {
  console.error('自动导入处理失败:', error)
  processing.value = false
  currentStep.value = 0
  ElMessage.error(`自动导入失败：${error.message}`)
}

// 组件挂载和卸载
onMounted(() => {
  // 监听下载相关事件
  window.electron.ipcRenderer.on('excel:downloadStarted', handleDownloadStarted)
  window.electron.ipcRenderer.on('excel:downloadProgress', handleDownloadProgress)
  window.electron.ipcRenderer.on('excel:downloadComplete', handleDownloadComplete)
  window.electron.ipcRenderer.on('excel:downloadError', handleDownloadError)

  // 监听自动导入处理相关事件
  window.electron.ipcRenderer.on('excel:autoImportStarted', handleAutoImportStarted)
  window.electron.ipcRenderer.on('excel:autoImportProgress', handleAutoImportProgress)
  window.electron.ipcRenderer.on('excel:autoImportComplete', handleAutoImportComplete)
  window.electron.ipcRenderer.on('excel:autoImportError', handleAutoImportError)
})

onUnmounted(() => {
  // 移除事件监听
  window.electron.ipcRenderer.removeAllListeners('excel:downloadStarted')
  window.electron.ipcRenderer.removeAllListeners('excel:downloadProgress')
  window.electron.ipcRenderer.removeAllListeners('excel:downloadComplete')
  window.electron.ipcRenderer.removeAllListeners('excel:downloadError')
  window.electron.ipcRenderer.removeAllListeners('excel:autoImportStarted')
  window.electron.ipcRenderer.removeAllListeners('excel:autoImportProgress')
  window.electron.ipcRenderer.removeAllListeners('excel:autoImportComplete')
  window.electron.ipcRenderer.removeAllListeners('excel:autoImportError')
})
</script>

<style scoped>
.auto-import-dialog {
  --el-dialog-border-radius: 12px;
}

.steps-container {
  margin-bottom: 40px;
  padding: 0 20px;
}

.step-content {
  padding: 0 20px;
  min-height: 400px;
}

/* URL输入区域样式 */
.url-input-section {
  max-width: 100%;
}

.section-header {
  text-align: center;
  margin-bottom: 30px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--bs-body-color);
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.section-subtitle {
  font-size: 14px;
  color: var(--bs-secondary-color);
  margin: 0;
}

.url-input {
  font-size: 14px;
}

.tips-section {
  margin-top: 25px;
}

.tips-title {
  font-weight: 600;
  font-size: 14px;
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: var(--bs-body-color);
}

.tip-item i {
  font-size: 14px;
  width: 16px;
  text-align: center;
}

/* 进度区域样式 */
.progress-section {
  text-align: center;
  padding: 40px 20px;
}

.progress-header {
  margin-bottom: 40px;
}

.progress-icon {
  font-size: 48px;
  color: var(--bs-primary);
  margin-bottom: 16px;
}

.progress-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--bs-body-color);
  margin: 0 0 8px 0;
}

.progress-subtitle {
  font-size: 14px;
  color: var(--bs-secondary-color);
  margin: 0;
}

.progress-content {
  max-width: 500px;
  margin: 0 auto;
}

.main-progress {
  margin-bottom: 25px;
}

.progress-info {
  display: flex;
  justify-content: space-around;
  background: var(--bs-gray-100);
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;
}

[data-bs-theme='dark'] .progress-info {
  background: var(--bs-gray-800);
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: var(--bs-secondary-color);
  font-weight: 500;
}

.info-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--bs-body-color);
}

/* 统一进度区域样式 */
.unified-progress-section {
  padding: 30px 20px;
  text-align: center;
}

.stage-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.stage-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.stage-item.active {
  opacity: 1;
  transform: scale(1.1);
}

.stage-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--bs-gray-200);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: var(--bs-secondary-color);
  transition: all 0.3s ease;
}

.stage-item.active .stage-icon {
  background: var(--bs-primary);
  color: white;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

[data-bs-theme='dark'] .stage-icon {
  background: var(--bs-gray-700);
}

[data-bs-theme='dark'] .stage-item.active .stage-icon {
  background: var(--bs-primary);
}

.stage-text {
  font-size: 12px;
  font-weight: 500;
  color: var(--bs-secondary-color);
  transition: color 0.3s ease;
}

.stage-item.active .stage-text {
  color: var(--bs-body-color);
  font-weight: 600;
}

.stage-connector {
  flex: 1;
  height: 2px;
  background: var(--bs-border-color);
  margin: 0 20px;
  position: relative;
  top: -20px;
}

.main-progress-area {
  max-width: 600px;
  margin: 0 auto;
}

.progress-details {
  margin-top: 30px;
}

.stage-details {
  background: var(--bs-gray-100);
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
}

[data-bs-theme='dark'] .stage-details {
  background: var(--bs-gray-800);
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.detail-label {
  font-size: 12px;
  color: var(--bs-secondary-color);
  font-weight: 500;
}

.detail-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--bs-body-color);
}

.progress-stats {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
}

.stat-label {
  font-size: 12px;
  color: var(--bs-secondary-color);
  margin-bottom: 5px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
}

.text-success {
  color: var(--bs-success);
}

.text-danger {
  color: var(--bs-danger);
}

.text-primary {
  color: var(--bs-primary);
}

.result-stats {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.stat-card {
  background: var(--bs-gray-100);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  min-width: 100px;
  border: 2px solid var(--bs-border-color);
}

[data-bs-theme='dark'] .stat-card {
  background: var(--bs-gray-800);
  border-color: var(--bs-border-color);
}

.stat-card.info {
  background: var(--bs-info-bg-subtle);
  border-color: var(--bs-info);
}

.stat-card.success {
  background: var(--bs-success-bg-subtle);
  border-color: var(--bs-success);
}

.stat-card.danger {
  background: var(--bs-danger-bg-subtle);
  border-color: var(--bs-danger);
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-card.success .stat-number {
  color: var(--bs-success);
}

.stat-card.danger .stat-number {
  color: var(--bs-danger);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
