.vue-devtools-frame[data-v-399f5059] {
  position: fixed;
  z-index: 2147483645;
}
.vue-devtools-frame[data-v-399f5059] iframe {
  width: 100%;
  height: 100%;
  outline: none;
  background: var(--vue-devtools-widget-bg);
  border: 1px solid rgba(125, 125, 125, 0.2);
  border-radius: 10px;
}
.vue-devtools-frame.view-mode-xs[data-v-399f5059] {
  width: 400px !important;
  height: 80px !important;
}
.vue-devtools-frame.view-mode-fullscreen[data-v-399f5059] {
  width: 100vw !important;
  height: 100vh !important;
  z-index: 1 !important;
  bottom: 0 !important;
  transform: none !important;
}
.vue-devtools-frame.view-mode-fullscreen[data-v-399f5059] iframe {
  border-radius: 0 !important;
}
.vue-devtools-resize--horizontal[data-v-399f5059] {
  position: absolute;
  left: 6px;
  right: 6px;
  height: 10px;
  margin: -5px 0;
  cursor: ns-resize;
  border-radius: 5px;
}
.vue-devtools-resize--vertical[data-v-399f5059] {
  position: absolute;
  top: 6px;
  bottom: 0;
  width: 10px;
  margin: 0 -5px;
  cursor: ew-resize;
  border-radius: 5px;
}
.vue-devtools-resize-corner[data-v-399f5059] {
  position: absolute;
  width: 14px;
  height: 14px;
  margin: -6px;
  border-radius: 6px;
}
.vue-devtools-resize[data-v-399f5059]:hover {
  background: #7d7d7d1a;
}
.vue-devtools__anchor[data-v-640ec535] {
  position: fixed;
  z-index: 2147483645;
  transform-origin: center center;
  transform: translate(-50%, -50%) rotate(0);
}
.vue-devtools__anchor.reduce-motion[data-v-640ec535],
.vue-devtools__anchor.reduce-motion[data-v-640ec535] * {
  transition: none !important;
  animation: none !important;
}
.vue-devtools__anchor.fullscreen[data-v-640ec535] {
  transform: none !important;
  left: 0 !important;
}
.vue-devtools__anchor-btn[data-v-640ec535] {
  border-radius: 100%;
  border-width: 0;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0.8;
  transition: opacity 0.2s ease-in-out;
}
.vue-devtools__anchor-btn[data-v-640ec535]:hover {
  opacity: 1;
}
.vue-devtools__anchor-btn svg[data-v-640ec535] {
  width: 14px;
  height: 14px;
}
.vue-devtools__anchor-btn.active[data-v-640ec535] {
  cursor: pointer;
}
.vue-devtools__anchor .panel-entry-btn[data-v-640ec535] {
  cursor: pointer;
  flex: none;
}
.vue-devtools__anchor--vertical .panel-entry-btn[data-v-640ec535] {
  transform: rotate(-90deg);
}
.vue-devtools__anchor--vertical .vue-devtools__panel[data-v-640ec535] {
  transform: translate(-50%, -50%) rotate(90deg);
  box-shadow: 2px -2px 8px var(--vue-devtools-widget-shadow);
}
.vue-devtools__anchor--hide .vue-devtools__panel[data-v-640ec535] {
  max-width: 32px;
  padding: 2px 0;
}
.vue-devtools__anchor--hide .vue-devtools__panel-content[data-v-640ec535] {
  opacity: 0;
}
.vue-devtools__anchor--glowing[data-v-640ec535] {
  position: absolute;
  left: 0;
  top: 0;
  transform: translate(-50%, -50%);
  width: 160px;
  height: 160px;
  opacity: 0;
  transition: all 1s ease;
  pointer-events: none;
  z-index: -1;
  border-radius: 9999px;
  background-image: linear-gradient(45deg, #00dc82, #36e4da, #0047e1);
  filter: blur(60px);
}
.vue-devtools__anchor:hover .vue-devtools__anchor--glowing[data-v-640ec535] {
  opacity: 0.6;
}
.vue-devtools__panel[data-v-640ec535] {
  position: absolute;
  left: 0;
  top: 0;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: flex-start;
  overflow: hidden;
  align-items: center;
  gap: 2px;
  height: 30px;
  padding: 4px 4px 4px 5px;
  box-sizing: border-box;
  border: 1px solid var(--vue-devtools-widget-border);
  border-radius: 20px;
  background-color: var(--vue-devtools-widget-bg);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  color: var(--vue-devtools-widget-fg);
  box-shadow: 2px 2px 8px var(--vue-devtools-widget-shadow);
  -webkit-user-select: none;
  user-select: none;
  max-width: 150px;
  transition:
    max-width 0.4s ease,
    padding 0.5s ease,
    transform 0.3s ease,
    all 0.4s ease;
}
.vue-devtools__panel-content[data-v-640ec535] {
  transition: opacity 0.4s ease;
}
.vue-devtools__panel-divider[data-v-640ec535] {
  border-left: 1px solid rgba(136, 136, 136, 0.2);
  width: 1px;
  height: 10px;
}
@keyframes blink-640ec535 {
  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.6;
  }
  to {
    opacity: 0.2;
  }
}
@media print {
  #vue-devtools-anchor[data-v-640ec535] {
    display: none;
  }
}
