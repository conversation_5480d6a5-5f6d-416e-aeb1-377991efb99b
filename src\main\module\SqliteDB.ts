import * as path from 'path'
import * as fs from 'fs'
import { Sequelize, Transaction, QueryTypes } from 'sequelize'
import {
  RoomCoreDataModel,
  initRoomCoreDataModel,
  OrderModel,
  initOrderModel,
  ProductModel,
  initProductModel,
  EnterModel,
  initEnterModel
} from './model'

/**
 * SQLite数据库操作类 - 使用 Sequelize ORM
 * 采用单例模式，提供数据库连接管理和基本的数据操作功能
 * 使用连接池、事务等 Sequelize 最佳实践
 */
class SqliteDB {
  /**
   * 默认数据库文件路径
   */
  private static readonly DB_PATH = 'data/live_rooms.db'

  /**
   * Sequelize 实例（单例）
   */
  private static sequelizeInstance: Sequelize | null = null

  /**
   * RoomCoreData 模型实例
   */
  private static roomCoreDataModel: typeof RoomCoreDataModel | null = null

  /**
   * Order 模型实例
   */
  private static orderModel: typeof OrderModel | null = null

  /**
   * Product 模型实例
   */
  private static productModel: typeof ProductModel | null = null

  /**
   * Enter 模型实例
   */
  private static enterModel: typeof EnterModel | null = null

  /**
   * 获取 Sequelize 实例（单例模式）
   * 如果实例不存在则创建新实例，配置连接池等最佳实践
   * @returns Sequelize 实例
   */
  private static getSequelizeInstance(): Sequelize {
    if (!this.sequelizeInstance) {
      const absolutePath = path.resolve(this.DB_PATH)
      console.log('创建 Sequelize 数据库连接:', absolutePath)

      // 确保数据库目录存在
      const dbDir = path.dirname(absolutePath)
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true })
        console.log('创建数据库目录:', dbDir)
      }

      // 创建 Sequelize 实例，配置连接池
      this.sequelizeInstance = new Sequelize({
        dialect: 'sqlite',
        storage: absolutePath,
        logging: false, // 生产环境建议关闭，开发时可设为 console.log
        pool: {
          max: 5, // 连接池最大连接数
          min: 0, // 连接池最小连接数
          acquire: 30000, // 获取连接的超时时间
          idle: 10000 // 连接空闲超时时间
        },
        // SQLite 特定配置
        dialectOptions: {
          // 启用外键约束
          // 设置超时时间
        },
        // 事务隔离级别
        isolationLevel: Transaction.ISOLATION_LEVELS.SERIALIZABLE,
        // 重试配置
        retry: {
          max: 3
        }
      })

      console.log('Sequelize 实例创建完成')
    }

    return this.sequelizeInstance
  }

  /**
   * 获取 RoomCoreData 模型（单例模式）
   * 如果模型不存在则初始化模型定义
   * @returns RoomCoreData 模型类
   */
  public static getRoomCoreDataModel(): typeof RoomCoreDataModel {
    if (!this.roomCoreDataModel) {
      const sequelize = this.getSequelizeInstance()
      // 使用导入的初始化函数
      this.roomCoreDataModel = initRoomCoreDataModel(sequelize)
      console.log('RoomCoreData 模型初始化完成')
    }
    return this.roomCoreDataModel
  }

  /**
   * 获取 Order 模型（单例模式）
   * 如果模型不存在则初始化模型定义
   * @returns Order 模型类
   */
  public static getOrderModel(): typeof OrderModel {
    if (!this.orderModel) {
      const sequelize = this.getSequelizeInstance()
      // 使用导入的初始化函数
      this.orderModel = initOrderModel(sequelize)
      console.log('Order 模型初始化完成')
    }
    return this.orderModel
  }

  /**
   * 获取 Product 模型（单例模式）
   * 如果模型不存在则初始化模型定义
   * @returns Product 模型类
   */
  public static getProductModel(): typeof ProductModel {
    if (!this.productModel) {
      const sequelize = this.getSequelizeInstance()
      // 使用导入的初始化函数
      this.productModel = initProductModel(sequelize)
      console.log('Product 模型初始化完成')
    }
    return this.productModel
  }

  /**
   * 获取 Enter 模型（单例模式）
   * 如果模型不存在则初始化模型定义
   * @returns Enter 模型类
   */
  public static getEnterModel(): typeof EnterModel {
    if (!this.enterModel) {
      const sequelize = this.getSequelizeInstance()
      // 使用导入的初始化函数
      this.enterModel = initEnterModel(sequelize)
      console.log('Enter 模型初始化完成')
    }
    return this.enterModel
  }

  // 获取transaction
  public static async getTransaction(): Promise<Transaction> {
    const sequelize = this.getSequelizeInstance()
    return await sequelize.transaction()
  }

  /**
   * 初始化数据库连接和表结构
   *
   * 工作流程：
   * 1. 检测所有表的状态（存在性和结构差异）
   * 2. 并行处理缺失表的创建（清理孤立索引 + sync创建）
   * 3. 串行处理表结构迁移（避免并发冲突）
   *
   * @returns 初始化结果：true-成功，false-失败
   */
  public static async initializeDatabase(): Promise<boolean> {
    try {
      const sequelize = this.getSequelizeInstance()

      // 初始化模型实例
      const roomCoreDataModel = this.getRoomCoreDataModel()
      const orderModel = this.getOrderModel()
      const productModel = this.getProductModel()
      const enterModel = this.getEnterModel()

      await sequelize.authenticate()
      console.log('✓ 数据库连接成功')

      // 表配置映射
      const tableConfigs = [
        { tableName: 'room_core_data', model: roomCoreDataModel },
        { tableName: 'orders', model: orderModel },
        { tableName: 'products', model: productModel },
        { tableName: 'enter', model: enterModel }
      ]

      // 并行检测所有表状态，提高性能
      const tableStatusResults = await Promise.all(
        tableConfigs.map((config) => this.analyzeTableStatus(sequelize, config))
      )

      // 分类处理结果
      const missingTables = tableStatusResults.filter((result) => result.status === 'missing')
      const migrationTables = tableStatusResults.filter((result) => result.status === 'migration')
      const normalTables = tableStatusResults.filter((result) => result.status === 'normal')

      console.log(
        `📊 表状态统计: 正常 ${normalTables.length}，缺失 ${missingTables.length}，需迁移 ${migrationTables.length}`
      )

      // 处理缺失表：并行清理索引 + 统一创建表
      if (missingTables.length > 0) {
        await this.handleMissingTables(sequelize, missingTables)
      }

      // 处理结构迁移：串行执行避免并发冲突
      if (migrationTables.length > 0) {
        await this.handleTableMigrations(sequelize, migrationTables)
      }

      // 全局索引清理和更新（包括"正常"状态的表）
      await this.globalIndexCleanupAndUpdate(sequelize, tableConfigs)

      console.log('✓ 数据库初始化完成')
      return true
    } catch (error: any) {
      console.error('✗ 数据库初始化失败:', error.message)
      return false
    }
  }

  /**
   * 分析单个表的状态
   *
   * @param sequelize Sequelize实例
   * @param config 表配置 {tableName, model}
   * @returns 表状态结果 {status: 'missing'|'migration'|'normal', tableName, model, details?}
   */
  private static async analyzeTableStatus(
    sequelize: Sequelize,
    config: { tableName: string; model: any }
  ): Promise<{
    status: 'missing' | 'migration' | 'normal'
    tableName: string
    model: any
    details?: string
  }> {
    const { tableName, model } = config

    try {
      // 检查表是否存在
      const tableExists = await sequelize
        .getQueryInterface()
        .showAllTables()
        .then((tables) => tables.includes(tableName))

      if (!tableExists) {
        return { status: 'missing', tableName, model }
      }

      // 表存在，检查结构差异
      const structureDiff = await this.compareTableStructure(sequelize, tableName, model)

      if (structureDiff.needsMigration) {
        return {
          status: 'migration',
          tableName,
          model,
          details: structureDiff.reason
        }
      }

      return { status: 'normal', tableName, model }
    } catch (error: any) {
      console.warn(`⚠️ 检查表 ${tableName} 时出错，标记为缺失:`, error.message)
      return { status: 'missing', tableName, model }
    }
  }

  /**
   * 比较表结构差异
   *
   * @param sequelize Sequelize实例
   * @param tableName 表名
   * @param model 模型定义
   * @returns 结构差异分析结果
   */
  private static async compareTableStructure(
    sequelize: Sequelize,
    tableName: string,
    model: any
  ): Promise<{ needsMigration: boolean; reason?: string }> {
    // 获取数据库表结构信息
    const dbColumns: Array<{
      name: string
      type: string
      notnull: number
    }> = await sequelize.query(`PRAGMA table_info(${tableName})`, { type: QueryTypes.SELECT })

    const modelAttributes = model.getAttributes()
    const dbFieldNames = dbColumns.map((col) => col.name)
    const modelFieldNames = Object.keys(modelAttributes)

    // 字段数量检查
    if (modelFieldNames.length !== dbFieldNames.length) {
      return {
        needsMigration: true,
        reason: `字段数量不一致(模型:${modelFieldNames.length} vs 数据库:${dbFieldNames.length})`
      }
    }

    // 字段名检查
    const missingFields = modelFieldNames.filter((field) => !dbFieldNames.includes(field))
    const extraFields = dbFieldNames.filter((field) => !modelFieldNames.includes(field))

    if (missingFields.length > 0 || extraFields.length > 0) {
      const details: string[] = []
      if (missingFields.length > 0) details.push(`缺少字段:${missingFields.join(',')}`)
      if (extraFields.length > 0) details.push(`多余字段:${extraFields.join(',')}`)

      return {
        needsMigration: true,
        reason: details.join('; ')
      }
    }

    // 字段属性检查（空值约束）
    for (const [fieldName, attr] of Object.entries(modelAttributes)) {
      const dbCol = dbColumns.find((c) => c.name === fieldName)
      if (!dbCol) continue

      const modelNotNull = (attr as any).allowNull === false
      const dbNotNull = dbCol.notnull === 1

      if (modelNotNull !== dbNotNull) {
        return {
          needsMigration: true,
          reason: `字段${fieldName}空值约束不一致`
        }
      }
    }

    return { needsMigration: false }
  }

  /**
   * 处理缺失表：清理孤立索引 + 创建表
   *
   * @param sequelize Sequelize实例
   * @param missingTables 缺失的表配置数组
   */
  private static async handleMissingTables(
    sequelize: Sequelize,
    missingTables: Array<{ tableName: string; model: any }>
  ): Promise<void> {
    const tableNames = missingTables.map((t) => t.tableName)
    console.log(`🔧 创建缺失表: ${tableNames.join(', ')}`)

    // 并行清理所有缺失表的孤立索引
    await Promise.all(
      missingTables.map(({ tableName, model }) =>
        this.cleanOrphanedIndexes(sequelize, tableName, model)
      )
    )

    // 统一创建所有表（Sequelize会自动跳过已存在的表）
    await sequelize.sync({
      alter: false,
      force: false,
      logging: false // 精简输出，不显示SQL
    })

    console.log(`✓ 已创建 ${missingTables.length} 个表`)
  }

  /**
   * 清理指定表的孤立索引
   *
   * @param sequelize Sequelize实例
   * @param tableName 表名
   * @param model 表模型
   */
  private static async cleanOrphanedIndexes(
    sequelize: Sequelize,
    tableName: string,
    model: any
  ): Promise<void> {
    if (!model.options.indexes) return

    try {
      // 获取所有现存索引
      const allIndexes = (await sequelize.query(
        `SELECT name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'`,
        { type: QueryTypes.SELECT }
      )) as Array<{ name: string }>

      const existingIndexNames = allIndexes.map((idx) => idx.name)

      // 删除与该表相关的孤立索引
      const orphanedIndexes = model.options.indexes
        .map((idx: any) => idx.name)
        .filter((name: string) => name && existingIndexNames.includes(name))

      if (orphanedIndexes.length > 0) {
        await Promise.all(
          orphanedIndexes.map((indexName: string) =>
            sequelize
              .query(`DROP INDEX IF EXISTS \`${indexName}\``)
              .catch((error) => console.warn(`⚠️ 删除索引${indexName}失败:`, error.message))
          )
        )
        console.log(`🧹 已清理表 ${tableName} 的 ${orphanedIndexes.length} 个孤立索引`)
      }
    } catch (error: any) {
      console.warn(`⚠️ 清理表 ${tableName} 索引时出错:`, error.message)
    }
  }

  /**
   * 处理表结构迁移
   *
   * @param sequelize Sequelize实例
   * @param migrationTables 需要迁移的表配置数组
   */
  private static async handleTableMigrations(
    sequelize: Sequelize,
    migrationTables: Array<{ tableName: string; model: any; details?: string }>
  ): Promise<void> {
    console.log(`🔄 开始结构迁移: ${migrationTables.map((t) => t.tableName).join(', ')}`)

    // 串行执行迁移，避免数据库锁冲突
    for (const { tableName, model, details } of migrationTables) {
      console.log(`⚙️ 迁移表 ${tableName}: ${details}`)

      try {
        await this.autoMigrateTable(sequelize, model, tableName)
        console.log(`✓ 表 ${tableName} 迁移完成`)
      } catch (error: any) {
        console.error(`✗ 表 ${tableName} 迁移失败:`, error.message)
        throw error // 迁移失败应该中断整个初始化
      }
    }
  }

  /**
   * 全局索引清理和更新
   * 先收集所有模型定义的索引，然后全局清理不需要的索引，最后创建缺失的索引
   *
   * @param sequelize Sequelize实例
   * @param tableConfigs 表配置数组
   */
  private static async globalIndexCleanupAndUpdate(
    sequelize: Sequelize,
    tableConfigs: Array<{ tableName: string; model: any }>
  ): Promise<void> {
    console.log('🔍 开始全局索引清理和更新...')

    try {
      // 1. 收集所有模型定义的索引
      const allModelIndexes = new Set<string>()
      const tableIndexMap = new Map<string, Array<any>>()

      for (const { tableName, model } of tableConfigs) {
        if (model.options.indexes && model.options.indexes.length > 0) {
          tableIndexMap.set(tableName, model.options.indexes)
          for (const idx of model.options.indexes) {
            if (idx.name) {
              allModelIndexes.add(idx.name)
            }
          }
        }
      }

      console.log(`📋 模型定义的索引总数: ${allModelIndexes.size}`)

      // 2. 获取数据库中现有的所有索引
      const existingIndexes: Array<{ name: string; tbl_name: string }> = await sequelize.query(
        `SELECT name, tbl_name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'`,
        { type: QueryTypes.SELECT }
      )

      console.log(`📋 数据库现有索引总数: ${existingIndexes.length}`)

      // 3. 找出需要删除的孤儿索引（数据库中有但模型中没有定义的）
      const orphanedIndexes = existingIndexes.filter((idx) => !allModelIndexes.has(idx.name))

      // 4. 删除孤儿索引
      if (orphanedIndexes.length > 0) {
        console.log(`🗑️ 发现 ${orphanedIndexes.length} 个孤儿索引，开始清理...`)

        for (const idx of orphanedIndexes) {
          try {
            await sequelize.query(`DROP INDEX IF EXISTS \`${idx.name}\``)
            console.log(`🗑️ 已删除孤儿索引: ${idx.name} (来自表: ${idx.tbl_name})`)
          } catch (error: any) {
            console.warn(`⚠️ 删除索引 ${idx.name} 失败:`, error.message)
          }
        }
      } else {
        console.log('✅ 没有发现孤儿索引')
      }

      // 5. 为每个表创建缺失的索引
      const existingIndexNames = new Set(existingIndexes.map((idx) => idx.name))
      let totalCreated = 0

      for (const { tableName } of tableConfigs) {
        const tableIndexes = tableIndexMap.get(tableName)
        if (!tableIndexes) continue

        const indexesToCreate = tableIndexes.filter(
          (idx) => idx.name && !existingIndexNames.has(idx.name)
        )

        if (indexesToCreate.length > 0) {
          console.log(`🔧 为表 ${tableName} 创建 ${indexesToCreate.length} 个索引...`)

          for (const idx of indexesToCreate) {
            try {
              const queryInterface = sequelize.getQueryInterface()
              await queryInterface.addIndex(tableName, {
                fields: idx.fields,
                name: idx.name,
                unique: idx.unique || false,
                where: idx.where || undefined
              })
              console.log(`✅ 已创建索引: ${idx.name} (表: ${tableName})`)
              totalCreated++
            } catch (error: any) {
              console.warn(`⚠️ 创建索引 ${idx.name} 失败:`, error.message)
            }
          }
        }
      }

      console.log(
        `🎯 索引更新完成: 删除 ${orphanedIndexes.length} 个孤儿索引，创建 ${totalCreated} 个新索引`
      )
    } catch (error: any) {
      console.error('❌ 全局索引清理和更新失败:', error.message)
      throw error
    }
  }

  /**
   * 自动迁移入口：根据模型与旧表名，自动用新表名建表并迁移，支持 null 类型修改、类型修改等sqlite不支持的改动
   * @param sequelize Sequelize实例
   * @param model 表模型
   * @param oldTableName 旧表名
   * @returns 迁移结果
   */
  public static async autoMigrateTable(
    sequelize: Sequelize,
    model: any,
    oldTableName: string
  ): Promise<void> {
    const queryInterface = sequelize.getQueryInterface()

    const newTableName = `${oldTableName}_new_${Date.now()}`

    console.log(`开始为表 ${oldTableName} 创建新表 ${newTableName} 并迁移数据`)

    try {
      // 1. 创建新表结构（使用原生SQL，避免模型实例问题）
      await queryInterface.createTable(newTableName, model.getAttributes())
      console.log(`新表 ${newTableName} 创建完成`)

      // 2. 获取旧表的实际字段列表
      const oldTableColumns: Array<{
        cid: number
        name: string
        type: string
        notnull: number
        dflt_value: any
        pk: number
      }> = await sequelize.query(`PRAGMA table_info(${oldTableName})`, { type: QueryTypes.SELECT })

      const oldTableFieldNames = oldTableColumns.map((col) => col.name)
      console.log(`旧表 ${oldTableName} 现有字段: ${oldTableFieldNames.join(', ')}`)

      // 3. 获取新表模型定义的字段
      const newTableAttributes = model.getAttributes()
      const newTableFieldNames = Object.keys(newTableAttributes)
      console.log(`新表模型定义字段: ${newTableFieldNames.join(', ')}`)

      // 4. 找出旧表和新表都存在的字段（交集）
      const commonFields = oldTableFieldNames.filter((field) => newTableFieldNames.includes(field))
      console.log(`可迁移的公共字段: ${commonFields.join(', ')}`)

      if (commonFields.length === 0) {
        console.warn(`表 ${oldTableName} 没有可迁移的公共字段，跳过数据迁移`)
      } else {
        // 5. 构造字段列表字符串，只包含公共字段
        const fieldsList = commonFields.map((f) => `\`${f}\``).join(',')

        // 6. 过滤条件构造，只针对公共字段中的非空字段
        const notNullFields = Object.entries(newTableAttributes)
          .filter(([field, attr]: [string, any]) => {
            return commonFields.includes(field) && attr.allowNull === false
          })
          .map(([field]) => `\`${field}\` IS NOT NULL`)

        const whereClause = notNullFields.length > 0 ? `WHERE ${notNullFields.join(' AND ')}` : ''

        // 7. 执行数据复制，只复制公共字段
        const copySql = `INSERT INTO \`${newTableName}\` (${fieldsList}) SELECT ${fieldsList} FROM \`${oldTableName}\` ${whereClause}`
        console.log(`执行数据迁移 SQL: ${copySql}`)

        await sequelize.query(copySql)
        console.log(`数据迁移完成，已复制 ${commonFields.length} 个字段的符合条件数据`)
      }

      // 8. 删除旧表
      await queryInterface.dropTable(oldTableName)
      console.log(`旧表 ${oldTableName} 已删除`)

      // 9. 重命名新表为旧表名
      await sequelize.query(`ALTER TABLE \`${newTableName}\` RENAME TO \`${oldTableName}\``)
      console.log(`新表 ${newTableName} 重命名为 ${oldTableName}`)

      // 10. 重新添加索引（如果模型中定义了）
      if (model.options.indexes && model.options.indexes.length > 0) {
        for (const idx of model.options.indexes) {
          try {
            await queryInterface.addIndex(oldTableName, {
              fields: idx.fields,
              name: idx.name,
              unique: idx.unique || false,
              where: idx.where || undefined
            })
            console.log(`索引 ${idx.name} 已重新创建`)
          } catch (e) {
            console.warn(`索引 ${idx.name} 创建失败:`, e)
          }
        }
      }

      // 11. 重置对应的模型实例，强制重新初始化
      if (oldTableName === 'room_core_data') {
        this.roomCoreDataModel = null
        console.log('已重置 RoomCoreData 模型实例')
      } else if (oldTableName === 'orders') {
        this.orderModel = null
        console.log('已重置 Order 模型实例')
      } else if (oldTableName === 'products') {
        this.productModel = null
        console.log('已重置 Product 模型实例')
      }
    } catch (error: any) {
      console.error(`表 ${oldTableName} 自动迁移失败:`, error)
      // 清理可能的临时表
      try {
        const tables = await queryInterface.showAllTables()
        if (tables.includes(newTableName)) {
          await queryInterface.dropTable(newTableName)
          console.log(`已清理临时表 ${newTableName}`)
        }
      } catch (cleanupError) {
        console.warn('清理临时表失败:', cleanupError)
      }
      throw error
    }
  }

  /**
   * 关闭数据库连接
   * 释放连接池中的所有连接
   * @returns 关闭操作是否成功
   */
  public static async close(): Promise<boolean> {
    try {
      if (this.sequelizeInstance) {
        await this.sequelizeInstance.close()
        this.sequelizeInstance = null
        this.roomCoreDataModel = null
        this.orderModel = null
        this.productModel = null
        console.log('数据库连接已关闭')
        return true
      }
      return true
    } catch (error: any) {
      console.error('关闭数据库连接时出错:', error)
      return false
    }
  }

  /**
   * 获取数据库连接状态
   * @returns 数据库是否已连接
   */
  public static isConnected(): boolean {
    return this.sequelizeInstance !== null
  }

  /**
   * 测试数据库连接
   * @returns 连接测试是否成功
   */
  public static async testConnection(): Promise<boolean> {
    try {
      const sequelize = this.getSequelizeInstance()
      await sequelize.authenticate()
      console.log('数据库连接测试成功')
      return true
    } catch (error: any) {
      console.error('数据库连接测试失败:', error)
      return false
    }
  }
}

export default SqliteDB
