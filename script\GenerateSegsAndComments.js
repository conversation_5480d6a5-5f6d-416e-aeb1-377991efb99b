import { writeFileSync, existsSync, readFileSync } from 'fs'
import { join } from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
/**
 * 生成 segs.json 格式的数据
 * @param {number} n  生成多少个分段
 */
function generateSegsJson(n) {
  const examples = [
    { start: 3210, end: 6370, spk: 0, text: '弹幕没有开播啦，' },
    { start: 6430, end: 9965, spk: 0, text: '姐妹们开播啦。' }
  ]
  let arr = []
  for (let i = 0; i < n; i++) {
    let e = examples[i % examples.length]
    arr.push({
      start: e.start + i * 1000,
      end: e.end + i * 1000,
      spk: e.spk,
      text: e.text + `[${i + 1}]`
    })
  }
  return arr
}

/**
 * 生成 comments.json 格式的数据
 * @param {number} n 生成多少条评论
 */
function generateCommentsJson(n) {
  const payGradeIconUrl =
    'https://p3-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_22.png~tplv-obj.image'
  const fansClubBadgeUrl =
    'https://p3-webcast.douyinpic.com/img/webcast/fansclub_new_advanced_badge_11_xmp.png~tplv-obj.image'
  const names = ['陈琼助理晴晴', '🌟', '小张', '小王', '测试用户']
  let arr = []
  for (let i = 0; i < n; i++) {
    const noUser = i % 10 === 0
    let comment = {
      content: `评论文本${i + 1}`,
      event_ts: ********** + i,
      is_important: false,
      nick_name: names[i % names.length]
    }
    if (!noUser) {
      const hasPayGrade = Math.random() > 0.3
      const hasFansClub = Math.random() > 0.5
      let user = {}
      if (hasPayGrade) {
        user.PayGrade = {
          newImIconWithLevel: {
            urlListList: [payGradeIconUrl],
            height: '16',
            width: '32',
            imageType: 1
          }
        }
      }
      if (hasFansClub) {
        user.FansClub = {
          data: {
            badge: {
              icons: {
                4: {
                  urlListList: [
                    fansClubBadgeUrl,
                    'https://p11-webcast.douyinpic.com/img/webcast/fansclub_new_advanced_badge_11_xmp.png~tplv-obj.image'
                  ],
                  uri: 'webcast/fansclub_new_advanced_badge_11_xmp.png'
                }
              }
            }
          }
        }
      }
      if (hasPayGrade || hasFansClub) comment.user = user
    }
    arr.push(comment)
  }
  return arr
}

/**
 * 写入房间JSON对象属性工具
 * @param {string} id 房间ID
 * @param {string} propKey 写入的属性名("segs"/"comments")
 * @param {Array} propValue 属性值
 */
function writeToRoomFile(id, propKey, propValue) {
  // 构造文件路径
  const filepath = join(__dirname, '../cache', `room_${id}`)
  let obj = {}
  // 如果已存在，先读进来、再改属性
  if (existsSync(filepath)) {
    try {
      obj = JSON.parse(readFileSync(filepath, 'utf8'))
    } catch (e) {
      console.error(e)
      console.error(`文件已存在但JSON格式不对，将覆盖: ${filepath}`)
    }
  }
  obj[propKey] = propValue
  writeFileSync(filepath, JSON.stringify(obj, null, 2), 'utf8')
  console.log(`已写入${filepath} 的${propKey}，共${propValue.length}条`)
}

// 主流程
const count = 50000
const roomId = '7506488674100890368'

// 例1：写入comments
const comments = generateCommentsJson(count)
writeToRoomFile(roomId, 'comments', comments)

// 例2：写入segs
// const segs = generateSegsJson(count);
// writeToRoomFile(roomId, 'segs', segs);

// 调用示例：node genfiles.js
