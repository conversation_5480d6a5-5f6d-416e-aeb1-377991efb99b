# 图表拖动选择区间功能实现

## 功能描述

在趋势分析图表中支持鼠标拖动选择区间，以拖动开始点为基准，拖动范围作为最终选中区间。

## 用户交互流程

### 1. 拖动选择

- **开始**：用户在图表上按下鼠标并拖动
- **预览**：拖动过程中显示蓝色半透明的预览区域
- **确定**：松开鼠标后确定最终选中区间
- **范围**：不受时间跨度设置限制，完全按照拖动范围

### 2. 点击选择（保留）

- **行为**：单击图表仍然使用时间跨度逻辑
- **兼容**：拖动和点击可以无缝切换使用

## 技术实现

### 1. 状态管理

```typescript
const isDragging = ref<boolean>(false) // 是否正在拖动
const dragStartTime = ref<string | null>(null) // 拖动开始时间
const dragEndTime = ref<string | null>(null) // 拖动结束时间
const dragPreviewRange = ref<[string, string] | null>(null) // 预览区间
const dragStartPixel = ref<[number, number] | null>(null) // 开始像素位置
```

### 2. 事件处理

- **mousedown**: 记录开始位置和时间点
- **mousemove**: 实时更新预览区域（超过5像素阈值才启动拖动）
- **mouseup**: 确定最终选择或执行点击逻辑

### 3. 视觉反馈

- **拖动预览**: 蓝色半透明区域 `rgba(0, 123, 255, 0.2)`
- **正式选中**: 灰色半透明区域 `rgba(200, 200, 200, 0.3)`
- **边框效果**: 拖动预览带有蓝色边框

### 4. 智能判断

```typescript
const dragThreshold = 5 // 最小拖动距离阈值（像素）
```

- 拖动距离 < 5像素：按点击处理
- 拖动距离 ≥ 5像素：启动拖动模式

## 数据同步

### 1. 消息格式

拖动选择会发送额外的元数据：

```typescript
{
  realTimeData: currentTimeData,
  clickedTime: startTime,
  isDragSelection: true,  // 标识这是拖动选择
  dragRange: [actualStartTime, actualEndTime]  // 拖动范围
}
```

### 2. 通讯对象

- **实时指标组件**: 接收拖动开始点的数据
- **LinkAge组件**: 接收拖动开始点用于视频跳转
- **区间数据面板**: 显示整个拖动范围的统计数据

## 功能优势

### 1. 灵活性

- ✅ **任意长度**: 不受时间跨度限制，可拖动任意长度区间
- ✅ **精确控制**: 用户可以精确选择想要分析的时间段
- ✅ **双向拖动**: 支持从左到右或从右到左拖动

### 2. 用户体验

- ✅ **实时预览**: 拖动过程中实时显示选择区域
- ✅ **智能判断**: 自动区分点击和拖动操作
- ✅ **视觉清晰**: 不同状态有不同的视觉反馈

### 3. 兼容性

- ✅ **保持原功能**: 点击选择功能完全保留
- ✅ **消息兼容**: 复用现有的组件间通讯机制
- ✅ **状态同步**: 与其他组件保持数据同步

## 使用场景

### 1. 精确分析

- 用户想分析特定时间段（如30分钟）的数据
- 当前时间跨度设置为5分钟，无法满足需求
- 通过拖动可以精确选择30分钟区间

### 2. 快速对比

- 快速选择不同长度的时间段进行对比
- 不需要反复调整时间跨度设置

### 3. 数据探索

- 在图表上直观地选择感兴趣的区域
- 更直观的交互方式

## 相关文件

- `src/renderer/src/views/detail/trends/hooks/useTrendAnalysis.ts` - 核心逻辑实现

## 完成状态

✅ 功能实现完成
✅ 事件处理完整
✅ 视觉反馈清晰
✅ 兼容现有功能
✅ 等待测试验证
