# Excel数据导入功能实现

## 任务概述

为ProductTable组件添加Excel数据导入功能，支持用户通过上传Excel文件批量导入订单数据。

## 功能特性

### 📊 数据导入流程

1. **文件上传** - 支持拖拽上传Excel文件（.xlsx/.xls）
2. **数据解析** - 自动解析Excel内容并进行字段映射
3. **数据验证** - 实时验证数据完整性和格式正确性
4. **预览确认** - 展示解析结果供用户确认
5. **批量导入** - 调用后端API批量创建订单记录

### 🎯 技术架构

- **前端处理**: Excel解析、数据验证、用户界面
- **后端存储**: 批量数据入库、事务管理、错误处理
- **性能优化**: 分批处理（50条/批次）、进度反馈

## Excel模板规范

### 必填字段

| Excel列名 | 对应字段         | 格式说明       | 示例           |
| --------- | ---------------- | -------------- | -------------- |
| 订单ID    | order_id         | 文本，至少3位  | ORD20241201001 |
| 商品ID    | product_id       | 文本           | PROD001        |
| 商品名称  | product_name     | 文本           | 苹果手机       |
| 达人信息  | user_name        | 文本           | 张三           |
| 计佣金额  | total_pay_amount | 数值，单位：元 | 1299.00        |
| 订单状态  | order_status     | 枚举值         | PAY_SUCC       |

### 可选字段

| Excel列名 | 对应字段              | 格式说明            | 示例                |
| --------- | --------------------- | ------------------- | ------------------- |
| 支付时间  | pay_time              | YYYY-MM-DD HH:mm:ss | 2024-12-01 14:30:00 |
| 佣金率    | commission_rate       | 百分比，如21.00%    | 21.00               |
| 流量渠道  | media_type_group_name | 文本                | 直播                |
| 商品详情  | product_detail        | URL                 | https://...         |

### 订单状态枚举值

- `PAY_SUCC` - 支付成功
- `REFUND` - 退货退款
- `CONFIRM` - 已收货
- `SETTLE` - 已结算

## 使用方法

### 1. 准备Excel文件

按照上述模板规范准备Excel文件，确保：

- 第一行为列标题（严格按照Excel列名）
- 数据从第二行开始
- 必填字段不能为空
- 时间格式正确

### 2. 导入操作

1. 点击"导入数据"按钮
2. 拖拽或选择Excel文件
3. 系统自动解析并验证数据
4. 查看预览结果，确认无误后点击导入
5. 等待导入完成，查看结果统计

### 3. 错误处理

- 系统会自动检查数据格式和完整性
- 错误记录会被标记并显示具体错误信息
- 只有有效记录会被导入数据库
- 导入失败时支持事务回滚

## 技术实现

### 前端组件

- `ExcelImportModal.vue` - Excel导入模态框
- 使用 `xlsx` 库解析Excel文件
- Element Plus 组件构建UI界面
- 分步骤引导用户操作

### 后端接口

- IPC事件: `lzy:createOrders`
- 调用 `createOrder()` 函数批量处理
- 支持分批处理和事务管理
- 详细的错误统计和日志记录

### 性能优化

- 前端：分页预览、内存控制
- 后端：批量处理（50条/批次）、并行处理、事务优化
- 缓存：搜索结果缓存、LRU清理策略

## 注意事项

1. **文件大小限制**: 最大10MB
2. **数据量建议**: 单次导入不超过5000条记录
3. **格式要求**: 严格按照模板格式
4. **重复数据**: 系统会检查订单ID重复
5. **错误恢复**: 导入失败时数据会回滚，不会产生脏数据

## 错误代码说明

| 错误信息          | 原因               | 解决方案                          |
| ----------------- | ------------------ | --------------------------------- |
| 订单ID不能为空    | 必填字段缺失       | 补充订单ID                        |
| 计佣金额格式错误  | 金额格式不正确     | 使用数值格式，如：1299.00         |
| 支付时间格式错误  | 时间格式不符合要求 | 使用标准格式：YYYY-MM-DD HH:mm:ss |
| 订单ID长度至少3位 | 订单ID太短         | 使用完整的订单ID                  |

## 更新日志

### v1.0.0 (2024-12-01)

- ✅ 实现Excel文件上传和解析
- ✅ 添加数据验证和预览功能
- ✅ 集成批量数据导入接口
- ✅ 完善错误处理和用户反馈
- ✅ 支持导入成功后自动刷新表格
