# 图标显示问题修复

## 🔍 问题描述

风险分析页面(EnterView)上的MDI图标无法正常显示。

## 🔧 问题原因

项目使用了UnoCSS的图标预设，但在EnterView中使用了传统的MDI CSS类名语法，导致图标无法正确加载。

### 错误的语法

```html
<!-- ❌ 传统 MDI 语法 -->
<i class="mdi mdi-shield-alert-outline"></i>
<i class="mdi mdi-account-group"></i>
```

### 正确的语法

```html
<!-- ✅ UnoCSS 图标语法 -->
<i class="i-mdi-shield-alert-outline"></i>
<i class="i-mdi-account-group"></i>
```

## ✅ 修复方案

### 1. UnoCSS 图标预设配置

项目在 `uno.config.ts` 中已正确配置了MDI图标集：

```typescript
presetIcons({
  collections: {
    carbon: () => import('@iconify-json/carbon/icons.json').then((i) => i.default),
    mdi: () => import('@iconify-json/mdi/icons.json').then((i) => i.default)
  }
})
```

### 2. 图标类名修复

在 `src/renderer/src/views/enter/EnterView.vue` 中，已将所有MDI图标类名从传统语法修改为UnoCSS语法：

| 原类名                         | 修复后类名                   |
| ------------------------------ | ---------------------------- |
| `mdi mdi-shield-alert-outline` | `i-mdi-shield-alert-outline` |
| `mdi mdi-account-group`        | `i-mdi-account-group`        |
| `mdi mdi-login`                | `i-mdi-login`                |
| `mdi mdi-chart-line`           | `i-mdi-chart-line`           |
| `mdi mdi-alert-circle`         | `i-mdi-alert-circle`         |
| `mdi mdi-video-account`        | `i-mdi-video-account`        |
| `mdi mdi-video`                | `i-mdi-video`                |
| `mdi mdi-shopping`             | `i-mdi-shopping`             |
| `mdi mdi-trophy`               | `i-mdi-trophy`               |
| `mdi mdi-table-large`          | `i-mdi-table-large`          |
| `mdi mdi-filter`               | `i-mdi-filter`               |
| `mdi mdi-refresh`              | `i-mdi-refresh`              |
| `mdi mdi-check-circle`         | `i-mdi-check-circle`         |
| `mdi mdi-close-circle`         | `i-mdi-close-circle`         |
| `mdi mdi-eye`                  | `i-mdi-eye`                  |
| `mdi mdi-database-remove`      | `i-mdi-database-remove`      |

## 📋 UnoCSS 图标使用规范

### 语法格式

```
i-{collection}-{icon-name}
```

### 常用图标集合

- **MDI**: `i-mdi-icon-name`
- **Carbon**: `i-carbon-icon-name`
- **Tabler**: `i-tabler-icon-name`

### 示例用法

```html
<!-- MDI 图标 -->
<i class="i-mdi-home"></i>
<i class="i-mdi-user"></i>
<i class="i-mdi-settings"></i>

<!-- Carbon 图标 -->
<i class="i-carbon-home"></i>
<i class="i-carbon-user"></i>
<i class="i-carbon-settings"></i>

<!-- 图标样式 -->
<i class="i-mdi-home text-2xl text-blue-500"></i>
<i class="i-mdi-user w-6 h-6 text-green-600"></i>
```

## 🎯 验证方法

### 1. 开发工具检查

- 在浏览器开发者工具中检查图标元素
- 确认CSS类名正确生成
- 检查是否有控制台错误

### 2. 图标显示测试

访问风险分析页面，确认以下图标正常显示：

- ✅ 页面标题的盾牌图标
- ✅ 统计卡片的各种图标
- ✅ 用户特征分析的视频和购物图标
- ✅ TOP10排行榜的奖杯图标
- ✅ 表格操作的各种图标

### 3. 参考其他页面

项目中 `src/renderer/src/components/Versions.vue` 已正确使用UnoCSS图标语法，可作为参考：

```html
<!-- 正确示例 -->
<i class="i-mdi-nodejs text-2xl text-green-600"></i>
<i class="i-mdi-electron-framework text-2xl text-blue-600"></i>
```

## 🔄 后续注意事项

1. **新增图标时**：统一使用 `i-mdi-icon-name` 格式
2. **图标样式**：可结合UnoCSS工具类使用，如 `text-2xl`、`text-blue-500` 等
3. **图标查找**：可访问 [Iconify MDI图标库](https://icon-sets.iconify.design/mdi/) 查找可用图标
4. **类型安全**：在TypeScript环境中，UnoCSS图标预设提供更好的类型安全

## ✨ 修复效果

修复后，风险分析页面的所有图标应该能够正常显示，提供完整的视觉体验。页面的用户体验将得到显著改善。
