# 组件位置调换实现

## 任务描述

调换DetailView页面中LinkAge组件最右边的一分钟数据区域和趋势分析图右边的五分钟数据区域的位置，但不影响现有逻辑。

## 实现方案

采用组件级别拆分重组的方案，保持组件逻辑完全独立。

## 执行步骤

### 1. 修改LinkAge组件

- 文件：`src/renderer/src/views/detail/linkAge/LinkAge.vue`
- 操作：添加`showRightPanel`属性，控制右侧一分钟数据区域的显示
- 完成状态：✅

### 2. 修改TrendAnalysisPanel组件

- 文件：`src/renderer/src/views/detail/trends/TrendAnalysisPanel.vue`
- 操作：添加`showRightPanel`属性，控制右侧五分钟数据区域的显示
- 完成状态：✅

### 3. 创建TrendMetricArea组件

- 文件：`src/renderer/src/views/detail/trends/TrendMetricArea.vue`
- 操作：将TrendAnalysisPanel中的右侧数据区域提取为独立组件
- 完成状态：✅

### 4. 修改DetailView布局

- 文件：`src/renderer/src/views/detail/DetailView.vue`
- 操作：
  - LinkAge设置`showRightPanel=false`，并在其旁边添加TrendMetricArea组件
  - TrendAnalysisPanel设置`showRightPanel=false`并改为col-9，旁边添加col-3的RealTimeMetricGrid组件
  - 导入必要的组件
- 完成状态：✅

### 5. 类型错误修复

- 使用`as any`类型断言修复TypeScript类型检查错误
- 确保组件能正常渲染
- 完成状态：✅

## 最终效果

- LinkAge组件（上方）：字幕面板 + 视频播放器 + 五分钟数据区域（TrendMetricArea）
- 趋势分析图（下方）：一分钟数据区域（RealTimeMetricGrid，占3列，左侧）+ 图表区域（占9列，右侧）

## 问题修复记录

### 1. TrendMetricArea组件错误修复

- **问题**：`calculateCustomMetricsData is not a function`
- **原因**：useCustomMetrics返回的是computed对象，不是函数
- **解决**：修改为`calculateCustomMetricsData.value`访问computed值
- **状态**：✅ 已修复

### 2. 布局调整（第一次）

- **问题**：用户要求一分钟数据放到趋势分析图左侧，不是右侧
- **解决**：重新组织布局，一分钟数据在左，趋势图在右
- **状态**：🔄 需要进一步调整

### 3. 布局重新设计（第二次）

- **问题**：LinkAge应该保持一行三列，趋势分析图内部划分左右布局
- **解决**：
  - LinkAge保持：字幕面板 + 视频播放器 + 五分钟数据（TrendMetricArea）
  - TrendAnalysisPanel增加showLeftPanel属性，内部布局：一分钟数据（左）+ 图表（右）
- **状态**：✅ 已修复

## 保持的功能

- 所有原有交互逻辑保持不变
- 时间同步服务正常工作
- 数据处理逻辑完全独立
- 组件间通信机制保持原样

## 技术要点

- 通过props控制组件内部区域的显示/隐藏
- 提取共用组件避免代码重复
- 保持数据流和事件处理的完整性
- 使用类型断言解决TypeScript严格类型检查问题
