//万瑞

import API from '../module/API'
// import JsonDB from "../module/JsonDB";
// import * as fs from 'fs';
// import { getAPPBasePath } from "../module/API";
import { IpcMain } from 'electron'
import { getAllRoomCoreData } from './business/RoomService'

//创建类
export default class DemoService {
  /**
   * 注册 IPC 处理器
   * @param ipcMain Electron 的 IpcMain 实例
   */
  public static hook(ipcMain: IpcMain): void {
    function updateSummary(rooms: any, event: any): void {
      const roomList = API.filterRecentData(rooms, 0, 30)

      const roomList1 = API.filterRecentData(rooms, 30, 60)
      const s1 = API.calculateMetrics(roomList)
      const s2 = API.calculateMetrics(roomList1)
      event.reply('/baiying/rooms/summary', { s1: s1, s2: s2 })
    }
    ipcMain.on('refresh_rooms', async (event) => {
      // 使用核心数据表查询所有直播间数据
      const roomList = await getAllRoomCoreData()
      event.reply('/baiying/rooms/refresh', roomList)
      updateSummary(roomList, event)
    })
    // 注册获取房间列表处理器
    ipcMain.handle('demo:getRoomList', async () => {
      return await this.getRoomList()
    })
  }

  /**
   * 获取房间列表
   * @returns 房间数据列表
   */
  private static async getRoomList(): Promise<any[]> {
    try {
      const roomList = await getAllRoomCoreData()

      return roomList
    } catch (error) {
      console.error('获取房间列表失败:', error)
      return []
    }
  }
}
