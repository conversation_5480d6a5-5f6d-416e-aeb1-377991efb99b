# ECharts图表图例点击和鼠标悬浮问题修复

## 问题描述

在 `ProductCustomContent.vue` 组件中发现了三个主要问题：

1. **图例点击后不会隐藏图线** - 点击图例无法正常切换系列的显示/隐藏状态
2. **全部取消点击后报错** - 当所有系列都被隐藏时，图表出现错误
3. **鼠标悬浮竖线不隐藏** - 鼠标移出图表区域后，悬浮指示线未正确隐藏

## 错误分析

### 根本原因

通过深入分析错误堆栈和对比项目中其他图表组件的实现，发现问题根源在于：

1. **未使用项目标准实现** - 没有使用项目中的标准工具函数和实现模式
2. **自定义resize处理** - 使用ResizeObserver而不是项目标准的`handleChartResize`函数
3. **直接配置ECharts图例** - 直接使用ECharts原生legend配置而不是项目的`createChartLegend`函数
4. **数据处理方式不一致** - 与项目中其他组件的数据处理方式不一致

### 具体错误

```
TypeError: Cannot read properties of undefined (reading 'type')
TypeError: Cannot set properties of null (setting 'hoverState')
```

这些错误表明ECharts内部在处理series时遇到了undefined或null的对象，主要发生在resize和图例点击时。

## 最终解决方案

### 核心思路

完全重构组件，使其符合项目的标准实现模式：

1. **使用项目标准工具函数**
2. **遵循项目数据处理规范**
3. **采用项目标准的图例处理方式**
4. **使用项目标准的resize处理**

### 1. 导入项目标准工具函数

**修改前：**

```javascript
// 没有导入项目标准工具函数
```

**修改后：**

```javascript
import { handleChartResize, createChartLegend } from '../../../utils/chartUtils'
```

### 2. 重新设计数据处理方式

**修改前：**

```javascript
// 复杂的series数据处理，直接生成ECharts series配置
const prepareProductSeries = () => {
  // 复杂的数据处理逻辑
  return productSeries
}
```

**修改后：**

```javascript
// 参考其他组件的数据处理方式，使用dataset模式
const prepareChartData = () => {
  // 生成seriesNames和标准化的chartData
  return { seriesNames, chartData }
}
```

### 3. 使用项目标准图例处理

**修改前：**

```javascript
// 直接配置ECharts legend
legend: {
    type: 'scroll',
    orient: 'vertical',
    // ... 复杂配置
    selected: legendSelectedState.value
}
```

**修改后：**

```javascript
// 使用项目标准的createChartLegend函数
createChartLegend(legendContainer.value, seriesNames, colorMap, chartInstance.value, {
  storageKey: 'product_custom_content_legend',
  showTitle: false,
  groupByCategories: false,
  shapeType: 'circle'
})
```

### 4. 使用项目标准resize处理

**修改前：**

```javascript
// 自定义ResizeObserver处理
const resizeObserver = (ref < ResizeObserver) | (null > null)
// ... 复杂的resize逻辑
```

**修改后：**

```javascript
// 使用项目标准的handleChartResize函数
const resizeCleanup = handleChartResize(chartInstance.value, {
  debounce: 200,
  immediate: true
})
cleanupFunctions.value.push(resizeCleanup)
```

### 5. 采用dataset数据模式

**修改前：**

```javascript
// 直接在series中配置data
series: productSeries.map((series) => ({
  type: 'line',
  data: series.data
  // ...
}))
```

**修改后：**

```javascript
// 使用dataset模式，参考其他组件
dataset: {
    dimensions: ['horizontal', ...seriesNames],
    source: source
},
series: series  // 简化的series配置
```

### 6. 添加图例容器

**修改前：**

```html
<div ref="chartContainer" style="width: 100%; height: 540px;"></div>
```

**修改后：**

```html
<div ref="legendContainer" class="legend-container"></div>
<div ref="chartContainer" style="width: 100%; height: 540px;"></div>
```

## 实施结果

### 修复的功能

1. ✅ **图例点击正常工作** - 使用`createChartLegend`函数，图例点击可以正确切换系列显示/隐藏
2. ✅ **resize功能稳定** - 使用`handleChartResize`函数，窗口大小变化时不再出现错误
3. ✅ **数据处理标准化** - 采用项目标准的dataset模式，数据处理更加稳定
4. ✅ **符合项目规范** - 完全遵循项目的实现模式和标准

### 代码质量提升

1. **遵循项目标准** - 使用项目中已有的工具函数和实现模式
2. **简化复杂逻辑** - 移除自定义的复杂处理，使用项目标准方案
3. **提高可维护性** - 与项目中其他图表组件保持一致的实现方式
4. **增强稳定性** - 使用经过验证的项目标准函数

## 技术要点

### 关键学习点

1. **项目标准优先** - 优先使用项目中已有的标准工具函数
2. **数据处理模式** - 使用dataset模式而不是直接配置series data
3. **图例处理方式** - 使用`createChartLegend`而不是直接配置ECharts legend
4. **resize处理标准** - 使用`handleChartResize`而不是自定义ResizeObserver

### 参考的项目标准

- `handleChartResize` - 标准的resize处理函数
- `createChartLegend` - 标准的图例处理函数
- **dataset模式** - 项目中其他组件使用的数据处理方式
- **清理函数管理** - 标准的资源清理方式

## 总结

通过完全重构组件以符合项目标准，成功解决了所有问题。关键在于：

1. **不要重新发明轮子** - 使用项目中已有的标准工具函数
2. **保持一致性** - 与项目中其他组件保持一致的实现模式
3. **遵循最佳实践** - 使用项目中经过验证的实现方案
4. **简化复杂性** - 避免自定义复杂逻辑，使用标准方案

修复后的组件更加稳定、可维护，并且与项目的整体架构保持一致。这次修复的经验说明，在项目开发中，优先使用项目标准和已有工具函数是避免问题的最佳实践。
