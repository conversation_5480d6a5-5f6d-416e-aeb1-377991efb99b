# TimeDivider和SubtitleEditModal暗黑主题兼容性修复

## 问题描述

- TimeDivider.vue 和 SubtitleEditModal.vue 组件使用了大量硬编码颜色
- 这些硬编码颜色覆盖了Bootstrap的暗黑主题，导致暗黑模式下显示异常
- 组件在暗黑模式下背景和文字颜色不协调

## 问题根因

1. **硬编码颜色值**：

   - 使用固定的十六进制颜色码（如 `#666`, `#f8f9fa`, `#fff`等）
   - 这些颜色在暗黑模式下不会自动适配

2. **样式覆盖**：
   - 组件自定义样式覆盖了Bootstrap的CSS变量系统
   - 滚动条等UI元素也使用了固定颜色

## 解决方案

使用Bootstrap CSS变量替换所有硬编码颜色，实现自动暗黑主题适配

### TimeDivider.vue 修改内容

1. **背景和边框**：

   - `#f8f9fa` → `var(--bs-body-bg)`
   - `#e9ecef` → `var(--bs-border-color)`
   - `#ddd` → `var(--bs-border-color)`

2. **文字颜色**：

   - `#666` → `var(--bs-secondary-color)`
   - `#444` → `var(--bs-emphasis-color)`
   - `#333` → `var(--bs-emphasis-color)`

3. **品牌色**：

   - `#0d6efd` → `var(--bs-primary)`
   - `#ffc107` → `var(--bs-warning)`

4. **背景色**：
   - `rgba(255, 255, 255, 0.7)` → `var(--bs-light)`
   - `rgba(13, 110, 253, 0.1)` → `rgba(var(--bs-primary-rgb), 0.1)`

### SubtitleEditModal.vue 修改内容

1. **模态框背景**：

   - `white` → `var(--bs-body-bg)`

2. **内容区域**：

   - `#f8f9fa` → `var(--bs-light)`
   - `#fff` → `var(--bs-body-bg)`

3. **文字和边框**：

   - `#495057` → `var(--bs-body-color)`
   - `#7367f0` → `var(--bs-primary)`
   - `#e0e0e0` → `var(--bs-border-color)`
   - `#d0d5dd` → `var(--bs-border-color)`

4. **表单元素**：

   - 添加 `background-color: var(--bs-body-bg)`
   - 添加 `color: var(--bs-body-color)`

5. **滚动条样式**：

   - `#f1f1f1` → `var(--bs-light)`
   - `#c0c0c0` → `var(--bs-secondary)`
   - `#a0a0a0` → `var(--bs-dark)`

6. **焦点状态**：
   - `rgba(115, 103, 240, 0.1)` → `rgba(var(--bs-primary-rgb), 0.1)`

## 修改文件

- `src/renderer/src/views/detail/linkAge/components/TimeDivider.vue`
- `src/renderer/src/views/detail/linkAge/components/SubtitleEditModal.vue`

## 技术要点

- 使用Bootstrap 5的CSS变量系统
- 保持梯度色彩（如header渐变）不变，因为它们是装饰性的
- 确保所有交互元素在两种模式下都清晰可见
- 滚动条样式也适配暗黑主题

## 预期效果

- 组件在亮色模式下保持原有视觉效果
- 组件在暗黑模式下自动适配深色背景和浅色文字
- 所有UI元素在两种模式下都具有良好的对比度和可读性
- 与系统整体暗黑主题保持一致

## 验证要点

1. 在亮色模式下检查组件显示是否正常
2. 切换到暗黑模式，检查组件是否正确适配
3. 测试文字对比度和可读性
4. 验证交互元素（输入框、按钮等）的可用性
