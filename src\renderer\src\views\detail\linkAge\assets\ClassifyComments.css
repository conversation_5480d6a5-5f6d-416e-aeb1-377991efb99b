.modal-content {
    width: 90vw !important;
    border-radius: 6px;
  }
  
  .fan-badge {
    height: 20px;
    border-radius: 50%;
    margin-left: 4px;
  }
  
  /* 评论列表样式 */
  .comment-list-container {
    max-height: 450px;
    overflow-y: auto;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    background-color: #fff;
  }
  
  .comment-list-header {
    position: sticky;
    top: 0;
    z-index: 1;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .comment-item {
    transition: background-color 0.2s ease;
    align-items: center;
  }
  
  .comment-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
  }
  
  .comment-item:last-child {
    border-bottom: none !important;
  }
  
  .comment-content {
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  .modal-body {
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }
  
    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 3px;
    }
  
    &::-webkit-scrollbar-thumb {
      background: #a3a6ad;
      opacity: 0.3;
      border-radius: 3px;
    }
  
    &::-webkit-scrollbar-thumb:hover {
      background: #7a7d84;
      opacity: 0.5;
    }
  }
  
  .comment-list-container {
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }
  
    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 3px;
    }
  
    &::-webkit-scrollbar-thumb {
      background: #a3a6ad;
      opacity: 0.3;
      border-radius: 3px;
    }
  
    &::-webkit-scrollbar-thumb:hover {
      background: #7a7d84;
      opacity: 0.5;
    }
  }
  
  /* 添加类别标签的样式 */
  .bg-info-subtle {
    background-color: rgba(13, 202, 240, 0.1);
  }
  
  .bg-warning-subtle {
    background-color: rgba(255, 193, 7, 0.1);
  }
  
  .bg-success-subtle {
    background-color: rgba(25, 135, 84, 0.1);
  }
  
  .bg-danger-subtle {
    background-color: rgba(220, 53, 69, 0.1);
  }
  
  .bg-secondary-subtle {
    background-color: rgba(108, 117, 125, 0.1);
  }
  
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1060;
  }
  
  /* 添加鼠标指针样式 */
  .cursor-pointer {
    cursor: pointer;
  }
  
  /* 添加选中效果 */
  .badge {
    transition: all 0.2s ease;
    font-weight: normal;
    display: inline-block;
    border-radius: 0.25rem;
  }
  
  /* 筛选标签样式 */
  .badge-filter {
    user-select: none;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    padding: 0.25rem 0.5rem;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
    display: inline-block;
    border-radius: 0.25rem;
    background-color: transparent;
    font-size: 0.875rem;
    line-height: 1.5;
    text-align: center;
    vertical-align: middle;
    border: 0;
    outline: none !important;
  }
  
  .badge-filter:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .badge-filter:active {
    transform: translateY(0);
  }
  
  .badge-filter:focus {
    outline: none;
    box-shadow: none;
  }
  
  /* 分类样式 */
  .bg-info {
    background-color: #0dcaf0 !important;
    color: #fff !important;
  }
  
  .bg-warning {
    background-color: #ffc107 !important;
    color: #fff !important;
  }
  
  .bg-success {
    background-color: #198754 !important;
    color: #fff !important;
  }
  
  .bg-danger {
    background-color: #dc3545 !important;
    color: #fff !important;
  }
  
  .bg-secondary {
    background-color: #6c757d !important;
    color: #fff !important;
  }
  
  .text-info {
    color: #0dcaf0 !important;
  }
  
  .text-warning {
    color: #ffc107 !important;
  }
  
  .text-success {
    color: #198754 !important;
  }
  
  .text-danger {
    color: #dc3545 !important;
  }
  
  .text-secondary {
    color: #6c757d !important;
  }
  
  /* 只去掉select的下拉箭头，保持其他样式 */
  .form-select-sm {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: none !important;
    padding-right: 0.375rem !important;
    width: auto !important;
    min-width: fit-content;
    max-width: 100%;
  }