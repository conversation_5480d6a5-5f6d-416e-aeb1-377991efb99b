# DashboardView重构拆分任务

## 背景

当前的DashboardView.vue是一个庞大的单文件组件（667行），包含了登录逻辑、Header、布局、业务逻辑等多个职责，与新的组件架构（MainLayout、Header、Sidebar）不符，需要进行拆分重构。

## 目标

将复杂的DashboardView.vue重构为符合新架构的组件结构，提升代码可维护性。

## 重构计划

### 1. 分析现有业务逻辑

- ✅ 登录相关逻辑 (模态框、验证码、子账号选择)
- ✅ 直播监控逻辑 (开始/停止监控、状态检测)
- ✅ 用户状态管理 (百应登录状态、用户信息)
- ✅ 数据获取和显示 (房间列表、汇总数据)
- ✅ 主题切换逻辑
- ✅ IPC事件监听

### 2. 创建组件和工具

- [ ] `components/modals/LoginModal.vue` - 登录模态框
- [ ] `components/modals/LiveMonitorModal.vue` - 直播监控模态框
- [ ] `composables/useAuth.ts` - 认证相关逻辑
- [ ] `composables/useAppState.ts` - 应用状态管理
- [ ] `composables/useIpcEvents.ts` - IPC事件管理

### 3. 重构现有组件

- [ ] 重构DashboardView.vue为纯业务组件
- [ ] 增强Header.vue组件功能

## 执行时间

开始时间：2024年当前时间
预计完成时间：1-2小时

## 技术要点

- 使用Vue3 Composition API
- 遵循单一职责原则
- 保持业务逻辑连续性
- 确保IPC事件正确迁移
