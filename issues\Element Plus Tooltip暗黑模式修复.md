# Element Plus Tooltip暗黑模式修复

## 问题描述

- Element Plus的tooltip组件在暗黑模式下显示异常
- tooltip背景色和文字颜色不符合暗黑主题
- 其他Element Plus组件的暗黑模式可能也受影响

## 问题根因

1. **CSS选择器不匹配**：

   - Element Plus暗黑模式需要 `html.dark` 类来触发CSS变量
   - 项目中的element-ui-theme.css只定义了 `[data-bs-theme=dark]` 选择器
   - Element Plus组件无法读取到正确的暗黑模式变量

2. **样式优先级问题**：

   - 自定义的Element UI主题配置覆盖了Element Plus默认的暗黑模式
   - Element Plus的 `'element-plus/theme-chalk/dark/css-vars.css'` 被覆盖

3. **配置不完整**：
   - 虽然App.vue中useDark正确设置了html.dark类
   - 但Element UI主题配置文件没有为html.dark定义相应的CSS变量

## 解决方案

在element-ui-theme.css中添加html.dark选择器，确保Element Plus组件能正确读取暗黑模式变量

### 修改内容

将暗黑模式选择器从：

```css
[data-bs-theme='dark'] {
  /* Element Plus变量 */
}
```

修改为：

```css
[data-bs-theme='dark'],
html.dark {
  /* Element Plus变量 */
}
```

这样确保：

1. Bootstrap组件使用 `[data-bs-theme=dark]`
2. Element Plus组件使用 `html.dark`
3. 两种选择器使用相同的CSS变量配置

## 修改文件

- `src/renderer/src/assets/element-ui-theme.css`

## 技术要点

- Element Plus暗黑模式依赖 `html.dark` 类名
- Bootstrap暗黑模式依赖 `data-bs-theme="dark"` 属性
- 项目需要同时支持两种暗黑模式机制
- 通过CSS选择器组合，可以让两套组件库共享相同的暗黑主题变量

## 预期效果

- Element Plus tooltip在暗黑模式下正确显示深色背景和浅色文字
- 所有Element Plus组件都能正确适配暗黑主题
- Bootstrap组件继续正常工作
- 两套组件库的暗黑主题保持一致

## 验证要点

1. 切换到暗黑模式，检查tooltip显示是否正常
2. 测试其他Element Plus组件（按钮、输入框、对话框等）
3. 确认Bootstrap组件不受影响
4. 验证主题切换的流畅性

## 根本原因分析

这个问题的根本原因是项目同时使用了两套UI组件库（Bootstrap和Element Plus），它们各自有不同的暗黑模式实现方案：

- Bootstrap 5: 使用 `data-bs-theme` 属性
- Element Plus: 使用 `html.dark` 类名

项目需要确保两套方案都能正常工作，因此需要在CSS配置中同时支持两种选择器。
